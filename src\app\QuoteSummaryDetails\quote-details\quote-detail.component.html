<admin-header> </admin-header>

<section class="header-section margin-top-section">
    <div class="container">
        <form class="k-form form-default p-0" ngNativeValidate #form="ngForm" (ngSubmit)="updateQuotes(form.value)">
            <div class="row" style="margin-bottom: -4px;">
                <div class="col-md-4 col-6">
                    <label class="k-form-field">
                        <span class="font-default">
                            Quote Reference
                        </span>
                        <input type="text" name="QuoteReference" class="k-textbox" [value]='list.QuoteReference'
                            readonly required />
                    </label>
                </div>
                <div class="col-md-4 col-6">
                    <label class="k-form-field div-center">
                        <span class="font-default">
                            Work Order
                            <span class="k-required">*</span></span>
                        <div *ngIf='list.QuoteStatus!="APPROVED"'>
                            <input type="text" name="WorkOrderNo" class="k-textbox" [(ngModel)]='list.WorkOrderNo'
                                required />
                        </div>
                        <div *ngIf='list.QuoteStatus==="APPROVED"'> <input type="text" name="WorkOrderNo"
                                class="k-textbox" [value]="list.WorkOrderNo" readonly required />
                        </div>
                        <span style="position: relative;bottom: 11px;" class="help-block text-danger"
                            *ngIf='!form.valid && form.touched'>Work Order is Required !
                        </span>
                    </label>
                </div>
                <div class="col-md-3 col-12 md-lg">
                    <button *ngIf="IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="You are unable to approve this quote as your account is currently on hold. Please contact your account manager for further details" disabled><b>APPROVE</b></button>
                    <button *ngIf="!IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>APPROVE</b></button>
                    <button *ngIf="IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>APPROVE</b></button>
                    <button *ngIf="!IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin"
                        [disabled]='list.QuoteStatus==="APPROVED" || !allowToApprove'><b>APPROVE</b></button>
                </div>
                <div class="col-md-1 col-12 md-lg">
                    <button *ngIf="IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="You are unable to decline this quote as your account is currently on hold. Please contact your account manager for further details" disabled><b>DECLINE</b></button>
                    <button *ngIf="!IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>DECLINE</b></button>
                    <button *ngIf="IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary button-top-margin" style="background-color: lightgray !important;
                    pointer-events: auto !important; cursor: no-drop !important;"
                    title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>DECLINE</b></button>
                    <button *ngIf="!IsRestricted && !IsExpiredQuote" type="button" class="k-button k-primary button-top-margin"
                        [disabled]='list.QuoteStatus==="APPROVED" || !allowToApprove' (click)="declineQuoteShow()"><b>DECLINE</b></button>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 col-6">
                    <label class="k-form-field">
                        <span style="font-family: calibri light , Roboto;
                        font-weight: 700;" class="font-default">
                            Status
                        </span>
                        <input type="text" name="WorkOrderNo" class="k-textbox" [value]='list.QuoteStatus' readonly
                            required />
                    </label>
                </div>
                <div class="col-md-4 col-6">
                    <label class="k-form-field div-center" *ngIf="list.QuoteStatus==='APPROVED'">
                        <span class="font-default">
                            Date Approved
                        </span>
                        <input type="text" name="WorkOrderNo" class="k-textbox" readonly [value]='convertDateToString()'
                            required />
                    </label>
                    <label class="k-form-field div-center" *ngIf='list.QuoteStatus!="APPROVED"'>
                        <div class="form-group">
                            <span class="font-default"> Select Priority <span class="k-required">*</span></span>
                            <kendo-dropdownlist class="priorityCss" style="position: relative;
                            bottom: 8px;" [data]="listItems" [textField]="'PriorityLevel'" [valueField]="'PriorityID'"
                                [valuePrimitive]="true" [(ngModel)]="list.PriorityCd" #State="ngModel" name="States"
                                required>
                            </kendo-dropdownlist>
                        </div>
                    </label>
                </div>
                <div class="col-md-4 col-12 md-lg">
                    <button type="button" class="k-button k-primary button-top-margin" (click)="getMainReport()">
                        <b>Download Document</b></button>
                </div>
                <div class="col-12 md-hide">
                        <button *ngIf="IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="You are unable to approve this quote as your account is currently on hold. Please contact your account manager for further details" disabled><b>APPROVE</b></button>
                        <button *ngIf="!IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>APPROVE</b></button>
                        <button *ngIf="IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>APPROVE</b></button>
                        <button *ngIf="!IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary" 
                        [disabled]='list.QuoteStatus==="APPROVED" || !allowToApprove'><b>APPROVE</b></button>
                        &nbsp;

                        <button *ngIf="IsRestricted && !IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="You are unable to decline this quote as your account is currently on hold. Please contact your account manager for further details" disabled><b>DECLINE</b></button>
                        <button *ngIf="!IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>DECLINE</b></button>
                        <button *ngIf="IsRestricted && IsExpiredQuote" type="submit" class="k-button k-primary" style="background-color: lightgray !important;
                        pointer-events: auto !important; cursor: no-drop !important;" title="The Validity of this quote has EXPIRED. Please contact your account manager for a current quote" disabled><b>DECLINE</b></button>
                        <button *ngIf="!IsRestricted && !IsExpiredQuote" type="button" class="k-button k-primary" 
                        [disabled]='list.QuoteStatus==="APPROVED" || !allowToApprove' (click)="declineQuoteShow()"><b>DECLINE</b></button>
                        &nbsp;
                        <button type="button" class="k-button k-primary" (click)="getMainReport()">
                                <b>Download Document</b></button>
                                
                </div>
            </div>
            <kendo-dialog title="Exceeded the validity date" *ngIf="isActivationDate" (close)="isActivationDate=false" [width]="560">
                <p style="font-size: 16px; font-family:calibri light , Roboto; padding-top: 15px;">
                    Please note the quote you are approving has exceeded its validity date,<br> 
                    please contact your Account Manager for a revised quotation. <br />
                    
                </p>
                <kendo-dialog-actions>
                    <button type="button" kendoButton (click)="isActivationDate = false" primary="true" style="width: 29% !important;    width: 29% !important;
                    background: #4ab4de !important;
                    color: white;margin-right: 202px">Close</button>
                </kendo-dialog-actions>
            
            </kendo-dialog>
        </form>
    </div>

    <kendo-dialog *ngIf="showDeclinePopup" (close)="closeDeclinePopup()" [minWidth]="300">
        <kendo-dialog-titlebar>
          <div style="font-size: 18px; line-height: 1.3em;">
             Decline Quote
          </div>
        </kendo-dialog-titlebar>
        <form [formGroup]="quoteDeclineForm"  (ngSubmit)="declineQoute()">
        <div class="row example-wrapper">
            <div class="col-xs-6 col-sm-6 example-col" style="margin-top: 15px;">
              <b>Reason</b><br/>
              <kendo-dropdownlist [data]="declineReasonData" [valueField]="'CodeID'" [textField]="'Description'" 
              [ngClass]="{ 'ng-invalid ng-touched': submitted && f.reason.errors }" class="declineReason-dropdown"
              name="reason" formControlName="reason" [(ngModel)]="decline.Reason">
              </kendo-dropdownlist>
              <div *ngIf="submitted && f.reason.errors" class="k-tooltip-validation">
                <div *ngIf="f.reason.errors.required" class="text-danger" style="margin-top: 5px;">Reason is required !</div>
              </div>
              <!-- <span style="position: absolute; left: 17px; top: 60px;" class="help-block text-danger" *ngIf="!quoteDeclineForm.controls['reason'].valid && quoteDeclineForm.controls['reason']?.touched">Reason is require !</span> -->
            </div>  
            <div class="col-xs-6 col-sm-6 example-col" style="margin-top: 15px;">
                <b>Description</b><br />
                <textarea kendoTextArea class="declineTextArea" [(ngModel)]="decline.Description" name="description" formControlName="description"></textarea>
              </div>    
          </div>   
          
        <kendo-dialog-actions>            
            <div class="container-fluid" style="padding: 0px 12px;">
                <hr />
                 <div style="float: right;">
                    <button class="k-button k-primary buttonFooterCancel" (click)="closeDeclinePopup()">Cancel</button>
                    &nbsp;
                    <button type="submit" class="k-button k-primary buttonFooter"><b>Submit</b></button>
                </div>
            </div>
        </kendo-dialog-actions>
        </form>
    </kendo-dialog>

</section>

<section  style="margin-top: -4px;margin-bottom: 0%; ">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <kendo-grid [data]="gridData" style="cursor: pointer"
                    [selectable]="{enabled: true, checkboxOnly: true }">
                     <kendo-grid-checkbox-column showSelectAll="true" width="150">
                        <ng-template kendoGridHeaderTemplate>
                            <input id="selectAllCheckboxId" kendoGridSelectAllCheckbox
                                [state]="selectAllState" (selectAllChange)="onSelectAllChange($event)">
                            <label class="k-checkbox-label" for="selectAllCheckboxId"></label>
                        </ng-template>
                        <ng-template kendoGridCellTemplate let-idx="rowIndex">

                            <input [kendoGridSelectionCheckbox]="idx" (change)="UpdateDetail($event,idx)"
                                style="    margin-left: 5px;" name="checkbox" />
                        </ng-template>
                    </kendo-grid-checkbox-column>

                    <kendo-grid-column field="Preferred" title="Prefered" width="150">
                        <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="k-grid-ignore-click">
                                {{ dataItem.Preferred?'Yes':'No'}}
                            </div>
                        </ng-template>
                    </kendo-grid-column>
                    <kendo-grid-column field="Title" title="Title" width="180">
                    </kendo-grid-column>
                    <kendo-grid-column field="Equipment" title="Equipment" width="150">
                    </kendo-grid-column>
                    <kendo-grid-column field="PriceTotal" title="Price" filter="numeric" width="100">
                        <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="k-grid-ignore-click" style="text-align: right">
                                ${{ dataItem.PriceTotal }}
                            </div>

                        </ng-template>
                    </kendo-grid-column>

                </kendo-grid>

            </div>
        </div>
    </div>
</section>

<section class="margin-auto">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <form class="k-form">
                    <label class="k-form-field">
                        <span class="font-default"> Introduction</span>

                        <div #intro class="introduction p-3">

                        </div>
                    </label>

                    <label class="k-form-field">
                        <span class="font-default">Scope of Work</span>

                        <div #sow class="p-3">

                        </div>

                    </label>

                    <label class="k-form-field">
                        <span class="font-default">
                            Exclusions
                        </span>
                        <div #exclusions class="p-3">

                        </div>

                    </label>
                </form>
            </div>

        </div>
    </div>
</section>
<admin-footer class="quoteDetailFooter"></admin-footer>