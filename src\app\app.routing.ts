import { Routes, RouterModule } from "@angular/router";
import { LoginComponent } from "./Common/login/login.component";
import {
  AuthGuardService,
  LoginAuthGuardService,
  MFAGuardService,
} from "./Services/auth-guard.service";
import { QuoteDetailComponent } from "./QuoteSummaryDetails/quote-details/quote-detail.component";
import { DashboardComponent } from "./Dashboard/dashboard.component";
import { OutstandingQuotes } from "./QuoteSummaryDetails/outstanding-quotes/outstanding-quotes.component";
import { OpenCallsComponent } from "./CallSummaryDetails/open-calls/open-calls.component";
import { OpenJCComponent } from "./CallSummaryDetails/open-jc/open-jc.component";
import { AttendComponent } from "./CallSummaryDetails/attend/attend.component";
import { PlaceCallComponent } from "./ServiceJobDetails/place-call/place-call.component";
import { OpenDebtComponent } from "./CallSummaryDetails/open-debt/open-debt.component";
import { SubContractorComponent } from "./CallSummaryDetails/sub-contractor/sub-contractor.component";
import { MOMComponent } from "../app/MoM/mom/mom.component";
import { MonthlyReportComponent } from "../app/Reports/monthly-report/monthly-report.component";
import { GenerateBulkReportComponent } from "./Reports/generate-bulk-report/generate-bulk-report.component";
import { ComplianceReportComponent } from "./Reports/compliance-report/compliance-report.component";
import { MomListComponent } from "./MoM/mom-list/mom-list.component";
import { UserInformationComponent } from "../app/Users/<USER>/userInformation.component";
import { ClientPerceptionReportComponent } from "../app/clientPerceptionReport/clientPerceptionReport.component";
import { DocumentationComponent } from "./Reports/Documentation/documentation.component";
import { VerificationComponent } from "./Common/verification/verification.component";
import { FTDashboardComponent } from "./FT-Dashboard/ft-dashboard.component";
import { MonthlyIMReportComponent } from "./Reports/monthly-im-report/monthly-im-report.component";
import { IAQComponent } from "./IndoorAirQuality/iaq.component";


const APP_ROUTES: Routes = [
  {
    path: "",
    component: LoginComponent,
    pathMatch: "full",
    canActivate: [AuthGuardService],
    data: { title: "Log in - Airmaster Flow" },
  },
  {
    path: "Login",
    redirectTo: "",
    data: { title: "Airmaster Flow - Log in" },
  },
  {
    path: "Login/:flowT",
    component: LoginComponent,
    canActivate: [LoginAuthGuardService],
    data: { title: "Airmaster Flow - Log in" },
  },
  {
    path: "TwoFactorAuthentication",
    component: VerificationComponent,
    canActivate: [MFAGuardService],
  },

 
  {
    path: "QuoteDetail",
    component: QuoteDetailComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Quote Details" },
  },
  {
    path: "Quotes/:id/:value",
    component: OutstandingQuotes,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Quotes" },
  },
  {
    path: "Dashboard",
    component: DashboardComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Dashboard" },
  },
  {
    path: "ServiceCalls/:value",
    component: OpenCallsComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Service Calls" },
  },
  {
    path: "PPMCompletion/:value",
    component: AttendComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - PPM Completion %" },
  },
  {
    path: "QuotesInProgress/:value",
    component: OpenJCComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Quotes In Progress" },
  },
  {
    path: "PlaceCall",
    component: PlaceCallComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Place a Call" },
  },
  {
    path: "MoMDetail",
    component: MOMComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Create MoM" },
  },
  {
    path: "MoM",
    component: MomListComponent,

    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Meeting List" },
  },

  {
    path: "MoMDetail/:id",
    component: MOMComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Edit MoM" },
  },

  {
    path: "OpenDebt/:value",
    component: OpenDebtComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Debt" },
  },
  {
    path: "SubContractor",
    component: SubContractorComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Sub Contractor" },
  },
  {
    path: "MonthlyReport",
    component: MonthlyReportComponent,

    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Monthly Report" },
  },
  {
    path: "MonthlyIMReport",
    component: MonthlyIMReportComponent,

    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Monthly IM Report" },
  },
  {
    path: "followUpMeeting/:id/:value",
    component: MOMComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Follow Up Meeting" },
  },
  {
    path: "generateBulkReport",
    component: GenerateBulkReportComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Bulk Documents" },
  },

  {
    path: "complianceReport",
    component: ComplianceReportComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Compliance Report" },
  },
  {
    path: "documentation",
    component: DocumentationComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Documentation" },
  },
  {
    path: "userInfo",
    component: UserInformationComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - User Information " },
  },
  {
    path: "clientPerceptionReport",
    component: ClientPerceptionReportComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - Client Perception Report" },
  },
  {
    path: "IM-Dashboard",
    component: FTDashboardComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - IM Interface" },
  },
  {
    path: "id_token",
    redirectTo: window.location.origin + "/#/data",
  },
  {
    path: "IM-Dashboard/IAQ",
    component: IAQComponent,
    canActivate: [AuthGuardService],
    data: { title: "Airmaster Flow - IAQ"}
  }
];
export const routing = RouterModule.forRoot(APP_ROUTES);
