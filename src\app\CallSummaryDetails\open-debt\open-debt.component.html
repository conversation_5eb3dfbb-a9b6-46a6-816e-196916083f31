<admin-header (valueChange)='locationChange($event)'></admin-header>
<section class="margin-top-table-list">
  <div [ngStyle]="{'height.px': pageHeight}">
  <kendo-grid class="gridFontStyle" [data]="gridData" [skip]="state.skip" [sort]="state.sort" [filter]="state.filter" [style.height]="'100%'"
    [sortable]="{allowUnsort: true, mode:'multiple'}" filterable="menu" (filterChange)="filterChange($event)"
    (dataStateChange)="dataStateChange($event)" (scrollBottom)="loadMore()" [navigable]="true" [loading]="loading"
    [selectable]="selectableSettings"
    [kendoGridSelectBy]="'InvoiceRef'"
    [selectedKeys]="mySelection"
    (selectedKeysChange)="onSelectedKeysChange($event)">
    <ng-template style="text-align: right;" kendoGridToolbarTemplate>
      <div class="container-fluid p-0">
        <div class="row">

          <div class="col-md-7 col-5" style="display: flex;">
             <!-- <h1 *ngIf="showOpenDebtTitle" (click)='openDebtclick($event)'>Open Debt</h1> -->
             <button *ngIf="selectedCheckbox.length > 0" kendoButton class="downloadButton" style="background-color: #00ccff !important; font-size: 11px !important;"
              primary="true" (click)="DownloadAllDoc()">DownLoad</button>
          </div>

          <div class="col-md-5 col-7 text-right">
            <button type="button" [disabled]="openDebtList.length===0" class="ExportToExcelButtonCss"
              kendoGridExcelCommand icon="file-excel">
              <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
              Export to
              Excel</button>
          </div>
        </div>
      </div>
    </ng-template>
    <!-- <kendo-grid-column field="Location" title="Location" width="160">
      <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
        <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
          operator="startswith">
          <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
          <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          <kendo-filter-eq-operator></kendo-filter-eq-operator>
          <kendo-filter-neq-operator></kendo-filter-neq-operator>
          <kendo-filter-contains-operator></kendo-filter-contains-operator>
        </kendo-grid-string-filter-menu>
      </ng-template>
    </kendo-grid-column> -->

    <kendo-grid-checkbox-column width="80">
      <ng-template kendoGridHeaderTemplate>
        <input id="selectAllCheckboxId" kendoGridSelectAllCheckbox
          [state]="selectAllState" (selectAllChange)="onSelectAllChange($event)">
        <label class="k-checkbox-label" for="selectAllCheckboxId"></label>
      </ng-template>
      <ng-template kendoGridCellTemplate let-idx="rowIndex">
        <input [kendoGridSelectionCheckbox]="idx" name="checkbox" />
    </ng-template>
    </kendo-grid-checkbox-column>

    <kendo-grid-column field="LocationName" title="Location Name" width="210">

    </kendo-grid-column>
    <kendo-grid-column field="InvoiceRef" title="Invoice Ref" width="170">

    </kendo-grid-column>
    <kendo-grid-column field="InvoiceAmount" title="Invoice Amount" width="180">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="k-grid-ignore-click" style="text-align: center">
          ${{ dataItem.InvoiceAmount }}
        </div>

      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="JobNumber" title="Job No" width="160">

    </kendo-grid-column>
    <kendo-grid-column field="Description" title="Job Description" width="300">

    </kendo-grid-column>
    <kendo-grid-column field="Aged" title="Aged" width="130">

    </kendo-grid-column>
    <kendo-grid-column field="InvoiceDate" title="Invoice Date" width="170" filter="date" operator="eq"
      format="{0:dd-MM-yyyy}">
      <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
        <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService" operator="eq">
        </kendo-grid-date-filter-menu>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="DueDate" title="Due Date" width="160" filter="date" operator="eq" format="{0:dd-MM-yyyy}">
      <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
        <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService" operator="eq">
        </kendo-grid-date-filter-menu>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="PurchaseOrder" title="Purchase Order" width="180">

    </kendo-grid-column>
    <kendo-grid-column field="" title="" width="170">

      <!-- <ng-template kendoGridHeaderTemplate>
        <button *ngIf="selectedCheckbox.length > 0" kendoButton class="downloadButton" style="background-color: #00ccff !important; font-size: 11px !important;"
          primary="true" (click)="DownloadAllDoc()">DownLoad All</button>
      </ng-template> -->

      <ng-template kendoGridCellTemplate let-dataItem>
        <button kendoButton class="followupButton" title="Download Maintenance Invoice Report" [primary]="true"
          (click)="downloadMaintenanceInvoice(dataItem)" primary="true"><span
            class="k-icon k-i-download"></span></button>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-excel fileName="Open Debt.xlsx" [fetchData]="allData">
      <kendo-excelexport-column field="LocationName" title="Location Name" width="210">

      </kendo-excelexport-column>
      <kendo-excelexport-column field="InvoiceRef" title="Invoice Ref" width="160">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="InvoiceAmount" title="Invoice Amount" width="300">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="JobNumber" title="Job No" width="160">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="Description" title="Job Desc" width="250">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="Aged" title="Aged" width="110">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="InvoiceDate" title="Invoice Date" width="170">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="DueDate" title="Due Date" width="160">
      </kendo-excelexport-column>
      <kendo-excelexport-column field="PurchaseOrder" title="Purchase Order" width="170">
      </kendo-excelexport-column>

    </kendo-grid-excel>
  </kendo-grid>
  <div *ngIf="isShow" style="text-align: center;">
    <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
        <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
    </button>
  </div>
</div>
</section>
<admin-footer class="OpenDebtFooter"></admin-footer>