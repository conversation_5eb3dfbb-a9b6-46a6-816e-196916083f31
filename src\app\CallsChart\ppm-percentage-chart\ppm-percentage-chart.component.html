<div *ngIf="count">

  <kendo-chart class="chart-size-css"
    [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
    (seriesClick)="onSeries($event)">

    <kendo-chart-tooltip>
      <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category" let-series="series">
        {{ category }} : {{value }}%
      </ng-template>
    </kendo-chart-tooltip>
    <kendo-chart-value-axis>
    <kendo-chart-value-axis-item [min]="0" [max]="100">
      </kendo-chart-value-axis-item>
  </kendo-chart-value-axis>


    <kendo-chart-category-axis>
      <kendo-chart-category-axis-item [categories]="['Total PPM %']">
        <kendo-chart-category-axis-item-labels [font]="25">
        </kendo-chart-category-axis-item-labels>

      </kendo-chart-category-axis-item>
    </kendo-chart-category-axis>
    <kendo-chart-series>
      <kendo-chart-series-item type="column" [gap]="3"  [data]="ApproveData">
        <kendo-chart-series-item-labels [margin]="-6">

        </kendo-chart-series-item-labels>
      </kendo-chart-series-item>
      <kendo-chart-series-item type="column" [data]="AwaitingApprove">
        <kendo-chart-series-item-labels>
        </kendo-chart-series-item-labels>
      </kendo-chart-series-item>
    </kendo-chart-series>
  </kendo-chart>
</div>