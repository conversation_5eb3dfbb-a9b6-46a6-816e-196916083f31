.user-row {
  margin-bottom: 14px;
}

.user-row:last-child {
  margin-bottom: 0;
}

.dropdown-user {
  margin: 13px 0;
  padding: 5px;
  height: 100%;
}

.dropdown-user:hover {
  cursor: pointer;
}

.table-user-information > tbody > tr {
  border-top: 1px solid rgb(221, 221, 221);
}

.table-user-information > tbody > tr:first-child {
  border-top: 0;
}

.table-user-information > tbody > tr > td {
  border-top: 0;
}
.toppad {
  margin-top: 20px;
}

.customCss {
  font-size: 14px;
  position: relative;
  bottom: 5px;
  left: 3px;
}
.panel-info {
}
.default {
  position: relative !important;
  bottom: -5px !important;
  font-family: calibri light , "Roboto";
  font-size: 11px;
  font-weight: 500;
}
.autoWidth {
  width: 68% !important;
}
.panel-title {
  font-family: calibri light , "Roboto";
  font-weight: 700;
  padding: 8px 0px;
  font-size: 1rem;
  background: rgb(52, 58, 64);
  color: white;
  padding: 12px;
}
.momFooterCss {
  position: relative;
  bottom: 0;
  left: 0;
  right: 0;
}
.table td,
.table th {
  padding: 3px 9px !important;
}
.table {
  width: 64%;
}
.panelCSS {
  width: 85%;
}

.topFooterCss {
  // top: 300px;
  position: absolute;
}
.section-margin {
  margin-top: 10%;
}

@media only screen and (max-width: 1129px) {
  .section-margin {
    margin-top: 0%;
  }
  .tr-md {
    display: inline-grid !important;
  }
}
@media only screen and (max-width: 524px) {
  kendo-grid {
    width: 326px;
  }
  .table {
    width: 100% !important;
  }
  .table tbody {
    display: block !important;
  }
  .autoWidth {
    width: 0% !important;
  }
  .UserInfoGrid .k-grid td:nth-child(2), .UserInfoGrid .k-grid td:nth-child(3) {
    position: relative !important;
    right: 0px !important;
}
}
