import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CoreDataService } from "../Services/core-data.service";
import { ClientPerceptionModel, CustomerLocationByName, CodeData } from "./clientPerceptionReport.model";
import { SharedDataService } from "../Services/shared-data.service";
import { AuthenticationService } from "../Services/authentication.service";
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SpinnerVisibilityService } from 'ng-http-loader';

import { SessionStorageService } from 'ngx-webstorage';
import { first } from 'rxjs/operators';

export let isBrowserRefreshed = false;

@Component({
  selector: 'app-clientPerceptionReport',
  templateUrl: './clientPerceptionReport.component.html',
  styleUrls: ['./clientPerceptionReport.component.css']
})
export class ClientPerceptionReportComponent implements OnInit {

  public defaultItem: CodeData = {
    CodeID: null, CodeTypeID: null,
    CodeName: null,
    Code: "Select State",
    Description: null
  };
  organisationHasSpaces: boolean;
  saveUsername: any;

  public LocationList: Array<CustomerLocationByName>;
  isKeepConfidential: boolean = true;
  recommendToOthers: boolean = true;

  gridData = [

    {
      Name: "Delivering the agreed service", propCode: "DeliveryAgreedService"
    },
    {
      Name: "Getting it right the first time", propCode: "GettingRightFirstTime"
    },
    { Name: "Rectifying problems", propCode: "RectifyingProblems" },
    {
      Name: "Responsiveness", propCode: "Responsiveness"
    },
    { Name: "Innovative proposals", propCode: "InnovativeProposals" },
    {
      Name: "Mutually beneficial relationship", propCode: "MutuBenRelation"
    },
    { Name: "Accurate invoicing", propCode: "AccurateInvoicing" },
    {
      Name: "Competent staff", propCode: "CompetentStaff"
    },
    { Name: "Professional image", propCode: "ProfessionalImage" },
    {
      Name: "Account Manager Communication", propCode: "AMCommunication"
    },
    {
      Name: "Technician Communication", propCode: "TechCommunication"
    },
    {
      Name: "Office Communication", propCode: "OfficeCommunication"
    }
  ]
  @ViewChild('customerStatus') formRef: any;
  selectedState: any;
  clientPerceptionData: ClientPerceptionModel = new ClientPerceptionModel();
  StateList: CodeData[] = [];
  codeData: CodeData[] = [];
  public defaultItemLocation: { CustName: string, Name: string, CustomerID: number, LocationCode: string, LocationID: number } = { CustName: "Select Location", Name: "Select Location", CustomerID: null, LocationCode: " ", LocationID: null };
  public date = new Date();
  public defaultLocation: { Name: string, CustName: string, fullName: string, LocationID: number } = { Name: "Select ", CustName: 'Select', fullName: 'Select', LocationID: null };
  isReadOnly: boolean = false;
  constructor(private session: SessionStorageService, private coredata: CoreDataService, private shareData: SharedDataService, private spinner: SpinnerVisibilityService, private router: Router, private route: ActivatedRoute, private titleService: Title, private coredataservice: CoreDataService, private sharedDataService: SharedDataService, private authdata: AuthenticationService) {

    isBrowserRefreshed = !router.navigated;


    //       this.loadCustomerLocationDropdown(tokenData.UserID,undefined);
    this.shareData.removeBacklogData();
    let pageTite = this.route.snapshot.data['title'];
    let tokenData = this.authdata.getTokenData();
    this.titleService.setTitle(pageTite);
    if (isBrowserRefreshed === false) {
      this.loadCustomers(undefined);
    }
    this.clientPerceptionData.RecommendOthers = true;
    this.clientPerceptionData.EmailID = tokenData.EmailID;
    this.clientPerceptionData.ContactName = tokenData.Name;
  }


  loadCustomers(locations) {
    if (locations === undefined) {
      this.LocationList = this.session.retrieve('locations');
    }
    else if (locations != undefined) {
      this.LocationList = locations;
    }
    let haveMultipleCustomers = [];
    this.LocationList.map(element => {
      let indexofElement = haveMultipleCustomers.findIndex(data => data.CustomerID === element.CustomerID);
      if (indexofElement === -1) {
        haveMultipleCustomers.push(element);
      }
    });
    if (haveMultipleCustomers.length === 1) {
      this.clientPerceptionData.OrganisationName = haveMultipleCustomers[0].CustName;
      this.isReadOnly = true;
    }
  }
  trimValue(event) {
    let name = event.target.name;
    let trimValue = event.target.value.trim();
    if (trimValue === "") {
      this.organisationHasSpaces = true;
    }
    else if (trimValue != "") {
      this.organisationHasSpaces = false;
    }
    if (this.clientPerceptionData[name] != undefined) {
      this.clientPerceptionData[name] = this.clientPerceptionData[name].trim();
    }
  }
  ngOnInit(): void {
    this.loadCodeData();
  }


  loadCustomerLocationDropdown(id, locations) {
    if (locations === undefined) {
      let locationsInSession = this.session.retrieve('locations');
      if (locationsInSession != null) {
        this.LocationList = locationsInSession;
        this.LocationList = this.LocationList.slice();
      }
      else if (locationsInSession === null) {
        this.coredata.getCustomerLocation(id).pipe(first()).subscribe(res => {
          if (res.StatusCode === 200) {
            this.LocationList = res.response;
            this.LocationList = this.LocationList.slice();
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
            }
          });
      }
    }
    else if (locations != undefined) {
      this.LocationList = locations;
      this.LocationList = this.LocationList.slice();
    }


  }



  close(value) {
    //  value.preventDefault();
  }


  loadCodeData() {
    this.coredataservice.getCodeData().pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        this.codeData = res.response;
        this.StateList = this.codeData.filter(data => data.CodeName === "STATE");
      }

    }, catchError => {
      if (catchError) {

      }
    });
  }

  filterLocation(value) {
    if (value != null && value != undefined) {
      let state = this.codeData.filter(data => data.CodeName === "STATE");
      this.StateList = state.filter((s) => s.Code.toLowerCase().indexOf(value.toLowerCase()) !== -1);
    }
  }





  setRatingValue(propName, val) {
    this.clientPerceptionData[propName] = val;
  }

  saveClientForm() {
    if (this.formRef.valid) {
      this.clientPerceptionData.KeepConfidential = this.isKeepConfidential;
      this.clientPerceptionData.State = this.selectedState.Code;
      if (this.clientPerceptionData.DeliveryAgreedService !== undefined && this.clientPerceptionData.GettingRightFirstTime !== undefined &&
        this.clientPerceptionData.RectifyingProblems !== undefined && this.clientPerceptionData.Responsiveness !== undefined &&
        this.clientPerceptionData.InnovativeProposals !== undefined && this.clientPerceptionData.MutuBenRelation !== undefined &&
        this.clientPerceptionData.AccurateInvoicing !== undefined && this.clientPerceptionData.CompetentStaff !== undefined &&
        this.clientPerceptionData.ProfessionalImage !== undefined && this.clientPerceptionData.AMCommunication !== undefined &&
        this.clientPerceptionData.TechCommunication !== undefined && this.clientPerceptionData.OfficeCommunication) {
        this.coredataservice.SaveClientPerceptionReport(this.clientPerceptionData).subscribe(
          (res: any) => {
            if (res != null && res != undefined && res.StatusCode === 200) {
              this.sharedDataService.showSuccess('Thank you for submitting the feedback');
              this.router.navigateByUrl('/Dashboard');

            }
          },
          catchError => {
            if (catchError) {
              if (catchError.status === 400) {
                this.sharedDataService.showError("Form not submitted due to Technical Error");
              }
            }
            else {
              this.sharedDataService.ErrorHandler(catchError);
            }
          }
        );

      }
      else {
        this.sharedDataService.showError("Please select all the Performance Ratings");
      }
    }
    else if (!this.formRef.valid) {
      this.sharedDataService.showError("Please insert all the Mandatory fields");
    }

  }

  changeRecommendation(val) {
    this.clientPerceptionData.RecommendOthers = val;
  }


  locationSelectionChange(value) {
    if (value != null) {
      let data = this.StateList.find(eleme => eleme.CodeID === value);
      this.selectedState = data;
    }
  }
  locationChange(locations) {
    this.loadCustomers(locations);
  }

}
