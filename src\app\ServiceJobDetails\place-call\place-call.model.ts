export class PlaceCallModel {
    LocationID: number;
    EquipmentID: any;
    PurchaseOrder: string;
    Description: String;
}
export class equipmentDropdownlistModel {
    Code: string;
    EquipmentID: number;
    EquipmentType: string;
    InstallDate: string;
    Make: string; //manufecturer
    Model: string;
}
export class CustomerLocationByName {
    // CustName: string;
    // Name: string;
    // LocationID: number;
    // CustomerID: number;
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number
    Name: string;
    StateCode: string;
}