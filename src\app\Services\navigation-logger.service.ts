import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { AuthenticationService } from "./authentication.service";
import { debounceTime, distinctUntilChanged, filter, timestamp } from "rxjs/operators";
import { CoreDataService } from "./core-data.service";
import { Subscription } from "rxjs";

@Injectable({providedIn: 'root'})
export class NavigationLoggerService {
    private initialized = false;
    private routerSubscription: Subscription | undefined;
    private externalClickListener: any;

    constructor(
        private router: Router,
        private authService: AuthenticationService,
        private coreDataService: CoreDataService
    ) {}

    public initialize(): void {
        if (this.initialized) return;
        this.initialized = true;

        this.trackInternalNavigation();
        this.trackExternalNavigation();
    }

    private trackInternalNavigation(): void {
        this.routerSubscription = this.router.events
        .pipe(
            filter(event => event instanceof NavigationEnd),
            debounceTime(300),
            distinctUntilChanged(
                (a: NavigationEnd, b: NavigationEnd) => a.urlAfterRedirects === b.urlAfterRedirects)
        )
        .subscribe((event: NavigationEnd) => {
            const tokenData = this.initialized ? this.authService.getTokenData() : null;
            if (!tokenData?.UserID) return;

            this.logNavigation(tokenData, event.urlAfterRedirects);
        });
    }

    private trackExternalNavigation() {
        this.externalClickListener = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (target.tagName.toLowerCase() === 'a') {
                const anchor = target as HTMLAnchorElement;
                const href = anchor.href;

                if (href && !href.includes(window.location.origin) && href !== 'javascript:void(0)') {
                    const tokenData = this.authService.getTokenData();
                    if (!tokenData?.UserID) return;

                    this.logNavigation(tokenData, href);
                }
            }
        };

        document.addEventListener('click', this.externalClickListener);

        if (!(window as any)._openPatched) {
            const originalOpen = window.open;
            window.open = (url: string, target?: string, features?: string) => {
              const tokenData = this.authService.getTokenData();
              if (tokenData?.UserID) {
                const pageUrl = url.split('/')[2];
                this.logNavigation(tokenData, pageUrl);
              }
              return originalOpen.call(window, url, target, features);
            };
            (window as any)._openPatched = true;
        }
    }

    public logNavigation(tokenData: any, url: string): void {
        const logEntry = {
            userID: tokenData.UserID,
            pageUrl: url.split('?')[0].toLowerCase(),
            timestamp: new Date().toISOString()
        };

        // console.log(logEntry);
        this.coreDataService.AddUserNavigationLog(logEntry).subscribe({
            next: () => {},
            error: () => {}
        });
    }

    public isInitialized(): boolean {
        return this.initialized;
    }

    public reset() {
        this.initialized = false;

        if(this.routerSubscription) {
            this.routerSubscription.unsubscribe();
            this.routerSubscription = undefined;
        }

        if(this.externalClickListener) {
            document.removeEventListener('click', this.externalClickListener);
            this.externalClickListener = undefined;
        }
    }
      
}   