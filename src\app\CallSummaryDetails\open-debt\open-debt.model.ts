export class OpenDebtModel {
    Location: string;
    Description: string;
    InvoiceRef: string;
    InvoiceAmount: string;
    JobNumber: string;
    PurchaseOrder: string;
    InvoiceDate: Date;
    DueDate: Date;
    Aged: string;
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}

export enum InvoiceNumberType {
    "S" = "Service",
    "M" = "Maintenance",
    "J" = "Job Cost",
    "P" = "Projects"
}
export enum OpenDebtChartKeys {

    "0To30" = "0 - 30 DAYS",
    "31To60" = "31 - 60 DAYS",
    "61To90" = "61 - 90 DAYS",
    "Over90" = "91 AND OVER"
}