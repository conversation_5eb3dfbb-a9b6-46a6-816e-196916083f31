export class CustomersEntities {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class CustomerSummary {
    TotalControlsJCServices: number;
    TotalInstallJCServices: number;
    TotalMCCServices: number;
    TotalMaintenanceJCCallsServices: number;
    TotalQuotes: number;
    TotalServicesJCCallServices: number;
    TotalTMCompletionServices: number;
    TotalTMOpenServices: number;
    TotalTMServiceCalls: number;
    TotalJCServiceCalls: number;
    TotalOpenDebt: number;
    TotalSubContractor: number;
}
export class LocationDropdownListModel {
    CustName: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class CustomerLocationByName {
    CustName: string;
    Name: string;
    LocationID: number;
    CustomerID: number;
    StateCode: string;
    CustomerCode: string;
    LocationCode: string;
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}
