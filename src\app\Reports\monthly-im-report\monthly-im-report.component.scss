.margin-top-table-list .container-fluid {
  padding: 0;
}
.margin-top-table-list {
  margin-top: 155px !important;
  width: 93% !important;
  position: relative !important;
  margin-left: 4% !important;
  left: -6px !important;
  margin-bottom: 79px !important;
}
.dateInputCss,
:host ::ng-deep .k-dateinput-wrap,
:host ::ng-deep .k-input {
    height: calc(8px + 1.75em);
    color: rgba(0, 0, 0, 0.87) !important;
}

::ng-deep .custom-multiselect .k-multiselect-wrap .k-button {
  line-height: normal;
  padding: 4px 8px;
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap {
    border-color: rgba(0, 0, 0, 0.87) !important;
    border-width: 0 0 1px !important;
}
@media (max-width: 1130px) { 
  .section-css{
    margin-top: -5px !important;  }
  
}
@media print {
  body {
    -webkit-print-color-adjust: exact;
  }

  .no-print,
  .no-print * {
    display: none !important;
  }

  .no-print-required,
  .no-print-required * {
    display: none !important;
  }
}
input[type="text"] {
  padding: 7px 9px;
}
.border-auto {
  width: 200px;
  border-right: 1px #00000024 solid;
  border-right: 1px #00000024 solid;
  border-left: 1px #00000024 solid;
}
.second-pane {
  margin-bottom: -18px;
}
.border-bottom {
  width: 200px !important;
  border-right: 1px #00000024 solid !important;
  border-bottom: 1px #00000024 solid !important;
  border-right: 1px #00000024 solid !important;
  border-left: 1px #00000024 solid !important;
}
tr {
  border-right: 1px #00000024 solid;
  border-left: 1px #00000024 solid;
}
.td-margin-top {
  border-right: 1px #00000024 solid;
  border-top: 1px #00000024 solid;
}
.bottom-button {
  margin-top: 16px;
}
.errorMessage {
  font-family: calibri light , "Roboto";
  font-weight: 700;
  color: #ff0000c7;
  font-size: small;
  position: relative;
  bottom: 4px;
}
.dropdownListWidth {
  width: 100%;
  position: relative;
  bottom: 12px;
}
.dropdownDateWidth {
  width: 36%;
  position: relative;
  bottom: 9px;
}
.text-area-size {
  resize: none;
  overflow-y: hidden;
  width: auto;
  border: none;
  padding: 10px 0px;
  font-size: 99%;
  min-height: 0px;
}
.ul-row li {
  margin-bottom: 3px;
}
.kendoBox {
  margin-bottom: -9px;
  width: -webkit-fill-available;
  position: relative;
  bottom: 16px;
}
p {
  font-family: calibri light , "Roboto";
  font-weight: 600;
  color: #000000f7;
  position: relative;
  margin-top: 0;
  top: 9px;
  margin-bottom: 1rem;
}
body {
  font-size: 12px;
}

table td {
  max-height: 15px;
}

.element-white {
  background-color: #fff;
  border: 0;
}
#foto {
  cursor: pointer;
}
.title {
  background-color: #343a40;
  /* font-family: 'Open Sans Condensed'; */
  font-family: calibri light , "Roboto";
  font-weight: 700;
  padding: 12px 0px;
}

select {
  border: 0 !important; /*Removes border*/
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  text-overflow: "";
  text-indent: 0.01px; /* Removes default arrow from firefox*/
  font-style: italic;
}

select::-ms-expand {
  display: none;
}

h6::first-letter {
  margin-top: 0;
}
h4::first-letter {
  margin-top: 0;
}

h6 {
  font-size: 14px;
}

kendo-daterange label {
  display: grid;
}
kendo-daterange kendo-dateinput {
  width: 93px;
  position: relative;
  bottom: 5px;
}
kendo-daterange {
  display: flex;
}
kendo-daterange .startDate {
  margin-right: 10px;
}
// .k-multiselect {
//   position: relative;
//   bottom: 7px;
// }

.label {
  font-size: 13px;
  font-weight: 600;
  color: #212529;

  position: relative;
  right: 1px;
}

.mutiselectTextPosition {
  text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover {
  transition: 0.5s !important;
  background: black !important;
}

.ul-container {
  padding: 0px 14px !important;
}

.k-i-close {
  font-size: 12px;
  padding: 2px;
  box-sizing: content-box;
}
.k-select {
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  margin-left: 0.5em;
  margin-right: -0.2em;
}
.span-content {
  background: #80808042;
  margin-right: 8px;
  padding: 4px;
  font-family: calibri light , "Roboto";
  margin-bottom: -7px;
  font-weight: 500;
  border-radius: 16px;
  margin-top: 4px;
}

.monthlyReportFooterCss {
  position: absolute;
  // top: 181px;
  bottom: 0;
  left: 0;
  right: 0;
}
.drodown-header-location
{
  padding: 7px 73px !important;
}
@media only screen and (max-width: 767px)
{
  .mutiselectTextPosition {
 
    font-size: 9px !important;
   
}
.col-3 button
{
  position: relative !important;
  right: 12px !important;
}
.md-size
{
  font-size: 9px !important;
}
.span-content
{
  font-size: 10px !important;
}
.location-position
{
  position: relative !important;
  right: 13px !important;
}
.k-button
{
  font-size: 9px !important;

}
.drodown-header-location
{
    padding: 7px 38px !important;

}
.drodown-filter-location
{
  padding: 7px 26px !important;

}
}
.setField
{
  position: relative;
  top: 14px;
}





.drodown-filter-location {
  background: #343a40;
  color: white;
  position: relative;
  bottom: 9px;
  margin-bottom: -4px;
  padding: 7px 39px;
}


















.displayInline
{
  position: relative;
  bottom: 3px;
}



.header-font-size {
  padding: 7px 13px;
  font-size: 72%;
  font-family: calibri light , "Roboto";
  text-decoration: none;
}
.imgHight {
  width: 68%;
  height: 41px;
}
.eValuateButton {
  margin-right: 23px;
}
.buttonFooter {
  width: 18% !important;
  background: #4ab4de !important;
  color: white !important;
}
.eValuateButton:hover {
  background: none !important;
}
.navbar-nav {
  margin-bottom: 1px !important;
}

.fa-caret-down {
  position: relative;
  left: 9px;
  top: 1px;
}
.submenu li {
  padding: 0px 5px;
  border-bottom: 1px solid #80808047;
}
.report-builder:hover .submenu {
  color: black;
  background: #17b0e9;
  display: block;
  top: 95%;
  width: 168px;
  right: 328px;
  border-radius: 0px;
  margin-top: 0px;
  padding: 0;
}
.logOutFormCss {
  display: none;
  width: 168px;
  position: absolute;
  background: #17b0e9;
  top: 100%;
  right: 46px;
}
.nav-item:hover {
  background: #17b0e9;
}
.nav-item {
  padding: 10px 0px !important;
}
.submenu a {
  padding: 8px;
  color: white;
}
.submenu li a {
  padding: 8px;
  text-align: left;
}
.submenu li:hover {
  background: #80808038;
}
ul li p {
  margin-bottom: -20px;
  position: absolute;
  right: 66px;
  color: rgba(212, 212, 212, 1);
  font-size: 79%;
  font-weight: 500;
}


@media only screen and (max-width: 767px) {
  .bottom-button
  {
    margin-top:0px;
  }
}



@media only screen and (min-width: 768px) {
  .text-right{
   text-align: right !important;
 }
}

@media only screen and (max-width: 991px) {
  ul li p {
    text-align: center;
    left: 0px;
    position: relative;
  }
}
ul li a:hover {
  text-decoration: none;
  //       transition: 0.5s;
  //       color: gray;
}

ul li {
  padding: 0px;
}

.fa-icon {
  font-size: 14px;
}
.fa-sign-out {
  position: relative;
  left: 4px;
}

.mutiselectTextPosition {
  text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover {
  transition: 0.5s !important;
  background: black !important;
}

@media only screen and (min-width: 991px) {
  .bar-chart-position {
    margin-top: 0;
    margin-bottom: 30px;
  }
}
.ul-container {
  padding: 0 12px;
}
.ul-row {
  height: 101px;
  overflow-y: scroll;
}

.k-select {
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  margin-left: 0.5em;
  margin-right: -0.2em;
}
.k-i-close {
  font-size: 12px;
  padding: 2px;
  box-sizing: content-box;
  cursor: pointer;
}
.span-content {
  background: rgba(128, 128, 128, 0.25882352941176473);  margin-right: 8px;
  padding: 4px;
  font-family: calibri light , "Roboto";
  margin-bottom: -7px;
  font-weight: 500;
  border-radius: 16px;
  margin-top: 4px;
}
.count-number {
  font-size: 2em;
}
@media only screen and (max-width: 1199px) {
  .bar-chart-position {
    margin-top: 25px;
  }
}
.k-chart text {
  font: sans-serif !important;
}

.position-pie-chart {
  height: 337px;
  margin-top: 2%;
  position: relative;
}
.count-number {
  margin-top: -8px;
}

.top-text-open-debt {
  position: relative;
  left: 8%;
  top: 1px;
  margin: 0px;
  font-size: 11px;
}
.dashboard-header .h4 {
  font-family: calibri light , "Roboto";
  font-size: 80%;
  font-weight: bold !important;
}

.text-uppercase a {
  color: #009adc;
}
.text-uppercase a:hover {
  transition: 0.7s;
  color: black;
}
.count-number a {
  color: black;
}
.container-fluid .count-data {
  margin-bottom: 14px;
}
.margin-bottom-location {
  margin-bottom: 1%;
}
.dashboard-counts strong {
  font-size: 72%;
}
 
.k-button {
  font-size: 13px;
}

 
.btn-sm {
  border-radius: 4px;
  color: white;
  background-color: #0093d7;
  border-color: #008cd1;
}
.btn-sm:hover {
  border-radius: 4px;
  background-color: #037db5;
  border-color: #037db5;
}
 
.datePickerRightPosition {
  position: relative;
  left: 1px;
   
}

.k-required {
  color: red;
}