import { MFAModel, StatusCodes, otpToken } from "./../shared";
import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { AuthenticationService } from "../../Services/authentication.service";
import { Router, ActivatedRoute } from "@angular/router";
import { Title } from "@angular/platform-browser";
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { TokenModel } from "../shared";
import { JwtHelper } from "angular2-jwt";
import { BehaviorSubject, Observable } from "rxjs";
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { NavigationLoggerService } from "src/app/Services/navigation-logger.service";

@Component({
  selector: "admin-app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent implements OnInit, AfterViewInit {
  LoginForm: FormGroup;
  submitted = false;
  tokenData: TokenModel = new TokenModel();
  @ViewChild("captchaRef") captcha: any;
  isWrongCredential: boolean = false;
  isCaptchaVerified: boolean = false;
  userName: string = null;

  jwtHelper: JwtHelper = new JwtHelper();
  ExpiryTime: number = 0;
  public UserInfo: any = undefined;
  public userInfo$: BehaviorSubject<any> = new BehaviorSubject<any>(this.UserInfo);
  public loggedIn: boolean = false;
  public loggedIn$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(this.loggedIn);

  isNewLogin: boolean = false;
  isLoggedIn: boolean;
  loginData: MFAModel;
  public isPasswordChange: boolean = false;
  public otpToken: otpToken = new otpToken();

  constructor(
    private titleService: Title,
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private coredataservice: CoreDataService,
    private authService: AuthenticationService,
    private sharedDataService: SharedDataService,
    private router: Router,
    private recaptchaV3Service: ReCaptchaV3Service,
    private navigationLoggerService: NavigationLoggerService
  ) {
    localStorage.clear();
    let pageTite = this.activatedRoute.snapshot.data["title"];
    this.titleService.setTitle(pageTite);
    this.sharedDataService.removeBacklogData();
  }
  ngAfterViewInit(): void {
    //MS - Access CP from AE Flow
    // Already logged in user Token
    //var userToken = localStorage.getItem('CPToken');
    var userToken = this.sharedDataService.getAccessToken();

    //console.log("1. User Token: " + userToken);
    let flowToken = this.activatedRoute.snapshot.paramMap.get("flowT");
    //console.log("2. Flow Token: " + flowToken);
    if (flowToken != undefined && flowToken != "") {
      //MS: If Token is available then Validate & redirect to Dashboard Page
      if (userToken != undefined && userToken != null) {
        //console.log("3. Already login")
        // Do Nothing, already logged In with another user.
        this.isNewLogin = false;
        this.getFilterDateByUserID();
      } else {
        // console.log("4. New user login")
        this.isNewLogin = true;
        //MS: If token not available then validate the Token From AE
        //localStorage.setItem('CPToken', flowToken);
        this.sharedDataService.setAccessToken(flowToken);

        let data = { access_token: flowToken };
        this.authService._setSession(data);
        this.getFilterDateByUserID();
      }
    }
  }

  ngOnInit() {
    this.LoginForm = this.formBuilder.group({
      loginUsername: ["", [Validators.required]],
      password: ["", [Validators.required, Validators.minLength(4)]],
      mfa_token: [null],
      OTP: [null],
    });
  }
  resolved(event) {
    if (event != null && event != undefined) {
      this.isCaptchaVerified = true;
    }
  }
  get LoginControlValidator() {
    return this.LoginForm.controls;
  }
  login(form) {
    this.submitted = true;
    if (this.LoginForm.valid || this.isPasswordChange) {
      if (this.isWrongCredential === true) {
        if (this.isCaptchaVerified === false) {
          this.sharedDataService.showWarning(
            "Please verify captcha before logging in!"
          );
        } else if (this.isCaptchaVerified === true || this.isPasswordChange) {
          this.subMitLoginForm(form);
        }
      } else if (this.isWrongCredential == false || this.isPasswordChange) {
        this.subMitLoginForm(form);
      }
    } else if (this.LoginForm.invalid) {
      return;
    }
  }
  subMitLoginForm(form) {
    if (
      /^([A-Za-z0-9][a-z][.]*[-]*[_]*[A-Za-z0-9]*[a-z][0-9]*[.]*[-]*[_]*[A-Za-z0-9]*[.]*[-]*[_]*[A-Za-z0-9]*)$/.test(
        form.loginUsername
      )
    ) {
      this.sharedDataService.loginUsername = form.loginUsername;
      if (this.isPasswordChange) {
        form.password = "";
      }
      this.coredataservice.getLogin(form).subscribe(
        (data: any) => {
          this.authService._setSession(data);
          this.getFilterDateByUserID();
        },
        (catchError) => {
          if (catchError) {

            if (catchError.status === 400) {
              if (catchError.error.error == "MFA Required") {
                this.recaptchaV3Service.execute("importantAction").subscribe((token) => {
                  if (token) {
                    this.otpToken.RecaptchaToken = token;
                    this.otpToken.userName = form.loginUsername;
                    this.coredataservice
                      .SendPasscode(this.otpToken)
                      .subscribe((res) => {
                        if (res["StatusCode"] === 200) {
                          this.sharedDataService.showInfo("OTP Sent");
                          this.sharedDataService.ChangePassword = false;
                          this.sharedDataService.loginUsername = form.loginUsername;
                          this.sharedDataService.password = form.password;
                          this.router.navigate(["/TwoFactorAuthentication"]);
                        } else {
                          this.sharedDataService.showInfo("Unable to send OTP");
                        }
                      }, (error) => {
                        this.sharedDataService.showError(
                          "reCAPTCHA Error")
                      });
                  }
                }
                )

              }
              else if (catchError.error.error == "Password Change required" || catchError.error.error == "User is Valid") {
                if (catchError.error.error == "Password Change required") {
                  this.sharedDataService.showWarning("Password Expired. Please enter new password")
                }
                this.recaptchaV3Service.execute("importantAction").subscribe((token) => {
                  if (token) {
                    this.otpToken.RecaptchaToken = token;
                    this.otpToken.userName = form.loginUsername;
                    this.coredataservice
                      .SendPasscode(this.otpToken)
                      .subscribe((res) => {
                        if (res["StatusCode"] === 200) {

                          this.sharedDataService.loginUsername = form.loginUsername;

                          this.sharedDataService.ChangePassword = true;
                          this.router.navigate(["/TwoFactorAuthentication"]);
                        } else if (res["StatusCode"] === 500 && res["response"] == 'Please contact Airmaster IT for support') {
                          this.sharedDataService.showInfo("Please contact Airmaster IT for support");
                        }
                        else {
                          this.sharedDataService.showInfo("Unable to send OTP");
                        }
                      }, (error) => {
                        this.sharedDataService.showError(
                          "reCAPTCHA Error")
                      });
                  }
                }
                )


              }
              else if (catchError.error.error == "User is Invalid") {

                this.sharedDataService.showError("Invalid Username")
              }
              else if (catchError.error.error == "You are not authorized in this application. Please login to AE") {
                this.sharedDataService.showError("You are not authorized in this application. Please login to AE");
                this.isWrongCredential = true;
                if (this.isCaptchaVerified === true) {
                  this.captcha.reset();
                  this.isCaptchaVerified = false;
                }
              }
              else {
                this.sharedDataService.showError("Invalid Username / Password");
                this.isWrongCredential = true;
                if (this.isCaptchaVerified === true) {
                  this.captcha.reset();
                  this.isCaptchaVerified = false;
                }
              }
            } else {
              this.sharedDataService.ErrorHandler(catchError);
            }
          }
        }
      );
    } else {
      this.sharedDataService.showError("Invalid Username");
    }
  }
  // FK: get starting date for filter by user id
  getFilterDateByUserID() {
    this.tokenData = this.authService.getTokenData();
    //let userId = this.tokenData.UserID;
    this.coredataservice.getFilterDateByUserID().subscribe(
      (res: any) => {
        if (res != null && res != undefined) {
          if (
            res.StatusCode === 200 &&
            res.response != null &&
            res.response != undefined
          ) {
            this.sharedDataService.setStartDateByUser(
              res.response.FromDateFilter
            );
            localStorage.setItem("IMUserOnly", res.response.IMUserOnly == null ? false : res.response.IMUserOnly);
            localStorage.setItem("TuningModule", res.response.TuningModule == null ? false : res.response.TuningModule);
            if (res.response.IMUserOnly) {
              this.router.navigate(["/IM-Dashboard"]);
            } else {
              this.router.navigate(["/Dashboard"]);
            }

            if (this.isNewLogin == true) {
              this.navigationLoggerService.logNavigation(this.tokenData, 'login');
              this.sharedDataService.showSuccess(
                "You have successfully logged in"
              );
            }
          } else if (res.StatusCode === 204) {
            this.router.navigate(["/Dashboard"]);
            if (this.isNewLogin == true) {
              this.navigationLoggerService.logNavigation(this.tokenData, 'login');
              this.sharedDataService.showSuccess(
                "You have successfully logged in"
              );
            }
          } else {
            this.sharedDataService.showError(
              "Error has occured please contact support."
            );
          }
        }
      },
      (catchError) => {
        if (catchError) {
          this.sharedDataService.ErrorHandler(catchError);
        }
      }
    );
  }



  ChangePassword() {
    this.isPasswordChange = true;
  }
  Back() {
    this.isPasswordChange = false;
  }

  redirectToAE() {
    let origin = window.location.origin.toString();

    if (origin == "https://cp.airmaster.com.au") {
      window.open("https://aetest.airmaster.com.au");
    }

    else if (origin == "https://cp.airmasterfire.com.au") {
      window.open("https://aetest.airmasterfire.com.au");
    }

    else if (origin == "https://cp.optimumair.co.nz") {
      window.open("https://aetest.optimumair.co.nz");
    }

    else if (origin == "https://cp.controlco.nz") {
      window.open("https://aetest.controlco.nz");
    }

    else if (origin == "https://flow.airmaster.com.au") {
      window.open("https://ae.airmaster.com.au");
    }

    else if (origin == "https://flow.airmasterfire.com.au") {
      window.open("https://ae.airmasterfire.com.au");
    }

    else if (origin == "https://flow.optimumair.co.nz") {
      window.open("https://ae.optimumair.co.nz");
    }

    else if (origin == "https://flow.controlco.nz") {
      window.open("https://ae.controlco.nz");
    }

    else if (origin == "http://localhost:4300" || origin == "http://localhost:4200") {
      window.open("https://aetest.airmaster.com.au");
    }

    else if (origin == "http://customerportal.eudemonic.co.in") {
      window.open("http://aetest.eudemonic.co.in");
    }

  }
}
