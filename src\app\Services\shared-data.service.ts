﻿import { Injectable } from "@angular/core";
import { BehaviorSubject } from "rxjs";
import { Observable } from "rxjs/Observable";
import { Subject } from "rxjs";
import { StatusCodes, CustomerSummary, MFAModel } from "../Common/shared";
import { ToastrService } from "ngx-toastr";
import { Router, ActivatedRoute } from "@angular/router";
import { MostExpensiveEquipment } from "../Dashboard/dashboard.model";

@Injectable()
export class SharedDataService {
  progressBarEvent: number = 0;
  isIMReport = false;
  defaultdataMultiple = [];
  customerLocations: Array<any> = [];
  changeDateInGrid = {};
  changeDateBehavior!: any;
  public changeDateBehavior$:BehaviorSubject<any> = new BehaviorSubject<any>(this.changeDateBehavior);
  public verfication: BehaviorSubject<any> = new BehaviorSubject<any>(0);
  public progressEventBehaviorSubject: BehaviorSubject<any> = new BehaviorSubject<any>(this.progressBarEvent);
  progressEventBehaviorSubjectAsObservable =
    this.progressEventBehaviorSubject.asObservable();
  changeDateBehaviorAsObservable = this.changeDateBehavior$.asObservable();
  dashBoardCountData: CustomerSummary = new CustomerSummary();
  pieChartData = [];
  public pieChartsubject: BehaviorSubject<any> = new BehaviorSubject<any>(this.pieChartData);
  piechartasObservable = this.pieChartsubject.asObservable();
  public customerSummarySubject: BehaviorSubject<any> = new BehaviorSubject<any>(this.dashBoardCountData);
  customerSummaryAsObservable = this.customerSummarySubject.asObservable();
  public customerSubject: BehaviorSubject<any> = new BehaviorSubject<any>(this.customerLocations);
  public defaultdata: BehaviorSubject<any> = new BehaviorSubject<any>(this.defaultdataMultiple);
  defaultasObservable = this.defaultdata.asObservable();
  customeridsasObservables = this.customerSubject.asObservable();
  public locationsList: Subject<Array<any>> = new Subject<Array<any>>();
  locationlistAsObservable = this.locationsList.asObservable();
  public LoginData: Subject<any> = new Subject<any>();
  public CustomerID: number;
  public quotePriority: Array<string> = [];
 public loginUsername: string;
 public password: string;
 public ChangePassword:boolean;
   IsCustomerLeveldata: any;
  public getMOMData: boolean = false;
  public getMOMData$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(this.getMOMData);

  public MostExpEqpBar: MostExpensiveEquipment = new MostExpensiveEquipment();

  public temperatureAvailable$ = new BehaviorSubject<boolean>(false);
  public humidityAvailable$ = new BehaviorSubject<boolean>(false);
  public co2Available$ = new BehaviorSubject<boolean>(false);

  private toggles = new BehaviorSubject<{ [key: string]: boolean }>({
    assetPerformance: false,
    workflow: true,
    backlog: true,
    tuning: true,
    awaitingApproval: true
  });

  private togglesForIAQ = new BehaviorSubject<{ [key: string]: boolean }>({
    Temperature: false,
    CO2: true,
    Humidity: true
  });

  toggles$ = this.toggles.asObservable();
  togglesForIAQ$ = this.togglesForIAQ.asObservable();

  updateToggle(key: string, value: boolean) {
    const current = this.toggles.value;
    this.toggles.next({ ...current, [key]: value });
  }

  getToggleValue(key: string): boolean {
    return this.toggles.value[key];
  }

  updateToggleForIAQ(key: string, value: boolean) {
    const current = this.togglesForIAQ.value;
    this.togglesForIAQ.next({ ...current, [key]: value });
  }

  getToggleValueForIAQ(key: string): boolean {
    return this.togglesForIAQ.value[key];
  }

  setCustomerIDS(ids) {
    this.customerSubject.next(ids);
  }
  setLocationList(data) {
    this.locationsList.next(data);
  }
  setProgressEvent(data) {
    if (typeof data == "number" && !isNaN(data)) {
      this.progressEventBehaviorSubject.next(data);
    }
  }
  setCustomerSummary(data) {
    this.customerSummarySubject.next(data);
  }

  //FK:
  setStartDateByUser(date: any) {
    localStorage.setItem("startdate", date);
  }
  //FK:
  getStartDateByUser() {
    return localStorage.getItem("startdate");
  }

  public UserInfo: any = {};
  public List_data: any = [];
  public _Token: Subject<any> = new Subject<any>();
  public Token$: Observable<any> = this._Token.asObservable();

  broadcastToken(token: any) {
    this._Token.next(token);
  }
  defaultvalue(data) {
    this.defaultdata.next(data);
  }
  changeDateDefault(data) {
    this.changeDateBehavior$.next(data);
  }

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private ToastrService: ToastrService
  ) {}

  public _isLoggedIn: Subject<boolean> = new Subject<boolean>();
  public broadcastLoginStatus$ = this._isLoggedIn.asObservable();
  broadcastLoginStatus(loginStatus: boolean) {
    this._isLoggedIn.next(loginStatus);
  }
  public quoteDetail:Subject<any> =new Subject<any>();

  public CodeData: any = [];
  public CodeData$: BehaviorSubject<any> = new BehaviorSubject<any>(this.CodeData);
  public ErrorHandler(Response: any) {
    switch (Response.status) {
      case StatusCodes.Continue: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.SwitchingProtocols: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Created: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.NonAuthoritativeInformation: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.ResetContent: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.PartialContent: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.MultipleChoices: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Ambiguous: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.MovedPermanently: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Moved: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Found: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Redirect: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.SeeOther: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.RedirectMethod: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.NotModified: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.UseProxy: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.Unused: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.TemporaryRedirect: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.RedirectKeepVerb: {
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue"
        );
        break;
      }
      case StatusCodes.BadRequest: {
        this.showError("Bad Request. Please Contact Support. ");
        break;
      }
      case StatusCodes.Unauthorized: {
        localStorage.clear();
        this.router.navigate(["/Login"]);
        this.showError(
          "Unauthorized Request, Please Login or refresh to continue."
        );
        break;
      }
      //case StatusCodes.PaymentRequired: {
      //    this.showError("Unauthorized Request, Please Login or refresh to continue");
      //    break;
      //}
      case StatusCodes.Forbidden: {
        this.showError("Request Forbidden, Please Contact Support.");
        break;
      }
      case StatusCodes.NotFound: {
        this.showError("Request not found, Please Contact Support.");
        break;
      }
      case StatusCodes.MethodNotAllowed: {
        this.showError("Request not allowed, Please Contact Support.");
        break;
      }
      case StatusCodes.NotAcceptable: {
        this.showError("Request Not accepted, Please Contact Support.");
        break;
      }
      case StatusCodes.ProxyAuthenticationRequired: {
        this.showError(
          "Proxy Authentication Required, Please Contact Support."
        );
        break;
      }
      case StatusCodes.RequestTimeout: {
        this.showError("Request timeout, Please Contact Support.");
        break;
      }
      case StatusCodes.Conflict: {
        this.showError("Request Conflict, Please Contact Support.");
        break;
      }
      case StatusCodes.Gone: {
        this.showError(
          "Requested Resource is no longer available, Please Contact Support."
        );
        break;
      }
      case StatusCodes.LengthRequired: {
        this.showError(
          "Required header length missing, Please Contact Support."
        );
        break;
      }
      // case StatusCodes.PreconditionFailed: {
      //    this.showError("Unauthorized Request, Please Contact Support");
      //    break;
      // }
      case StatusCodes.RequestEntityTooLarge: {
        this.showError("Request too long, Please Contact Support.");
        break;
      }
      case StatusCodes.RequestUriTooLong: {
        this.showError("Request URI too long, Please Contact Support.");
        break;
      }
      case StatusCodes.UnsupportedMediaType: {
        this.showError("Request type not supported, Please Contact Support.");
        break;
      }
      case StatusCodes.RequestedRangeNotSatisfiable: {
        this.showError(
          "Requested Range Not Satisfiable, Please Contact Support."
        );
        break;
      }
      case StatusCodes.ExpectationFailed: {
        this.showError("Expect header failed, Please Contact Support.");
        break;
      }
      case StatusCodes.UpgradeRequired: {
        this.showError(
          "Network Protocol needs to be changed, Please Contact Support."
        );
        break;
      }
      case StatusCodes.InternalServerError: {
        this.showError("Internal Server Error, Please Contact Support");
        break;
      }
      case StatusCodes.NotImplemented: {
        this.showError(
          "Server does not support the requested function, Please Contact Support."
        );
        break;
      }
      case StatusCodes.BadGateway: {
        this.showError("Bad Gateway, Please Contact Support.");
        break;
      }
      case StatusCodes.ServiceUnavailable: {
        this.showError("Service/Server unavailable, Please Contact Support.");
        break;
      }
      case StatusCodes.GatewayTimeout: {
        this.showError("Server Timeout, Please Contact Support.");
        break;
      }
      case StatusCodes.HttpVersionNotSupported: {
        this.showError("Incorrect HTTP Version, Please Contact Support.");
        break;
      }
      default: {
        this.showError(
          "The system error has occurred, please contact support."
        );
        break;
      }
    }
  }

  showSuccess(message) {
    this.ToastrService.success(message);
  }

  showError(message) {
    this.ToastrService.error(message);
  }

  showWarning(message) {
    this.ToastrService.warning(message);
  }

  showInfo(message?) {
    this.ToastrService.info(message);
  }
  //MS - Setting, Getting & Deleting Access Token Dynamically.
  setAccessToken(token: string) {
    if (token != null && token != undefined && token != "") {
      if (window.location.hostname == "localhost") {
        localStorage.setItem("CPToken", token);
      } else if (
        window.location.hostname.split(".")[1].toString().toUpperCase() ==
        "AIRMASTER"
      ) {
        localStorage.setItem("CPTokenAIR", token);
      } else if (
        window.location.hostname.split(".")[1].toString().toUpperCase() ==
        "AIRMASTERFIRE"
      ) {
        localStorage.setItem("CPTokenAFS", token);
      } else if (
        window.location.hostname.split(".")[1].toString().toUpperCase() ==
        "OPTIMUMAIR"
      ) {
        localStorage.setItem("CPTokenOPT", token);
      } else if (
        window.location.hostname.split(".")[1].toString().toUpperCase() ==
        "CONTROLCO"
      ) {
        localStorage.setItem("CPTokenControlco", token);
      }
    }
  }
  getAccessToken() {
    var token = undefined;
    if (window.location.hostname == "localhost") {
      token = localStorage.getItem("CPToken");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTER"
    ) {
      token = localStorage.getItem("CPTokenAIR");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTERFIRE"
    ) {
      token = localStorage.getItem("CPTokenAFS");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "OPTIMUMAIR"
    ) {
      token = localStorage.getItem("CPTokenOPT");
    } else if (
      window.location.hostname.split(".")[1].toString().toUpperCase() ==
      "CONTROLCO"
    ) {
      token = localStorage.getItem("CPTokenControlco");
    }
    return token;
  }
  deleteAccessToken() {
    if (window.location.hostname == "localhost") {
      localStorage.removeItem("CPToken");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTER"
    ) {
      localStorage.removeItem("CPTokenAIR");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTERFIRE"
    ) {
      localStorage.removeItem("CPTokenAFS");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "OPTIMUMAIR"
    ) {
      localStorage.removeItem("CPTokenOPT");
    } else if (
      window.location.hostname.split(".")[1].toUpperCase() == "CONTROLCO"
    ) {
      localStorage.removeItem("CPTokenControlco");
    }
  }

  removeBacklogData(){
     localStorage.removeItem("BacklogData");
  }
}
export class locationDateFilterEntity {
  start: string;
  end: string;
  locations: string;
}
