.backlog-ew {
    margin-top: 60px;
    display: flex; 
    gap: 15px; 
    height: 130px !important; 
    width: 40%;
}
.dashboardFooterCss {
    position: absolute;
    left: 0;
    right: 0;
    top: auto;
    bottom: auto;
    z-index: 999;
}

.dashboard-header {
    margin-left: 220px;
    height: calc(100vh - 92.5px);
    margin-bottom: 0;

    @media (max-width: 768px) {
        margin-left: 0;
    }
}

.panel-container {
  margin-bottom: 15px;
  background: white;
}

.panel-btn {
  font-weight: 600;
  font-size: 1.6vw;
  background: white;
  border-radius: 6px 6px 0 0;
  width: 100%;
  justify-content: space-between;
  padding: 10px 15px;
  border: 1px solid #757575;
  transition: border-radius 0.3s ease;
  box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 0.25);

  span {
    display: inline-block;
    text-transform: none !important;
  }

  .k-icon {
    float: right;
    font-size: 16px;
  }
}

.collapse-panel {
  background: white;
  overflow: hidden;
  border-radius: 0 0 6px 6px;
  border: 1px solid #757575;
  border-top: 0;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.25);
}

.switch {
    position: relative;
    display: inline-block;
    width: 26px;
    height: 12px;

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0; left: 0;
      right: 0; bottom: 0;
      background-color: #d9d9d9;
      transition: background-color 0.3s;
      border-radius: 16px;

      &::before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 0;
        bottom: 0;
        background-color: whitesmoke;
        transition: 0.4s;
        border-radius: 50%;
        transition: transform 0.3s, background-color 0.3s;
      }
    }

    input:checked + .slider:before {
      transform: translateX(12px);
      background-color: #575757;
    }
}


.col-md-12 {
    // padding-right: 0 !important;
}

.screenScroll {
    border-radius: 10px;
    padding: 20px;
}

.donutChart {
    height: 290px;
}

.backlogTasksHeader {
    margin-bottom: 4vh;
}

.total-cost .cost {
    width: 100%;
    height: 130px;
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    /* padding: 20px; */
    justify-content: space-between;
    align-items: center;
}

.total-cost .cost-1 {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.total-cost .cost-2 {
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.total-cost .reliability {
    width: 49%;
    height: 120px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    margin-right: 10px;
    padding: 25px;
}

.total-cost .thermal {
    width: 49%;
    height: 120px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    padding: 25px;
}

.problems {
    display: flex;
}

.total-problems .problem-addressed {
    width: 100%;
    height: 100%;
    border-radius: 10px;
    background-color: #fff;
    padding: 5px;
}

text {
    font: 25px Roboto, sans-serif !important;
}

.total-problems .energy-waste {
    width: 100%;
    height: 148px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 12px;
    padding: 30px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.reductions {
    display: flex;
}

.reductions .reduction-service {
    width: 49%;
    height: 150px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    margin-right: 10px;
    /* padding: 30px; */
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.reductions .technician {
    width: 49%;
    height: 150px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    padding: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
}

.proactively-addressed {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
}

.problem-approval {
    width: 100%;
    padding: 20px;
    background-color: #fff;
    margin-top: 0;
}

#myChart {
    height: 100%;
    width: 100%;
    min-height: 150px;
}

.zc-ref {
    display: none;
}

.tabs {
    width: 150px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    margin-left: 8px;
}

.tabs p {
    cursor: pointer;
}

.legend {
    list-style-type: none;
}

.legend-item {
    margin: 0px 0px 0px 50px;
    cursor: pointer;
}

.legend-item .legend-marker {
    display: inline-block;
    width: 12px;
    height: 12px;
    vertical-align: middle;
    user-select: none;
    background-color: #5a5a5a;
    border-radius: 50%;
}

div.sticky {
    position: -webkit-sticky;
    position: sticky;
    bottom: -20px;
    // background-color: #fff;
}

p {
    font-weight: 500;
}

.k-chart {
    overflow: auto;
    width: 100%;
}

.k-chart-surface {
    width: 800px;
}

.reductive-data {
    background: #fff;
    /* padding: 10px; */
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
}




table {
    position: relative;
    border-collapse: collapse;
}

th,
td {
    padding: 0.25rem;
}

thead {
    position: sticky;
    top: 0;
    background-color: #fff;
    // border-bottom: 1px solid #888;
    /* box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.4); */
}

thead::after,
thead::before {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
}

thead::before {
    top: 0;
    margin-top: -0.5px;
}

thead::after {
    bottom: 0;
    border-bottom: 1px solid #888;
}

tbody:before {
    /* This doesn't work because of border-collapse */
    line-height: 1em;
    content: ".";
    color: white;
    /* bacground color */
    display: block;
}

[data-title]:hover::after {
    opacity: 1;
    transition: all 0.1s ease 0.5s;
    visibility: visible;
}

[data-guage-title]:hover::after {
    opacity: 1;
    transition: all 0.1s ease 0.5s;
    visibility: visible;
}

[data-guage-title] {
    position: relative;
}

[data-guage-title]:after {
    content: attr(data-guage-title);
    background-color: black;
    color: white;
    font-size: 20px;
    position: absolute;
    padding: 1px 3px 2px 3px;
    bottom: 0em;
    left: 100% !important;
    font-weight: 400;
    white-space: nowrap;
    box-shadow: 1px 1px 3px #222222;
    opacity: 0;
    border: 1px solid #111111;
    z-index: 99999;
    visibility: hidden;
}

[data-title]:after {
    content: attr(data-title);
    background-color: black;
    color: white;
    font-size: 15px;
    position: absolute;
    padding: 1px 3px 2px 3px;
    bottom: 1.5em;
    left: 60%;
    font-weight: 400;
    white-space: nowrap;
    box-shadow: 1px 1px 3px #222222;
    opacity: 0;
    border: 1px solid #111111;
    z-index: 99999;
    visibility: hidden;
}

[data-title] {
    position: relative;
}

//.....................................


.service_call {
    // box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    height: 440px;
}

.service_call_content {
    padding: 2px;
}

.realised_energy {
    width: 55%;
    height: 140px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 0px;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    padding: 14px 12px 0px 13px;
    box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    position: relative;
}

.realised_energy_heading {
    color: rgb(125, 221, 255);
    font-size: 49px;
    font-weight: 600;
    margin-top: 8px;
}

.realised_energy_content {
    font-size: 13px;
    padding: 0px;
    color: #888;
    font-weight: 400;
    position: absolute;
    bottom: 11px;
}

.unrealised_energy {
    width: 55%;
    height: 139px !important;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 0px;
    display: flex;
    flex-direction: column;
    padding: 14px 12px 0px 13px;
    box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    position: relative;
}

.unrealised_energy_heading {
    color: #545454;
    font-size: 49px;
    font-weight: 600;
    margin-top: 8px;
}

.unrealised_energy_content {
    font-size: 13px;
    padding: 0px;
    color: #888;
    font-weight: 400;
    position: absolute;
    bottom: 1em;
}

.equipment_reliability {
    width: 55%;
    height: 140px;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    padding: 14px 12px 0px 13px;
    box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    position: relative;
}

.equipment_reliability_heading {
    color: rgb(125, 221, 255);
    font-size: 49px;
    font-weight: 600;
    margin-top: 8px;
    // margin-left: 10px;
}

.equipment_reliability_content {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    position: absolute;
    bottom: 15px;
}

.thermal_comfort {
    width: 55%;
    height: 139px !important;
    border-radius: 10px;
    background-color: #fff;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    padding: 14px 12px 0px 13px;
    box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    position: relative;
}

.thermal_comfort_heading {
    color: #545454;
    font-size: 49px;
    font-weight: 600;
    margin-top: 8px;
    // margin-left: 10px;
}

.thermal_comfort_content {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    position: absolute;
    bottom: 15px;
}

.reduction_Service_call {
    background: #fff;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
}

.reduction_Service_call_heading {
    color: rgb(175, 171, 171);
    font-size: 49px;
    font-weight: 600;
    margin-left: 20px;
}

.reduction_Service_call_content {
    font-size: 13px;
    color: rgb(175, 171, 171);
    font-weight: 400;
    margin-right: 20px;
}

.technical_footprint {
    background: #fff;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
}

.technical_footprint_heading {
    color: rgb(175, 171, 171);
    font-size: 49px;
    margin-left: 12px;
    font-weight: 600;
    // width: 38%;
}

.technical_footprint_content {
    font-size: 12px;
    margin: 0;
    padding: 0;
    // overflow-wrap: break-word;
    // inline-size: 130px;
    color: rgb(175, 171, 171);
    font-weight: 400;
    margin-right: 20px !important;
}

.energy_waste {
    background: #fff;
    height: 100%;
    display: flex;
    justify-content: space-around !important;
    align-items: center;
    border-radius: 10px;
}

.energy_waste_heading {
    color: #7dddff;
    font-size: 36px;
    margin-left: 12px;
    font-weight: 600;
    // width: 38%;
}

.energy_waste_content {
    font-size: 12px;
    margin: 0;
    padding: 0;
    // overflow-wrap: break-word;
    // inline-size: 126px;
    color: #888;
    font-weight: 400;
    margin-right: 20px !important;
}

.section_1 {
    display: flex;
    margin-bottom: 13px;
}

.section_2 {
    display: flex;
    margin-bottom: 13px;
}

.section_3 {
    display: flex;
    // margin-bottom: 13px;
    margin-right: 0;
}

.table_div {
    // padding: 20px;
    height: 560px;
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap,
:host ::ng-deep .k-input {
    height: calc(8px + 1.75em);
    color: black !important;
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap {
    border-color: #F5F3EF !important;
    border-width: 0 0 1px !important;
}

.opportunities {
    box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
    height: 100%;
}

.opportunity_heading {
    color: #545454;
    font-size: 41px;
    text-align: center;
    font-weight: 600;
}

.opportunity_content {
    font-size: 13px;
    text-align: center;
    color: #888;
    font-weight: 400;
    margin-top: 10px;
}

.unrealised_energy2_heading {
    color: #545454;
    font-size: 41px;
    font-weight: 600;
}

.unrealised_energy2_content {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    margin-top: 10px;
    overflow-wrap: break-word;
    inline-size: auto;
}

.reliability2_heading {
    color: #545454;
    font-size: 41px;
    font-weight: 600;
}

.reliability2_content {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    margin-top: 10px;
    overflow-wrap: break-word;
    inline-size: auto;
}

.thermal_comfort2_heading {
    color: #545454;
    font-size: 41px;
    font-weight: 600;
}

.thermal_comfort2_content {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    margin-top: 10px;
    overflow-wrap: break-word;
    inline-size: auto;
}

:host ::ng-deep svg {
    margin-top: 0px !important;
}

:host ::ng-deep .k-arcgauge-label {
    font-size: 1.8vw !important;
    position: absolute;
    top: 51% !important;
    font-weight: 600;
}

.table_building {
    width: 200px !important;
}

.table_service_call_ID {
    width: 115px !important;
}

.table_equipment {
    width: 120px !important;
}

.table_task_type {
    width: 300px !important;
}

.table_outcome {
    width: 115px !important;
}

.table_thermal_comfort {
    width: 185px !important;
}

.table_equipment_reliability {
    width: 190px !important;
}

.table_energy_waste_cost {
    width: 175px !important;
}

.table_energy_waste_avoidance {
    width: 160px !important;
}

.data-drive-task-summary h2 {
    font-size: 30px !important;
    font-weight: 300 !important;
    color: #545454;
    text-align: center;
}

.disableButton {
    background-color: darkgray !important;
}

@media only screen and (max-width: 562px) {
    .top-text {
        position: relative;
        left: 17%;
        top: 1px;
        margin: 0px;
        font-size: 11px;
    }
}

@media only screen and (max-width:1130px) {
    // .section-top-dashboard {
    //     margin-top: 5% !important;
    // }

    .section-padding {
        padding: 0rem 0;
    }
}

.section-top-dashboard {
    margin-top: 4%;
}

.realised_data {
    display: flex;
}

.section_4 {
    display: flex;
}

@media only screen and (max-width:1093px) {
    .screenScroll {
        overflow: scroll !important;
    }
}

@media only screen and (max-width:1083px) {
    .scrollScreen {
        overflow: scroll !important;
    }
    
}


@media only screen and (max-width:1092px) {
    .serviceCallGraphCss {
        width: 43rem !important;
    }

    .openDebtGraphCssForMobile {
        width: 19rem !important;
    }

    // p {
    //     position: absolute;
    //     left: 78%;
    //     font-size: 9px;
    // }

    .section_1 {
        display: flex;
        flex-direction: column;
    }

    .service_call_data {
        max-width: 100%;
    }

    .realised {
        max-width: 100%;
        margin-top: 10px;
    }

    .realised_data {
        display: flex;
    }

    .total-problems {
        padding-right: 0;
    }

    .total-problems p {
        margin-bottom: 0;
        margin-left: 15px;
        position: absolute;
        top: 10px;
        right: 19px;
        z-index: 999;
        left: 19px;
        font-size: 16px;
    }

    .reduction_data {
        width: 100% !important;
    }

    .section_2 {
        display: flex;
        flex-direction: column;
    }

    .site_comfort_data {
        max-width: 100%;
        padding-right: 0;
    }

    .site_comfort_data p {
        position: absolute;
        left: 20px;
        font-size: 15px;
    }

    .site_comfort_chart {
        margin-top: 40px;
    }

    .HVAC_data {
        padding-right: 0;
        max-width: 100%;
        margin-top: 20px;
    }

    .HVAC_data span {
        position: absolute !important;
        top: 67% !important;
        left: 47% !important;
        font-weight: 600 !important;
        font-size: 20px !important;
    }

    .HVAC_chart {
        height: 350px !important;
    }

    .problem_proactively {
        padding: 0;
        margin-left: 10px;
    }

    .section_4 {
        display: flex;
        flex-direction: column;
    }

    .section_4_data {
        max-width: 100%;
    }

    .section_4_chart {
        max-width: 50%;
        margin-top: 20px;
    }

    .realised_energy_heading {
        color: rgb(125, 221, 255) !important;
        font-size: 49px !important;
        font-weight: 600 !important;
        margin-top: 8px !important;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212) !important;
        font-size: 49px !important;
        font-weight: 600 !important;
        margin-top: 8px !important;
    }

    .unrealised_energy_content {
        font-size: 13px !important;
        padding: 0px !important;
        color: #888 !important;
        font-weight: 400 !important;
        position: absolute !important;
        bottom: 10px !important;
    }

    .realised_energy_content {
        font-size: 13px !important;
        padding: 0px !important;
        color: #888 !important;
        font-weight: 400 !important;
        position: absolute !important;
        bottom: 11px !important;
    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171) !important;
        font-size: 49px !important;
        font-weight: 600 !important;
        margin-left: 20px !important;
    }

    .reduction_Service_call_content {
        font-size: 13px !important;
        color: rgb(175, 171, 171) !important;
        font-weight: 400 !important;
        margin-right: 20px !important;
    }

    .energy_waste_heading {
        color: #7dddff !important;
        font-size: 36px !important;
        margin-left: 6px !important;
    }

    .energy_waste_content {
        font-size: 12px !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-wrap: break-word !important;
        inline-size: 126px !important;
        color: #888 !important;
        font-weight: 400 !important;
    }

}

@media only screen and (max-width: 995px) {
    .table_div {
        height: 600px !important;
    }
}

@media (max-width: 991px) {
    .dashboard-header div[class*='col-'] {
        margin-bottom: 0px;
    }

    .table_div {
        padding: 0 !important;
        height: 600px !important;
    }
}


// @media (min-width: 1130px) and (max-width: 1500px) {
//     .section-top-dashboard {
//         margin-top: 4%;
//     }
// }

@media (min-width: 1466px) and (max-width: 1600px) {
    .realised_energy_heading {
        color: rgb(125, 221, 255);
        font-size: 46px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212);
        font-size: 46px;
        font-weight: 600;
        margin-top: 8px;
    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171) !important;
        font-size: 46px;
        font-weight: 600;
        margin-left: 20px;
    }

    .energy_waste_heading {
        color: #7dddff;
        font-size: 38px !important;
        margin-left: 16px;
        font-weight: 600;
        width: 46%;
    }

    .technical_footprint_heading {
        color: rgb(175, 171, 171);
        font-size: 46px;
        margin-left: 16px;
        font-weight: 600;
        // width: 38%;
    }

    .realised_energy_content {
        font-size: 13px !important;
        margin: 0;
        position: absolute;
        top: 38%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 70%;
    }

    .unrealised_energy_content {
        font-size: 13px !important;
        margin: 0;
        position: absolute;
        top: 37%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 85% !important;
    }

    .digit1 {
        height: 70px !important;
        position: relative !important;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .equipment_reliability_content {
        font-size: 13px !important;
        margin: 0;
        position: absolute;
        top: 38%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 70%;
    }

    .thermal_comfort_content {
        font-size: 13px !important;
        margin: 0;
        position: absolute;
        top: 37%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 87% !important;
    }

    .reduction_Service_call_content {
        line-height: initial !important;
    }

    .technical_footprint_content {
        line-height: initial !important;
    }

    .energy_waste_content {
        line-height: initial !important;
        font-size: 13px !important;
    }

    .opportunity_content {
        line-height: initial !important;
        font-size: 15px !important;
    }

    .unrealised_energy2_content {
        line-height: initial !important;
        font-size: 15px !important;
        color: #888;
        font-weight: 400;
        // margin-top: 11px !important;
        overflow-wrap: break-word !important;
        inline-size: 173px !important;
        margin-left: -5px !important;
    }

    .reliability2_content {
        line-height: initial !important;
        font-size: 15px !important;
    }

    .thermal_comfort2_content {
        line-height: initial !important;
        font-size: 15px !important;
    }

    .equipment_reliability_heading {
        font-size: 46px !important;
    }

    .thermal_comfort_heading {
        font-size: 46px !important;
    }

    .table_building {
        width: 190px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 45px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 13px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 8px !important;
    }

    .last_summary_content {
        margin-top: 14px;
        display: flex;
        flex-direction: column;
    }

}

@media (max-width: 1456px) {

    .realised_energy_heading {
        color: rgb(125, 221, 255);
        font-size: 42px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212);
        font-size: 42px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy {
        width: 55%;
        height: 140px;
        border-radius: 10px;
        background-color: #fff;
        margin-top: -1px;
        display: flex;
        flex-direction: column;
        padding: 14px 12px 0px 13px;
        box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
        position: relative;
    }

    .unrealised_energy_content {
        font-size: 12px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        bottom: 10px;
    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171);
        font-size: 42px;
        font-weight: 600;
        margin-left: 20px;
    }

    .reduction_Service_call_content {
        font-size: 12px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        margin-right: 20px;
    }

    .energy_waste_content {
        font-size: 11px;
        margin: 0;
        padding: 0;
        overflow-wrap: break-word;
        inline-size: 126px;
        color: #888;
        font-weight: 400;
        text-align: center;
    }

    .technical_footprint_content {
        font-size: 11px;
        margin: 0;
        padding: 0;
        overflow-wrap: break-word;
        inline-size: 190px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        text-align: center;
    }

    .energy_waste_heading {
        color: #7dddff;
        font-size: 26px;
        margin-left: 16px;
        font-weight: 600;
        width: 35%;
    }

    .technical_footprint_heading {
        color: rgb(175, 171, 171);
        font-size: 36px;
        margin-left: 16px;
        font-weight: 600;
        // width: 56%;
    }
}

@media (min-width: 1366px) and (max-width: 1466px) {
    .realised_energy_heading {
        color: rgb(125, 221, 255);
        font-size: 38px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212);
        font-size: 38px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy {
        width: 55%;
        height: 139px;
        border-radius: 10px;
        background-color: #fff;
        margin-top: 0px;
        display: flex;
        flex-direction: column;
        padding: 14px 12px 0px 13px;
        box-shadow: 0 8px 20px 0 rgba(218, 224, 235, .6);
        position: relative;
    }

    .unrealised_energy_content {
        font-size: 13px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        /* position: absolute; */
        /* bottom: 10px; */
        margin: 0;
        position: absolute;
        top: 45%;
        transform: translateY(-50%);
        line-height: initial !important;
    }

    .realised_energy_content {
        font-size: 13px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        /* position: absolute; */
        /* bottom: 11px; */
        margin: 0;
        position: absolute;
        top: 45%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 80%;
    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171);
        font-size: 38px;
        font-weight: 600;
        margin-left: 20px;
    }

    .reduction_Service_call_content {
        font-size: 12px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        margin-right: 20px;
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        font-size: 13px !important;
        color: #888 !important;
        font-weight: 400 !important;
        margin-top: -2px !important;
        overflow-wrap: break-word !important;
        inline-size: auto !important;
    }

    .digit1 {
        position: relative;
        height: 70px !important;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .equipment_reliability_content {
        font-size: 13px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        /* position: absolute; */
        /* bottom: 11px; */
        margin: 0;
        position: absolute;
        top: 45%;
        transform: translateY(-50%);
        line-height: initial !important;
        width: 80%;
    }

    .thermal_comfort_content {
        font-size: 13px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        /* position: absolute; */
        /* bottom: 10px; */
        margin: 0;
        position: absolute;
        top: 45%;
        transform: translateY(-50%);
        line-height: initial !important;
    }

    .technical_footprint_content {
        text-align: center !important;
        line-height: initial !important;
        font-size: 12px !important;
    }

    .energy_waste_content {
        line-height: initial !important;
        font-size: 13px !important;
        text-align: left !important;
    }

    .opportunity_content {
        line-height: initial !important;
        font-size: 14px !important;
    }

    .unrealised_energy2_content {
        line-height: initial !important;
        font-size: 14px !important;
        color: #888;
        font-weight: 400;
        margin-top: 11px !important;
        overflow-wrap: break-word !important;
        inline-size: auto !important;
    }

    .reliability2_content {
        line-height: initial !important;
        font-size: 14px !important;
    }

    .thermal_comfort2_content {
        line-height: initial !important;
        font-size: 14px !important;
    }

    .equipment_reliability_heading {
        font-size: 38px !important;
    }

    .thermal_comfort_heading {
        font-size: 38px !important;
    }

    .energy_waste_heading {
        font-size: 34px !important;
    }

    .table_building {
        width: 190px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 38px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 13px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 12px !important;
    }

    .last_summary_content {
        margin-top: 15px;
        display: flex;
        flex-direction: column;
    }
}

@media (min-width: 1250px) and (max-width: 1366px) {
    .realised_energy_heading {
        color: rgb(125, 221, 255);
        font-size: 36px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212);
        font-size: 36px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_content {
        font-size: 12px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        bottom: 10px;
        line-height: initial;
        width: 80% !important;
    }

    .realised_energy_content {
        font-size: 12px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        bottom: 11px;
        width: 65% !important;
        line-height: initial !important;
    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171);
        font-size: 36px;
        font-weight: 600;
        margin-left: 20px;
    }

    .reduction_Service_call_content {
        font-size: 10px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        margin-right: 20px;
        line-height: initial !important;
    }

    .energy_waste_heading {
        color: #7dddff;
        font-size: 32px;
        margin-left: 16px;
        font-weight: 600;
        width: 35%;
    }

    .technical_footprint_heading {
        color: rgb(175, 171, 171);
        font-size: 36px;
        margin-left: 16px;
        font-weight: 600;
        // width: 56%;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .energy_waste_content {
        text-align: left !important;
        line-height: initial !important;
        font-size: 12px !important;
    }

    .technical_footprint_content {
        line-height: initial !important;
    }

    .equipment_reliability_content {
        font-size: 12px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        bottom: 11px;
        width: 65% !important;
        line-height: initial !important;
    }

    .thermal_comfort_content {
        font-size: 12px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        bottom: 10px;
        line-height: initial;
        width: 80% !important;
    }

    .opportunity_content {
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        line-height: initial !important;
        font-size: 13px;
        color: #888;
        font-weight: 400;
        margin-top: 11px !important;
        overflow-wrap: break-word !important;
        inline-size: auto !important;
    }

    .reliability2_content {
        line-height: initial !important;
    }

    .thermal_comfort2_content {
        line-height: initial !important;
    }

    .equipment_reliability_heading {
        font-size: 36px !important;
    }

    .thermal_comfort_heading {
        font-size: 36px !important;
    }

    .table_building {
        width: 190px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 36px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 12px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 13px !important;
    }

    .last_summary_content {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
    }
}

@media (min-width: 1191px) and (max-width: 1250px) {
    .realised_energy_heading {
        color: rgb(125, 221, 255);
        font-size: 35px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_heading {
        // color: rgb(170, 114, 212);
        font-size: 35px;
        font-weight: 600;
        margin-top: 8px;
    }

    .unrealised_energy_content {
        font-size: 11px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        // bottom: 10px;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        line-height: initial !important;
    }

    .realised_energy_content {
        font-size: 11px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        // bottom: 11px;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        line-height: initial !important;
        margin-right: 10px !important;
        width: 80%;

    }

    .reduction_Service_call_heading {
        color: rgb(175, 171, 171);
        font-size: 35px;
        font-weight: 600;
        margin-left: 10px !important;
    }

    .reduction_Service_call_content {
        font-size: 11px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        margin-left: 12px;
        line-height: initial !important;
    }

    .technical_footprint_content {
        font-size: 11px;
        margin: 0;
        padding: 0;
        // overflow-wrap: break-word;
        // inline-size: 87px;
        color: rgb(175, 171, 171);
        font-weight: 400;
        width: auto;
        margin-right: 5px !important;
        line-height: initial !important;
    }

    .technical_footprint_heading {
        color: rgb(175, 171, 171) !important;
        font-size: 35px !important;
        margin-left: 12px !important;
        font-weight: 600 !important;
        width: auto !important;
    }

    .digit1 {
        position: relative;
        height: 70px !important;
    }

    // .unrealised_energy {
    //     margin-top: -1px !important;
    // }

    .equipment_reliability_content {
        font-size: 11px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        // bottom: 11px;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        line-height: initial !important;
        margin-right: 10px !important;
        width: 80%;
    }

    .thermal_comfort_content {
        font-size: 11px;
        padding: 0px;
        color: #888;
        font-weight: 400;
        position: absolute;
        // bottom: 10px;
        margin: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        line-height: initial !important;
    }

    // .break{
    //     display: none !important;
    // }
    .energy_waste_content {
        font-size: 12px;
        margin: 0;
        padding: 0;
        // overflow-wrap: break-word;
        // inline-size: 87px;
        color: #888;
        font-weight: 400;
        width: auto;
        margin-right: 10px !important;
        text-align: left !important;
        line-height: initial !important;
    }

    .energy_waste_heading {
        color: #7dddff !important;
        font-size: 32px !important;
        margin-left: 12px !important;
        font-weight: 600 !important;
        width: auto !important;
    }

    .opportunity_content {
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        line-height: initial !important;
        font-size: 13px;
        color: #888;
        font-weight: 400;
        margin-top: 11px !important;
        overflow-wrap: break-word !important;
        inline-size: 125px !important;
    }

    .reliability2_content {
        line-height: initial !important;
    }

    .thermal_comfort2_content {
        line-height: initial !important;
    }

    .equipment_reliability_heading {
        font-size: 35px !important;
    }

    .thermal_comfort_heading {
        font-size: 35px !important;
    }

    .table_building {
        width: 200px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 35px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 11px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 13px !important;
    }

    .last_summary_content {
        margin-top: 17px;
        display: flex;
        flex-direction: column;
    }
}

@media only screen and (min-width: 1801px) and (max-width: 1920px) {
    .dashboard-header {
        margin-left: 330px;
        height: calc(100vh - 145px);
    }
    .section-top-dashboard {
        margin-top: 5.4%;
    }
    .backlog-ew {
        height: 165px !important;
    }
    .technical_footprint_content {
        font-size: 14px;
        width: 40%;
        margin-right: 5px !important;
        line-height: initial !important;
    }

    .energy_waste_content {
        font-size: 18px !important;
        width: 40%;
        margin-right: 5px !important;
        line-height: initial !important;

    }

    .realised_energy_content {
        font-size: 18px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        position: absolute;
        bottom: 10px;
        margin-left: -3px !important;
        width: 85%;
    }

    .unrealised_energy_content {
        font-size: 18px !important;
        position: absolute;
        bottom: 10px;
        line-height: initial !important;
        margin-left: -4px !important;
    }

    .equipment_reliability_content {
        font-size: 18px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        position: absolute;
        bottom: 10px;
        margin-left: -3px !important;
        width: 90%;
    }

    .thermal_comfort_content {
        font-size: 18px !important;
        position: absolute;
        bottom: 10px;
        line-height: initial !important;
        margin-left: -4px !important;
    }

    .reduction_Service_call_heading {
        width: 50%;
    }

    .reduction_Service_call_content {
        font-size: 15px !important;
        width: 50%;
        margin-left: 15px;
        line-height: initial !important;
    }

    .opportunity_content {
        font-size: 17px !important;
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        font-size: 17px !important;
        line-height: initial !important;
    }

    .reliability2_content {
        font-size: 17px !important;
        line-height: initial !important;
    }

    .thermal_comfort2_content {
        font-size: 17px;
        line-height: initial !important;
    }

    .performance_data {
        width: 98.5% !important;
    }

    .break {
        display: none !important;
    }

    .technical_footprint {
        display: flex;
        justify-content: space-between;
    }

    .technical_footprint_heading {
        font-size: 49px !important;
        // width: 60%;
    }

    .energy_waste {
        display: flex;
        justify-content: space-between;
    }

    .energy_waste_heading {
        font-size: 40px !important;
        // width: 60%;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .table_building {
        width: 190px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 49px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 18px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 5px !important;
    }

    .last_summary_content {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
    }
    .imp-ew{
        font-size: 2.4vw !important;
    }
}

@media only screen and (min-width: 1601px) and (max-width: 1800px) {
    .dashboard-header {
        margin-left: 295px;
        height: calc(100vh - 125px);
    }
    .section-top-dashboard {
        margin-top: 4.9%;
    }
    .backlog-ew {
        height: 158px !important;
    }
    .realised_energy_content {
        margin-right: 10px;
        line-height: initial;
        position: absolute;
        bottom: 11px;
        margin-left: -4px;
        font-size: 17px !important;
    }

    .performance_data {
        width: 98.5% !important;
    }

    .unrealised_energy_content {
        position: absolute !important;
        bottom: 11px !important;
        margin-right: 4px !important;
        line-height: initial;
        margin-left: -4px !important;
        font-size: 17px !important;
    }

    .equipment_reliability_content {
        margin-right: 10px;
        line-height: initial;
        position: absolute;
        bottom: 11px;
        margin-left: -4px;
        font-size: 17px !important;
    }

    .thermal_comfort_content {
        position: absolute !important;
        bottom: 11px !important;
        margin-right: 4px !important;
        line-height: initial;
        margin-left: -4px !important;
        font-size: 17px !important;
    }

    .break {
        display: none !important;
    }

    // .technical_footprint_heading {
    //     width: 60% !important;
    // }

    .technical_footprint_content {
        width: 40% !important;
        margin-right: 8px !important;
        line-height: initial !important;
    }

    .energy_waste_heading {
        // width: 60% !important;
        font-size: 40px !important;
    }

    .energy_waste_content {
        width: 40% !important;
        margin-right: 8px !important;
        line-height: initial !important;
        font-size: 17px !important;
    }

    .reduction_Service_call_content {
        margin-left: 15px !important;
        line-height: initial !important;
    }

    .opportunity_content {
        font-size: 15px !important;
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        font-size: 15px !important;
        line-height: initial !important;
    }

    .reliability2_content {
        font-size: 15px !important;
        line-height: initial !important;
        margin-right: 2vw !important;
    }

    .thermal_comfort2_content {
        font-size: 15px !important;
        line-height: initial !important;
        margin-right: 2vw !important;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .table_building {
        width: 190px !important;
    }

    .table_service_call_ID {
        width: 115px !important;
    }

    .table_equipment {
        width: 120px !important;
    }

    .table_task_type {
        width: 300px !important;
    }

    .table_outcome {
        width: 115px !important;
    }

    .table_thermal_comfort {
        width: 185px !important;
    }

    .table_equipment_reliability {
        width: 190px !important;
    }

    .table_energy_waste_cost {
        width: 175px !important;
    }

    .table_energy_waste_avoidance {
        width: 160px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 45px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 15px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 8px !important;
    }

    .last_summary_content {
        margin-top: 13px;
        display: flex;
        flex-direction: column;
    }
}

@media only screen and (min-width: 1921px) and (max-width: 2115px) {
    .dashboard-header {
        margin-left: 220px; 
    }
    .realised_energy_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 15px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        font-size: 19px !important;
        width: 80% !important;
    }

    .performance_data {
        width: 98.5% !important;
    }

    .unrealised_energy_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 15px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        font-size: 19px !important;
    }

    .equipment_reliability_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 15px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        font-size: 19px !important;
        width: 80% !important;
    }

    .thermal_comfort_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 15px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        font-size: 19px !important;
    }

    .break {
        display: none;
    }

    .reduction_Service_call_content {
        margin-left: 15px !important;
        font-size: 15px !important;
        line-height: initial !important;
    }

    // .technical_footprint_heading {
    //     width: 60% !important;
    // }

    .technical_footprint_content {
        width: 40% !important;
        margin-right: 10px !important;
        font-size: 14px !important;
        line-height: initial !important;
    }

    .energy_waste_heading {
        // width: 60% !important;
        font-size: 42px !important;
    }

    .energy_waste_content {
        width: 40% !important;
        margin-right: 10px !important;
        font-size: 19px !important;
        line-height: initial !important;
    }

    .opportunity_content {
        font-size: 17px !important;
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        font-size: 17px !important;
        line-height: initial !important;
    }

    .reliability2_content {
        font-size: 17px !important;
        line-height: initial !important;
        margin-right: 2vw !important;
    }

    .thermal_comfort2_content {
        font-size: 17px !important;
        line-height: initial !important;
        margin-right: 2vw !important;
    }

    .unrealised_energy {
        margin-top: -1px !important;
    }

    .data-drive-task-summary h2 {
        font-size: 49px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 19px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 5px !important;
    }

    .last_summary_content {
        margin-top: 9px;
        display: flex;
        flex-direction: column;
    }

    .imp-ew-container {
        gap: 2px !important;
    }

    .imp-ew{
        font-size: 2.2vw !important;
    }
}

@media only screen and (min-width: 2115px) {
    .dashboard-header {
        margin-left: 220px;
    }
    .realised_energy_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 20px !important;
        margin-right: 2vw !important;
        line-height: initial !important;
        width: 70% !important;
    }

    .performance_data {
        width: 98.5% !important;
    }

    .unrealised_energy_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 20px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        width: 80%;
    }

    .equipment_reliability_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 20px !important;
        margin-right: 2vw !important;
        line-height: initial !important;
        width: 70% !important;
    }

    .thermal_comfort_content {
        position: absolute !important;
        bottom: 10px !important;
        font-size: 20px !important;
        margin-right: 10px !important;
        line-height: initial !important;
        width: 80%;
    }

    .break {
        display: none;
    }

    .reduction_Service_call_content {
        margin-left: 15px !important;
        font-size: 17px !important;
        line-height: initial !important;
    }

    // .technical_footprint_heading {
    //     width: 60% !important;
    // }

    .technical_footprint_content {
        width: 40% !important;
        margin-right: 10px !important;
        font-size: 16px !important;
        line-height: initial !important;
    }

    .energy_waste_heading {
        // width: 60% !important;
        font-size: 45px !important;
    }

    .energy_waste_content {
        width: 40% !important;
        margin-right: 10px !important;
        font-size: 20px !important;
        line-height: initial !important;
    }

    .opportunity_content {
        font-size: 19px !important;
        line-height: initial !important;
    }

    .unrealised_energy2_content {
        font-size: 19px !important;
        line-height: initial !important;
    }

    .reliability2_content {
        font-size: 19px !important;
        margin-right: 4vw !important;
        line-height: initial !important;
    }

    .thermal_comfort2_content {
        font-size: 19px !important;
        line-height: initial !important;
        margin-right: 3vw !important;
    }

    .data-drive-task-summary h2 {
        font-size: 52px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .data-drive-task-summary span {
        font-size: 20px !important;
        font-weight: 400 !important;
        color: #888;
        line-height: 1;
    }

    .last_summary {
        margin-top: 5px !important;
    }

    .last_summary_content {
        margin-top: -1px;
        display: flex;
        flex-direction: column;
    }

    .imp-ew-container {
        gap: 2px !important;
    }
    
    .imp-ew {
        font-size: 2.2vw !important;;
    }
}

td {
    border-bottom: .5px solid rgb(208, 208, 208);
    border-collapse: collapse;
}

// @media (min-width: 2100px) {
//     .section-top-dashboard {
//         margin-top: 8%;
//     }
// }

// @media (min-width: 1501px) and (max-width: 1700px) {
//     .section-top-dashboard {
//         margin-top: 145px !important;
//     }
// }

// @media (min-width: 1701px) and (max-width: 1900px) {
//     .section-top-dashboard {
//         margin-top: 150px !important;
//     }
// }

// @media (min-width: 1901px) and (max-width: 2100px) {
//     .section-top-dashboard {
//         margin-top: 156px !important;
//     }
// }

// @media (min-width: 2101px) and (max-width: 2300px) {
//     .section-top-dashboard {
//         margin-top: 159px !important;
//     }
// }

// @media (min-width: 2301px) {
//     .section-top-dashboard {
//         margin-top: 163px !important;
//     }
// }

:host ::ng-deep kendo-radialgauge text {
    font: 20px Roboto !important;
    // color: red !important;
    stroke: none !important;
    fill: #545454 !important;
    font-weight: bolder !important;
}

:host ::ng-deep .k-grid th {
    background-color: #F7F7F7 !important;
    font-size: 16px !important;
    // border-right: 1px solid lightgray !important;
}

:host ::ng-deep .k-grid-header .k-filterable>.k-link {
    color: black !important;
}

:host ::ng-deep .k-grid td {
    padding: 10px 12px !important;
    font-size: 14px !important;
}

:host ::ng-deep .k-i-filter {
    color: black !important;
}

:host ::ng-deep .k-grid-header-wrap {
    // border-right: 1px solid lightgray !important;
    // border-left: 1px solid lightgray !important;
    border: 1px solid rgba(0, 0, 0, 0.12) !important;
    border-radius: 8px 8px 0 0 !important;
    // border-bottom-width: 0 !important;
}

:host ::ng-deep kendo-grid {
    border: none !important;
}

// :host ::ng-deep .k-grid th:first-child{
//     border-left: 1px solid lightgray !important;
// }

:host ::ng-deep .k-grid tr.k-alt {
    background-color: #fff !important;
}

:host ::ng-deep .k-grid table tr.k-alt:hover {
    background-color: rgba(0, 0, 0, 0.07) !important;
}

:host ::ng-deep .k-grid tr.k-alt.k-state-selected {
    color: #000 !important;
    background-color: #7E9DEB !important;
}

.sparkUrl:hover {
    color: blue !important;
    text-decoration: underline !important;
}

.ag-courses_box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;

    padding: 50px 0;
}

.ag-courses_item {
    -ms-flex-preferred-size: calc(33.33333% - 30px);
    flex-basis: calc(33.33333% - 30px);
    border-radius: 28px;
    cursor: pointer !important;
}

.ag-courses-item_link {
    display: block;
    // padding: 15px 20px;
    /* background-color: #121212; */
    overflow: hidden;
    position: relative;
    text-align: center;
}

.ag-courses-item_link:hover,
.ag-courses-item_link:hover .ag-courses-item_date {
    text-decoration: none;
    color: #888;
}

/* .ag-courses-item_link:hover .ag-courses-item_bg {
    -webkit-transform: scale(10);
    -ms-transform: scale(10);
    transform: scale(10);
} */

.ag-courses-item_title {
    overflow: hidden;
    font-weight: bold;
    font-size: 2.2vw;
    color: #000;
    z-index: 2;
    position: relative;
}

.ag-courses-item_date-box {
    font-size: 0.9vw;
    color: #FFF;
    z-index: 2;
    position: relative;
}

.ag-courses-item_date {
    font-weight: 500;
    color: #888;

    -webkit-transition: color .5s ease;
    -o-transition: color .5s ease;
    transition: color .5s ease;
    // text-align: right;
}

.ag-courses-item_bg {
    height: 128px;
    width: 128px;
    background-color: #0291d5;

    z-index: 1;
    position: absolute;
    top: -75px;
    right: -75px;

    border-radius: 50%;

    -webkit-transition: all .5s ease;
    -o-transition: all .5s ease;
    transition: all .5s ease;
}




@media only screen and (max-width: 979px) {
    .ag-courses_item {
        -ms-flex-preferred-size: calc(50% - 30px);
        flex-basis: calc(50% - 30px);
    }

    .ag-courses-item_title {
        font-size: 24px;
    }
}

@media only screen and (max-width: 767px) {
    .ag-format-container {
        width: 96%;
    }

    .total-problems {
        margin-left: -1.2vw;
        padding-left: 0;
        margin-top: 3vh;
    }

}

@media only screen and (max-width: 639px) {
    .ag-courses_item {
        -ms-flex-preferred-size: 100%;
        flex-basis: 100%;
    }

    .ag-courses-item_title {
        min-height: 72px;
        line-height: 1;

        font-size: 24px;
    }

    .ag-courses-item_link {
        padding: 22px 40px;
    }

    .ag-courses-item_date-box {
        font-size: 16px;
    }
}

.card-div {
    max-width: 19.7%;
}

.main-widget {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap !important;
    padding-left: 0;
    justify-content: space-between;
}

.widgets {
    width: 100%;
    // height: 100px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
}

.widgets h2 {
    font-size: 2.2vw;
    color: #545454;
    font-weight: 300;
}

.widgets span {
    font-size: 0.9vw;
    color: #888;
    margin-top: -7px;
}

.co2Title {
    font-size: 1.7vw !important;
}

.co2-main-div {
    margin-top: 7px;
}


@media (min-width: 1603px) and (max-width: 1818px) {
    .co2Title {
        font-size: 1.6vw !important;
    }

    .co2-main-div {
        margin-top: 10px !important;
    }
}

@media (min-width: 1566px) and (max-width: 1603px) {
    .co2Title {
        font-size: 1.58vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1483px) and (max-width: 1566px) {
    .co2Title {
        font-size: 1.53vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1435px) and (max-width: 1483px) {
    .co2Title {
        font-size: 1.50vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1405px) and (max-width: 1435px) {
    .co2Title {
        font-size: 1.48vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1376px) and (max-width: 1405px) {
    .co2Title {
        font-size: 1.46vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1350px) and (max-width: 1376px) {
    .co2Title {
        font-size: 1.44vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1311px) and (max-width: 1350px) {
    .co2Title {
        font-size: 1.41vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1278px) and (max-width: 1311px) {
    .co2Title {
        font-size: 1.38vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}

@media (min-width: 1250px) and (max-width: 1278px) {
    .co2Title {
        font-size: 1.36vw !important;
    }

    .co2-main-div {
        margin-top: 8px !important;
    }
}


.co2-content {
    margin-top: 7px !important;
}

.HVAC-main-div {
    height: 394px;
    width: 80%;
    margin: auto;
}

.radial-chart {
    display: block;
    height: 370px;
    margin-top: 30px;
}

@media (min-width: 1190px) and (max-width: 2001px) {
    .HVAC-main-div {
        height: 250px;
        margin: auto;
    }

    .radial-chart {
        display: block;
        height: 250px;
        margin-top: 0px;
    }
}

@media (min-width: 2001px) {
    .HVAC-main-div {
        height: 300px;
        margin: auto;
    }

    .radial-chart {
        display: block;
        height: 300px;
        margin-top: 0px;
    }
}

.HVAC-count {
    position: absolute;
    top: 40%;
    left: 50%;
    -moz-transform: translateX(-50%) translateY(-50%);
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(50%);
}

@media (min-width: 2481px) {
    .HVAC-count {
        position: absolute;
        top: 33%;
        left: 49%;
        -moz-transform: translateX(-50%) translateY(-50%);
        -webkit-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(50%);
    }
}

@media (min-width: 1801px) and (max-width: 2001px) {
    .HVAC-count {
        position: absolute;
        top: 36%;
        left: 50%;
        -moz-transform: translateX(-50%) translateY(-50%);
        -webkit-transform: translateX(-50%) translateY(-50%);
        transform: translateX(-50%) translateY(50%);
    }
}

@media (min-width: 1191px) and (max-width: 1251px) {
    .im-count {
        max-width: 62px !important;
        align-items: center;
        margin-right: 21px;
    }

    .quote-req {
        // margin-right: -20px !important;
    }

    .expired {
        // margin-left: 7px;
        margin-left: 0px;
        margin-bottom: 11px;
    }

    .quote-not-accepted {
        // margin-left: 7px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 35px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 27px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 9px !important;
        width: 200px;
        text-align: center;
    }
}

@media (min-width: 1251px) and (max-width: 1368px) {
    .im-count {
        max-width: 65px !important;
        align-items: center;
        margin-right: 20px;
    }

    .quote-req {
        // margin-right: -24px !important;
    }

    .expired {
        // margin-left: 8px;
        margin-left: 0px;
        margin-bottom: 12px;
    }

    .quote-not-accepted {
        // margin-left: 8px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 36px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 24px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 10px !important;
        width: 200px;
        text-align: center;
    }
}

@media (min-width: 1368px) and (max-width: 1454px) {
    .dashboard-header {
        margin-left: 245px;
        height: calc(100vh - 102px);
    }

    .section-top-dashboard {
        margin-top: 4.3%;
    }

    .backlog-ew {
        height: 130px !important;
    }

    .im-count {
        max-width: 68px !important;
        align-items: center;
        margin-right: 24px;
    }

    .quote-req {
        // margin-right: -29px !important;
    }

    .expired {
        // margin-left: 8px;
        margin-left: 0px;
        margin-bottom: 12px;
    }

    .quote-not-accepted {
        // margin-left: 8px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 38px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 32px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 14px !important;
        width: 200px;
        text-align: center;
    }
}

@media (min-width: 1454px) and (max-width: 1603px) {
    .dashboard-header {
        margin-left: 275px;
        height: calc(100vh - 111.5px);
    }

    .section-top-dashboard {
        margin-top: 4.4%;
    }

    .backlog-ew {
        height: 150px !important;
    }

    .im-count {
        max-width: 75px !important;
        align-items: center;
        margin-right: 24px;
    }

    .quote-req {
        // margin-right: -29px !important;
    }

    .expired {
        // margin-left: 8px;
        margin-left: 0px;
        margin-bottom: 12px;
    }

    .quote-not-accepted {
        // margin-left: 8px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 45px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 28px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 18px !important;
        width: 200px;
        text-align: center;
    }

    .donutChart {
        height: 270px;
    }
}

@media (min-width: 1603px) and (max-width: 1817px) {
    .im-count {
        max-width: 85px !important;
        align-items: center;
        margin-right: 24px;
    }

    .quote-req {
        // margin-right: -31px !important;
    }

    .expired {
        // margin-left: 10px;
        margin-left: 0px;
        margin-bottom: 13px;
    }

    .quote-not-accepted {
        // margin-left: 10px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 45px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 33px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 22px !important;
        width: 200px;
        text-align: center;
    }

    .donutChart {
        height: 250px;
    }

    .backlogTasksHeader {
        margin-bottom: 3vh;
    }
}

@media (min-width: 1817px) and (max-width: 1966px) {
    .im-count {
        max-width: 92px !important;
        align-items: center;
        margin-right: 25px;
    }

    .quote-req {
        // margin-right: -38px !important;
    }

    .expired {
        // margin-left: 12px;
        margin-left: 0px;
        margin-bottom: 15px;
    }

    .quote-not-accepted {
        // margin-left: 12px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 49px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 49px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 26px !important;
        width: 200px;
        text-align: center;
    }

    .donutChart {
        height: 250px;
    }

    .backlogTasksHeader {
        margin-bottom: 2vh;
    }
}

@media (min-width: 1966px) and (max-width: 2136px) {
    .im-count {
        max-width: 100px !important;
        align-items: center;
        margin-right: 27px;
    }

    .quote-req {
        // margin-right: -42px !important;
    }

    .expired {
        // margin-left: 12px;
        margin-left: 0px;
        margin-bottom: 17px;
    }

    .quote-not-accepted {
        // margin-left: 12px;
        margin-left: 0px;
    }

    .widgets h2 {
        font-size: 49px;
        color: #545454;
        font-weight: 300;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 44px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 30px !important;
        width: 200px;
        text-align: center;
    }

    .donutChart {
        height: 250px;
    }

    .backlogTasksHeader {
        margin-bottom: 1vh;
    }
}

@media (min-width: 2136px) and (max-width: 2351px) {
    .im-count {
        max-width: 110px !important;
        align-items: center;
        margin-right: 28px;
    }

    .quote-req {
        // margin-right: -45px !important;
    }

    .expired {
        // margin-left: 12px;
        margin-left: 0px;
        margin-bottom: 19px;
    }

    .quote-not-accepted {
        // margin-left: 12px;
        margin-left: 0px;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 49px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 36px !important;
        width: 200px;
        text-align: center;
    }
}

@media (min-width: 2351px) and (max-width: 2561px) {
    .im-count {
        max-width: 120px !important;
        align-items: center;
        margin-right: 30px;
    }

    .quote-req {
        // margin-right: -51px !important;
    }

    .expired {
        // margin-left: 13px;
        margin-left: 0px;
        margin-bottom: 21px;
    }

    .quote-not-accepted {
        // margin-left: 13px;
        margin-left: 0px;
    }

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 49px;
        color: #000;
        z-index: 2;
        position: relative;
    }

    .awaiting-approval-text {
        margin-left: 40px !important;
        width: 200px;
        text-align: center;
    }
}

@media only screen and (min-width: 2136px) {
    .widgets h2 {
        font-size: 52px !important;
        font-weight: 300 !important;
        color: #545454;
    }

    .backlogTasksHeader {
        margin-bottom: 1vh;
    }

    .donutChart {
        height: 170px;
    }
}

@media only screen and (min-width: 2561px) {

    .ag-courses-item_title {
        overflow: hidden;
        font-weight: bold;
        font-size: 52px;
        color: #000;
        z-index: 2;
        position: relative;
    }
}

.thermal-0 {
    color: #26d535;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #d8f2d0;
    width: 50%;
    font-size: 14px;
}

.thermal-1 {
    color: #26d535;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #d8f2d0;
    width: 50%;
    font-size: 14px;
}

.thermal-2 {
    color: #26d535;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #d8f2d0;
    width: 50%;
    font-size: 14px;
}

.thermal-3 {
    color: #26d535;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #d8f2d0;
    width: 50%;
    font-size: 14px;
}

.thermal-4 {
    color: #f5d338;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #ffffcd;
    width: 50%;
    font-size: 14px;
}

.thermal-5 {
    color: #f5d338;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #ffffcd;
    width: 50%;
    font-size: 14px;
}

.thermal-6 {
    color: #f5d338;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #ffffcd;
    width: 50%;
    font-size: 14px;
}

.thermal-7 {
    color: #e1211e;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #fae4d7;
    width: 50%;
    font-size: 14px;
}

.thermal-8 {
    color: #e1211e;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #fae4d7;
    width: 50%;
    font-size: 14px;
}

.thermal-9 {
    color: #e1211e;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #fae4d7;
    width: 50%;
    font-size: 14px;
}

.thermal-10 {
    color: #e1211e;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #fae4d7;
    width: 50%;
    font-size: 14px;
}

:host ::ng-deep #Backlog-list tr.gray {
    background-color: #fff !important;
}

:host ::ng-deep #Backlog-list tr.white {
    background-color: inherit !important;
}

:host ::ng-deep #Backlog-list tr.gray:hover {
    background-color: rgba(0, 0, 0, 0.07) !important;
}

:host ::ng-deep #Backlog-list tr.white:hover {
    background-color: rgba(0, 0, 0, 0.07) !important;
}

:host ::ng-deep #Backlog-list tr.gray.k-state-selected {
    color: #000 !important;
    background-color: #7E9DEB !important;
}

:host ::ng-deep #Backlog-list tr.white.k-state-selected {
    color: #000 !important;
    background-color: #7E9DEB !important;
}

.IM-task-element {
    background-color: #ecf0f3;
    box-shadow:
        inset 2px 2px 4px #d1d9e6,
        inset -2px -2px 4px #f9f9f9;
    border: none;
    border-radius: 5px;
}

.selected {
    background-color: #ecf0f3;
    box-shadow:
        inset 2px 2px 4px #d1d9e6,
        inset -2px -2px 4px #f9f9f9;
    border: none;
    border-radius: 5px;
}

.widgets {
    cursor: pointer;
}

.widgets h2:hover {
    cursor: pointer;
    text-decoration: underline;
    text-underline-offset: 0.1em;
    text-decoration-thickness: 2px;
    font-weight: 600;
}

.selected h2 {
    text-decoration: underline;
    text-underline-offset: 0.1em;
    text-decoration-thickness: 2px;
}

:host ::ng-deep .k-grid th {
    padding: 16px 24px;
    border-width: 0 0 1px;
    white-space: normal;
    background: #343A40;
    vertical-align: middle;
    color: black;
}

// :host ::ng-deep .k-grid td:first-child, .k-grid th:first-child {
//     border-left-width: 1px !important;
// }

// :host ::ng-deep .k-grid td:last-child, .k-grid th:last-child {
//     border-right-width: 1px !important;
// }
:host ::ng-deep .k-grid-toolbar {
    border: none !important;
}

:host ::ng-deep .k-grid-header {
    border-bottom-width: 0 !important;
}

:host ::ng-deep .k-grid-content {
    border: 1px solid rgba(0, 0, 0, 0.12) !important;
    border-top-width: 0 !important;
}

:host ::ng-deep .k-grid-container {
    width: 99.7% !important;
}

:host ::ng-deep .awaiting-grid .k-grid-container {
    width: 99.6% !important;
}

.parent_customer_div:hover .ratingValue {
    width: 100px;
    border: 1px solid lightgray;
    background-color: #fff;
    border-radius: 5px;
    padding-left: 10px;
}

.parent_customer_div:hover {
    padding-left: 0 !important;
}

:host ::ng-deep .outcomeDrop .k-input {
    color: black !important;
    font-size: 13px !important;
    margin-left: 5px;
}

:host ::ng-deep .outcomeDrop .k-select .k-icon {
    color: black !important;
}

.button-disabled {
    background-color: lightgray !important;
}

.button-enabled {
    background-color: #00ccff !important;
}

:host ::ng-deep #container3 text {
    font: 13px "Roboto" !important;
}


priority-container {
    display: flex;
    flex-direction: column;
    // padding: 10px;
}

.box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    height: 60px;
}

.priority-icon {
    width: 40px;
    height: 100%;
}

.priority {
    padding: 5px 10px;
    border-radius: 14px;
    cursor: move;
}

.high {
    display: flex;
    align-items: center;
    border: 1px solid #ff000078;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-evenly;
}

.medium {
    display: flex;
    align-items: center;
    border: 1px solid #ffbf00;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-between;
}

.low {
    display: flex;
    align-items: center;
    border: 1px solid #4dff4d;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-evenly;
}

.high-dot {
    height: 11px;
    width: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.medium-dot {
    height: 11px;
    width: 10px;
    background-color: #ffbf00;
    border-radius: 50%;
    display: inline-block;
    margin-right: 3px;
}

.low-dot {
    height: 11px;
    width: 10px;
    background-color: #4dff4d;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.equipment-container {
    padding: 10px;
    border-radius: 5px;
}

.equipment-container h2 {
    margin-top: 0;
}

.equipment-container input[type="text"] {
    width: 100%;
    padding: 5px;
    margin-bottom: 10px;
    box-sizing: border-box;
    margin-left: -10px;
    border: 1px solid lightgray;
}

.equipment-list {
    margin-bottom: 20px;
}

.equipment-list label {
    display: block;
    margin-bottom: 5px;
    position: relative;
}

.select-all hr {
    width: 10px;
    position: absolute;
    top: -10px;
    left: -10px;
    height: 25px;
    border-right: 0px !important;
    border-left: 1px solid lightgray;
    border-top: 1px solid lightgray;
}

.equipment-list hr {
    width: 10px;
    position: absolute;
    top: -10px;
    left: -10px;
    height: 23px;
    border-right: 0px !important;
    border-left: 1px solid lightgray;
    border-top: 1px solid lightgray;
}

.equip-buttons {
    display: flex;
    justify-content: end;
}

.equip-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.equip-buttons button:first-child {
    margin-right: 10px;
    border: 1px solid lightgray;
    background: #fff;
}

.equip-buttons button:last-child {
    border: 1px solid lightgray;
    background: #fff;
}

.select-all label {
    position: relative;
}

.equipment-list label:last-child hr {
    height: 0;
}

:host ::ng-deep .kendo-grid tr.highlight-red-row {
    color: red !important;
    font-weight: bold !important;
}

// .highlight-red-row {
//     // background-color: red !important;
//     // color: white !important;
//     color: red !important;
//     font-weight: bold !important;
// }

.half-donut-chart {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 200px;
    margin: 0 auto;
}

.highcharts-xaxis-labels>text {
    font-weight: bold !important;
}

:host ::ng-deep .k-grid-header .k-header>.k-link {
    padding-left: 10px !important;
    font-family: 'Roboto' !important;
}

:host ::ng-deep .k-grid-header th> {
    font-family: 'Roboto' !important;
}


:host ::ng-deep .k-window-content {
    overflow: hidden;
}

:host ::ng-deep .custom-dialog .k-dialog-wrapper .k-dialog {
    width: 49%;
    position: relative;
    height: 84vh;
}

:host ::ng-deep .custom-dialog .k-button .k-icon,
.k-button .k-image,
.k-button .k-sprite {
    color: black !important;
}

::ng-deep .highcharts-scrolling {
    scrollbar-width: none;
}
.taskCompleted, .taskBacklogged {
    font-family: Calibri Light !important;
    letter-spacing: 0.3px;
    cursor: pointer;
    color: black;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: rgba(0, 176, 240, 0.2);
    padding: 5px 10px;
    font-size: 14px;
}

.taskCompleted:hover, .taskBacklogged:hover {
    background-color: rgba(0, 176, 240, 0.5) !important;
}