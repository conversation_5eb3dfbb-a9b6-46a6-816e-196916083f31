import { Component, OnInit } from '@angular/core';
import { CoreDataService } from './../../Services/core-data.service';
import { SharedDataService } from '../../Services/shared-data.service';
import { TermsAndConditionsModel } from "./footer.model";
import { SessionStorageService } from 'ngx-webstorage';

@Component({
  selector: 'admin-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit {
  TermsAndConditionData: TermsAndConditionsModel = new TermsAndConditionsModel();
  yearVal: string = "";
  constructor(private coreData: CoreDataService, private sharedDataService: SharedDataService, private sessionStorage: SessionStorageService) {

    var currentYear = new Date();
    this.yearVal = (currentYear.getFullYear() - 1).toString() + '-' + currentYear.getFullYear().toString();
  }
  ngOnInit() {
    let sessionLink = this.sessionStorage.retrieve('TermsAndConditionData');
    if (sessionLink === null) {
      this.loadTermConditionsPrivacyPolicyUrls();
    }
    else if (sessionLink != null) {
      // this.loadUrlStoreInSession();
      this.TermsAndConditionData = sessionLink;
    }
  }

  loadTermConditionsPrivacyPolicyUrls() {
    // debugger;
    this.coreData.getPrivacyPolicyTermCondition().subscribe(response => {
      if (response != null && response != undefined) {
        if (response.StatusCode === 200) {
          this.TermsAndConditionData = response.response;
          this.sessionStorage.store('TermsAndConditionData', this.TermsAndConditionData)
        }
      }
    },
      catchError => {
        if (catchError) {
          this.sharedDataService.ErrorHandler(catchError);
        }
      }
    );

  }



}
