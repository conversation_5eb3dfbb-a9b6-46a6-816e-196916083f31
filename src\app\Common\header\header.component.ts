import { Component, OnInit, Output, EventEmitter, ViewEncapsulation, OnDestroy } from '@angular/core';
import { AuthenticationService } from "../../Services/authentication.service";
import { ActivatedRoute } from "@angular/router";
import { Title } from "@angular/platform-browser";
import { Router } from '@angular/router';
import { SessionStorageService } from 'ngx-webstorage';
import { CoreDataService } from "../../Services/core-data.service";
import { TokenModel } from "../shared";
import { SharedDataService } from "../../Services/shared-data.service";
import { CustomersEntities, CustomerSummary, locationDateFilterEntity, LocationDropdownListModel, CustomerLocationByName } from "./header.model";
import { Subscription } from 'rxjs';
import { first } from 'rxjs/operators';
import { error } from 'console';
import { NavigationLoggerService } from 'src/app/Services/navigation-logger.service';
export let browserRefresh = false;
@Component({
  selector: 'admin-header',
  templateUrl: './header.component.html',
  encapsulation: ViewEncapsulation.Emulated,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit, OnDestroy {
  hasAllValueSelected: boolean = false;
  public StateList: Array<string> = [];
  items: any[] = [{
    text: 'Reports',
    items: [
      { text: 'Monthly Report' }, 
      // {text: 'Monthly IM Report'}, 
      { text: 'Bulk Downloads' },
      { text: 'Compliance' }, 
      { text: 'Documentation' }]
  }];
  goToEvaluateUrl: any;
  selectAllState: string = 'All';
  CustomerName: string;
  originalDate = new Date();
  startDate: string;
  fromDateFilter: any;
  tokenData: TokenModel = new TokenModel();
  endDate: string;
  totalCount: number;
  totalSelectedLocations: string;
  toolTipMsg: string;
  defaultLocation = [];
  defaultLocationAsString: string;
  customerList: CustomersEntities[] = [];
  hasOneCustomer: boolean;
  isLoggedIn: boolean;
  LoggedInUserName: string;
  locations = [];
  defaultCurrency: string;
  TotalStatus: number = 0;
  locationList: CustomersEntities[] = [];
  defaultDropdown = "Location Filter (All Locations)";
  TotalOpenDetInCurrency: string;
  // events
  subscription: Subscription;
  DashboardCount: CustomerSummary = new CustomerSummary();
  public listItems: CustomerLocationByName[] = [];
  public selectedLocationList: Array<any> = [];
  priority = "PRIORITY";
  public opened = false;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  Category = []
  customerIds = [];
  public range: any;
  userId: number;
  isInternalUser: boolean = true;
  @Output() valueChange = new EventEmitter();
  @Output() addLocationListOnRefreshBrowser = new EventEmitter();
  public LocationData: Array<CustomerLocationByName> = [];
  IsDashboard: boolean = false;
  IsIMDashboard: boolean = false;
  isPrioritization: boolean = false;
  IsIMUser: boolean = false
  IsMonthlyReport: boolean = false;
  IsMonthlyIMReport: boolean = false;
  IsBulkDownloads: boolean = false;
  constructor(private sessionStorage: SessionStorageService, private titleService: Title, private activatedRoute: ActivatedRoute, private router: Router, private coredata: CoreDataService, private authservice: AuthenticationService, public shareData: SharedDataService, private navigationLoggerService: NavigationLoggerService) {
    this.tokenData = this.authservice.getTokenData();
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.tokenData.IsExternalUser === "1") {
      this.isInternalUser = false;
      this.items = [{
        text: 'Reports',
        items: [
          { text: 'Bulk Downloads' },
          { text: 'Compliance' },
          { text: 'Documentation' }
        ]
      }];
    }
    let sessionVar = this.sessionStorage.retrieve('locations');
    this.LoggedInUserName = this.tokenData.Name;
    this.userId = parseInt(this.tokenData.UserID);
    let pageTite = this.activatedRoute.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
    browserRefresh = !router.navigated;
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (this.locationDateFilterData === null) {
      if (this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == "") {
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations.split(",").map(el => parseInt(el));
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
    if (browserRefresh === false) {
      if (sessionVar === null) {
        this.loadCustomerLocationDropdown(this.userId);
      }
      else if (sessionVar != null) {
        this.loadLocationStoredInSession();
      }
    }

    if (browserRefresh === true) {
      this.loadCustomerLocationDropdown(this.userId);
    }
  }
  ngOnInit() {
    this.IsIMUser = JSON.parse(localStorage.getItem("IMUserOnly"))
    let sessionVarForDashboardCount = this.sessionStorage.retrieve('dashboardCount');
    this.shareData.defaultasObservable.subscribe(data => {
    });
    if (this.router.url.startsWith("/IM-Dashboard")) {
      this.IsIMDashboard = true;
    }
    if (this.router.url === "/MonthlyReport") {
      this.IsMonthlyReport = true;
    }
    if (this.router.url === "/MonthlyIMReport") {
      this.IsMonthlyIMReport = true;
    }
    if (this.router.url === "/generateBulkReport") {
      this.IsBulkDownloads = true;
    }
    this.defaultCurrency = "";
    if (browserRefresh === false) {
      if (sessionVarForDashboardCount === null) {
        if (this.IsIMDashboard === false)
          this.loadCount();
      }
      else if (sessionVarForDashboardCount != null) {
        this.loadDashboardCountStoreInSession();
      }
    }
    else if (browserRefresh == true) {
      if (this.IsIMDashboard === false)
        this.loadCount();
    }
  }

  removeTag(index) {
    let data = this.selectedLocationList.slice();
    data.splice(index, 1);
    this.selectedLocationList = data;
    this.checkIsListSelectedAll();
  }
  redirectToOutStandingQuotes() {
    if (!this.IsIMUser) {
      this.router.navigate(["/Quotes", this.priority, "All"]);
    }
  }

  checkStatus() {
    this.checkIsListSelectedAll();
  }

  redirectToOpenDebt() {
    if (!this.IsIMUser) {
      this.router.navigate(["/OpenDebt", "All"]);
    }
  }

  valueChanged() { // You can give any function name
    let data = {
      start: this.range.start,
      end: this.range.end,
      locations: this.locations.toString()
    }
    this.valueChange.emit(JSON.stringify(data));
  }

  redirectToOpenCalls() {
    if (!this.IsIMUser) {
      this.router.navigate(["/ServiceCalls", "All"]);
    }
  }

  redirectToOpenjc() {
    if (!this.IsIMUser) {
      let start = this.range["start"];
      let end = this.range["end"];
      let locations = this.locations.toString();
      let dataToString = {
        start: start,
        end: end,
        locations: locations
      };
      localStorage.setItem('location', JSON.stringify(dataToString));
      this.router.navigate(["/QuotesInProgress", "All"]);
    }
  }
  redirectMCCService() {
    if (!this.IsIMUser) {
      this.router.navigate(["/PPMCompletion", "All"]);
    }
  }

  redirectToSubContractor() {
    if (!this.IsIMUser) {
      this.router.navigate(["/SubContractor"]);
    }
  }

  redirectToEvaluateSystem() {
    this.GenerateGoToEvaluate();

  }
  activeMenuText: string = '';
  onSelectMenu(events) {
    if (events != null && events != undefined) {
      this.activeMenuText = events.item.text;
      if (events.item.text === "Monthly Report") {
        this.router.navigate(['/MonthlyReport']);
      }
      // else if (events.item.text === "Monthly IM Report") {
      //   this.router.navigate(['/MonthlyIMReport']);
      // }
      else if (events.item.text === "Bulk Downloads") {
        this.router.navigate(['/generateBulkReport']);
      }
      else if (events.item.text === "Compliance") {
        this.router.navigate(['/complianceReport']);
      }
      else if (events.item.text === "Documentation") {
        this.router.navigate(['/documentation']);
      }
    }
  }

  editdate(filtered: boolean = false) {
    let customerIds = [];
    this.selectedLocationList.map(element => {
      customerIds.push(element.LocationID);
    });
    this.locations = customerIds;
    this.shareData.setCustomerIDS(customerIds);

    this.shareData.defaultdata.next(this.selectedLocationList);
    // this.shareData.changeDateDefault({ start: this.range.start, end: this.range.end });
    this.defaultCurrency = "";
    if (this.IsIMDashboard === false)
      this.loadCount();
    //console.log("editDate: " + filtered);
    this.setDataInLocalStorage(filtered);
    this.valueChanged();
  }
  // FK: save starting date for filter range
  saveDateRange() {
    this.coredata.saveDateRange(this.range["start"]).subscribe(response => {
      if (response != null && response != undefined) {
        if (response.StatusCode === 200) {
          this.shareData.showSuccess('Filter range has been saved');
        }
        else {
          this.shareData.showError("Error has occured please contact support.");
        }
      }
    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }

  loadLocationStoredInSession() {
    this.locationList = this.sessionStorage.retrieve('locations');
    this.listItems = this.locationList;
    this.sessionStorage.store('locations', this.locationList);
    this.shareData.setLocationList(this.listItems);
    let haveMultipleCustomers = [];
    this.locationList.map(element => {
      let indexofElement = haveMultipleCustomers.findIndex(data => data.CustomerID === element.CustomerID);
      if (indexofElement === -1) {
        haveMultipleCustomers.push(element);
      }
    });
    if (haveMultipleCustomers.length === 1) {
      this.hasOneCustomer = true;
      this.CustomerName = haveMultipleCustomers[0].CustName;
    }

    this.LocationData = JSON.parse(JSON.stringify(this.listItems));
    this.loadStates(this.LocationData.slice());

    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (JSON.parse(localStorage.getItem('location')) != null) {
      let range = {
        start: new Date(this.locationDateFilterData.start),
        end: new Date(this.locationDateFilterData.end)
      };
      this.range = range;
      if (!!this.locationDateFilterData.locations) {
        let selectedLocationList = [];
        this.locations.map(element => {
          let value = this.listItems.find(el => el.LocationID == element.toString());
          selectedLocationList.push(value);
        });
        this.selectedLocationList = selectedLocationList;
        this.defaultLocationAsString = this.selectedLocationList.toString();
      }
    }


  }

  loadDashboardCountStoreInSession() {
    this.DashboardCount = this.sessionStorage.retrieve('dashboardcount');
    this.sessionStorage.store('dashboardCount', this.DashboardCount)
    this.TotalOpenDetInCurrency = "$" + this.CurrencyToString(this.DashboardCount.TotalOpenDebt);
    this.shareData.setCustomerSummary(this.DashboardCount);
  }

  loadCustomerLocationDropdown(id) {
    this.coredata.getCustomerLocation(id).pipe(first()).subscribe(res => {
      if (res.StatusCode === 200) {
        this.locationList = res.response;
        this.listItems = this.locationList;
        if (browserRefresh == true) {
          this.addLocationListOnRefreshBrowser.emit(this.locationList);
        }
        this.selectedLocationList = [];
        this.locations.map(element => {
          let data = this.listItems.find(el => el.LocationID === element);
          this.selectedLocationList.push(data);
        })
        this.sessionStorage.store('locations', this.locationList);
        this.shareData.setLocationList(this.listItems);
        let haveMultipleCustomers = [];
        this.locationList.map(element => {
          let indexofElement = haveMultipleCustomers.findIndex(data => data.CustomerID === element.CustomerID);
          if (indexofElement === -1) {
            haveMultipleCustomers.push(element);
          }
        });
        if (haveMultipleCustomers.length === 1) {
          this.hasOneCustomer = true;
          this.CustomerName = haveMultipleCustomers[0].CustName;
        }

        this.LocationData = JSON.parse(JSON.stringify(this.listItems));
        this.loadStates(this.listItems.slice());

        this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
        if (JSON.parse(localStorage.getItem('location')) != null) {
          let range = {
            start: new Date(this.locationDateFilterData.start),
            end: new Date(this.locationDateFilterData.end)
          };
          this.range = range;
          if (!!this.locationDateFilterData.locations) {
            let selectedLocationList = [];
            this.locations.map(element => {
              let value = this.listItems.find(el => el.LocationID == element.toString());
              selectedLocationList.push(value);
            });
            this.selectedLocationList = selectedLocationList;
          }

        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }

  loadCount() {
    this.coredata.getDashbaordCount(this.locations, this.range["start"], this.range["end"]).pipe(first()).subscribe(res => {
      if (res.StatusCode === 200 && res.response != null && res.response != undefined) {
        this.DashboardCount = res.response[0];
        this.sessionStorage.store('dashboardCount', this.DashboardCount)
        this.TotalOpenDetInCurrency = "$" + this.CurrencyToString(this.DashboardCount.TotalOpenDebt);
        this.shareData.setCustomerSummary(this.DashboardCount);
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }

  loadStates(locations: Array<any>) {
    let StatesWithoutDuplicateElement = [];
    locations.map(element => {
      let index = StatesWithoutDuplicateElement.findIndex(el => el === element.StateCode);
      if (index === -1) {
        StatesWithoutDuplicateElement.push(element.StateCode);
      }
    });

    this.StateList = StatesWithoutDuplicateElement;
    this.StateList.unshift('All');
    this.selectAllState = 'All';

  }

  GenerateGoToEvaluate() {
    this.coredata.generateGoToEaluateToken().pipe(first()).subscribe(res => {
      if (res.StatusCode === 200 && res.response != null && res.response != undefined) {
        this.goToEvaluateUrl = res.response;
        let url = this.goToEvaluateUrl;
        window.open(url, '_blank' // <- This is what makes it open in a new window.
        );

      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }
  redirectToUpTick() {
    let url = "https://airmaster.onuptick.com/"
    window.open(url, '_blank');
  }
  public close(value) {
    if (value === "cancel") {
      this.opened = false;
      let dataItem = [];
      this.locations.map(element => {
        let data = this.listItems.find(el => (el.LocationID).toString() === element.toString());
        dataItem.push(data);

      })
      this.selectedLocationList = dataItem;
      this.checkIsListSelectedAll();
      this.stateChange('All');
    }
    else if (value === "yes") {
      this.opened = false;
      let customerIds = [];
      this.selectedLocationList.map(element => {
        customerIds.push(element.LocationID);
      });
      this.locations = customerIds;
      this.shareData.setCustomerIDS(customerIds);
      this.shareData.defaultdata.next(this.selectedLocationList);
      this.GetLocationPriority();
      this.defaultCurrency = "";
      if (this.IsIMDashboard === false)
        this.loadCount();
      this.valueChanged();
      this.setDataInLocalStorage(false);
      this.stateChange('All');
    }
  }

  GetLocationPriority() {
    if (this.selectedLocationList.length == 1) {
      this.coredata.GetFTLocationPriority(this.selectedLocationList[0].CustomerCode, this.selectedLocationList[0].LocationCode).subscribe(response => {
        if (response != null && response != undefined) {
          if (response.StatusCode === 200) {
            this.priorityItems.map((res) => {
              if (res.label == 'Equipment Reliability') {
                res.priority = response.response.EQReliablityPriority
              }
              if (res.label == 'Thermal Comfort') {
                res.priority = response.response.ThermalCountPriority
              }
              if (res.label == 'Energy Waste') {
                res.priority = response.response.EnergyPriority
              }
            })
          }
        }
      }, catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
    }
  }
  stateChange(event) {
    this.hasAllValueSelected = false;
    if (event != null && event != undefined) {
      if (event === 'All') {
        this.LocationData = JSON.parse(JSON.stringify(this.listItems));
        this.selectAllState = 'All';
      }
      else {
        this.LocationData = this.listItems.filter(element => element.StateCode === event);
        this.selectAllState = event;
      }
    }
    this.checkIsListSelectedAll();
  }

  checkIsListSelectedAll() {
    if (this.selectAllState != 'All') {
      let selectedList: Array<any> = this.selectedLocationList.filter(eleement => eleement.StateCode === this.selectAllState);
      if (selectedList.length === this.LocationData.length) {
        this.hasAllValueSelected = true;
      }
      else if (selectedList.length < this.LocationData.length || selectedList === undefined || selectedList === null) {
        this.hasAllValueSelected = false;
      }
    }
    else if (this.selectAllState === 'All') {
      if (this.selectedLocationList.length === this.LocationData.length) {
        this.hasAllValueSelected = true;
      }
      else if (this.selectedLocationList.length < this.LocationData.length) {
        this.hasAllValueSelected = false;
      }
    }
  }

  public open() {
    this.opened = true;
  }

  public filterChange(filter: any): void {
    if (filter.length >= 1) {
      let LocationDatabyLocationName = this.listItems.filter((l) => l.Name.includes(filter.toUpperCase()));
      let LocationDatabyState = this.listItems.filter((s) => s.StateCode.includes(filter.toUpperCase()));
      let LocationDatabyCustomer = this.listItems.filter((c) => c.CustName.includes(filter.toUpperCase()));
      let LocationDataByCustomerCode = this.listItems.filter((c) => c.CustomerCode.includes(filter.toUpperCase()));
      let LocationDataByLocationCode = this.listItems.filter((c) => c.LocationCode.includes(filter.toUpperCase()));
      this.LocationData = LocationDatabyLocationName.concat(LocationDatabyState.concat(LocationDatabyCustomer.concat(LocationDataByCustomerCode.concat(LocationDataByLocationCode))));
    } else {
      this.LocationData = this.listItems;
    }
  }
  clearAll(data) {
    this.selectedLocationList = [];
    this.checkIsListSelectedAll();
  }

  ngDoCheck() {
    let locations = [];
    let defaultLocation = [];
    this.selectedLocationList.map((element, index) => {
      if ((index + 1) <= 1) {
        this.totalCount = index + 1;
        defaultLocation.push(element.Name);
        this.defaultLocation = defaultLocation;
      }
      this.defaultLocationAsString = this.defaultLocation.toString();
      locations.push(element.Name);
    });
    this.totalSelectedLocations = locations.toString();
    this.toolTipMsg = this.totalSelectedLocations.split(',').join(' , ');
  }

  setDataInLocalStorage(filtered: boolean = false) {
    let start = this.range["start"];
    let end = this.range["end"];
    let locations = this.locations.toString();
    let dataToString = {
      start: start,
      end: end,
      locations: locations
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
    if (filtered == true) {
      //MS: Emit the Date to Behaviour so that it can be subscribe at the MOM page to update the data of MOM List with the Date range.
      this.shareData.getMOMData$.next(false);
    }
  }

  CurrencyToString(value: any) {
    if (value == 0) {
      this.defaultCurrency = "";
      return 0;
    }
    else {
      // hundreds
      if (value <= 999) {
        this.defaultCurrency = " In Hundreds ";
        return Number.parseFloat(value.toString()).toFixed(2);
      }
      // thousands
      else if (value >= 1000 && value <= 999999) {
        this.defaultCurrency = " In Thousands ";
        return Number.parseFloat((value / 1000).toString()).toFixed(2).toString() + 'K';
      }
      // millions
      else if (value >= 1000000 && value <= 999999999) {
        this.defaultCurrency = " In Millions ";

        return Number.parseFloat((value / 1000000).toString()).toFixed(2).toString() + 'M';
      }
      // billions
      else if (value >= 1000000000 && value <= 999999999999) {
        this.defaultCurrency = " In Billions ";
        return Number.parseFloat((value / 1000000000).toString()).toFixed(2).toString() + 'B';
      }
      else
        return value;
    }
  }

  logout() {
    // Remove tokens and profile and update login status subject
    //localStorage.removeItem('CPToken');
    this.navigationLoggerService.logNavigation(this.authservice.getTokenData(), 'logout');
    this.shareData.deleteAccessToken();

    this.sessionStorage.clear();
    this.shareData.showInfo('You have been succesfully logged out');
    this.router.navigate(['/']);
    this.authservice.setLoggedIn(false);
    this.shareData.setCustomerIDS([]);
    this.shareData.changeDateDefault(undefined);
    this.shareData.defaultvalue([]);
    this.shareData.setCustomerSummary([]);
  }

  OnSelectAllChange() {
    if (this.selectedLocationList.length != 0) {
      this.LocationData.map(ele => {
        this.selectedLocationList.push(ele);
      });
    }
    else if (this.selectAllState === 'All') {
      this.selectedLocationList = this.LocationData;
    }
    else {
      this.selectedLocationList = this.LocationData;
    }
    let locationWithNoDuplications = [];
    this.selectedLocationList.map(data => {
      let index = locationWithNoDuplications.findIndex(element => element.LocationID === data.LocationID);
      if (index === -1) {
        locationWithNoDuplications.push(data);
      }
    });
    this.selectedLocationList = locationWithNoDuplications;
    this.checkIsListSelectedAll();

  }

  OnDeSelectAllChange() {
    this.LocationData.map(el => {
      let index = this.selectedLocationList.findIndex(data => data.LocationID === el.LocationID);
      if (index != -1) {
        this.selectedLocationList.splice(index, 1);
      }
    });
    let data = JSON.parse(JSON.stringify(this.selectedLocationList));
    this.selectedLocationList = [];
    this.selectedLocationList = data;
    this.checkIsListSelectedAll();
  }

  ngOnDestroy() {
    // this.subscription.unsubscribe();
  }

  openPrioritization() {
    this.GetLocationPriority();
    this.isPrioritization = true;
  }

  public closePriority(value) {
    if (value === "cancel") {
      this.isPrioritization = false;
    }
  }
  priorityLevels = ['High', 'Medium', 'Low'];
  priorityItems = [
    { label: 'Equipment Reliability', priority: 'High', icon: '../../../assets/images/equipment.png' },
    { label: 'Thermal Comfort', priority: 'Medium', icon: '../../../assets/images/thermal-comfort.png' },
    { label: 'Energy Waste', priority: 'Low', icon: '../../../assets/images/energy-waste.png' }
  ];
  draggedItemIndex: number | null = null;

  onDragStart(event: DragEvent, index: number) {
    this.draggedItemIndex = index;
    event.dataTransfer?.setData('text/plain', index.toString()); // Set index as data
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    const draggedItemIndexStr = event.dataTransfer?.getData('text/plain');
    if (draggedItemIndexStr !== null) {
      const draggedItemIndex = parseInt(draggedItemIndexStr, 10);
      if (this.draggedItemIndex !== null && draggedItemIndex !== null) {
        const draggedItem = this.priorityItems[this.draggedItemIndex];
        const dropItem = this.priorityItems[dropIndex];
        const draggedPriorityIndex = this.priorityLevels.indexOf(draggedItem.priority);
        const dropPriorityIndex = this.priorityLevels.indexOf(dropItem.priority);
        const newPriority = this.priorityLevels[dropPriorityIndex];
        this.priorityItems[this.draggedItemIndex] = { ...draggedItem, priority: newPriority };
        this.priorityItems[dropIndex] = { ...dropItem, priority: this.priorityLevels[draggedPriorityIndex] };
        this.priorityItems = [...this.priorityItems];

        console.log('Updated priorityItems:', this.priorityItems);
        this.draggedItemIndex = null;
      }
    }
  }

  ApplyPriority() {
    const payload = [];
    const data = { CUSTNMBR: '', ADRSCODE: '', EQReliablityPriority: '', ThermalCountPriority: '', EnergyPriority: '' }
    this.selectedLocationList.forEach((res) => {
      data.CUSTNMBR = res.CustomerCode
      data.ADRSCODE = res.LocationCode
      data.EQReliablityPriority = this.priorityItems[0].priority
      data.ThermalCountPriority = this.priorityItems[1].priority
      data.EnergyPriority = this.priorityItems[2].priority
      payload.push(data)
    })

    this.coredata.UpdateFTLocationPriority(payload).subscribe(response => {
      if (response != null && response != undefined) {
        if (response.StatusCode === 200) {
          this.shareData.showSuccess("Priority updated successfully.")
          this.isPrioritization = false;
        }
        else {
          this.shareData.showError("Error has occured please contact support.");
        }
      }
    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
  isEquipment: boolean = false;
  equipment = []
  filteredEquipment = [];
  lastConfirmedSelection: any[] = [];
  openEquipments() {
    this.isEquipment = true;
    let isIMUPdatedValue = JSON.parse(localStorage.getItem('IsIMDataUpdated'));
    if (isIMUPdatedValue == true) {
      let completeData = JSON.parse(localStorage.getItem("CompleteList"))
      let awaitingData = JSON.parse(localStorage.getItem("AwaitingList"))
      let backlogData = JSON.parse(localStorage.getItem("BacklogList"))

      if (completeData != null && completeData != undefined && awaitingData != null && awaitingData != undefined && backlogData != null && backlogData != undefined) {
        let getEquipments = this.findCommonAndUniqueEquipments(completeData, awaitingData, backlogData);
        getEquipments.sort((a, b) => a.name.localeCompare(b.name));
        this.equipment = [...getEquipments];
        this.filteredEquipment = [...this.equipment];
      }

      localStorage.setItem("IsIMDataUpdated", JSON.stringify(false));
    }

  }

  public closeEquipment(value) {
    if (value === "cancel") {
      this.isEquipment = false;
      this.revertToLastConfirmed();
      this.resetSearchAndFilter();
    }
  }


  searchTerm: string = '';
  selectAll: boolean = false;
  @Output() equipmentsData = new EventEmitter();
  // equipment = [
  //   { name: 'AHU-01', selected: false },
  //   { name: 'AHU-02', selected: false },
  //   { name: 'AHU-03', selected: false },
  //   { name: 'FCU-01', selected: false },
  //   { name: 'FCU-02', selected: false },
  //   { name: 'FCU-03', selected: false }
  // ];
  // filteredEquipment = [...this.equipment];

  filterEquipment() {
    if (this.searchTerm) {
      this.filteredEquipment = this.equipment.filter(item =>
        item.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.filteredEquipment = [...this.equipment];
    }
    this.updateSelectAll();
  }

  toggleSelectAll() {
    this.filteredEquipment.forEach(item => item.selected = this.selectAll);
  }
  getSelectedItems() {
    return this.equipment.filter(item => item.selected);
  }

  confirmSelection() {
    const selectedItems = this.getSelectedItems();
    this.equipmentsData.emit(selectedItems);
    this.lastConfirmedSelection = JSON.parse(JSON.stringify(this.equipment));
    this.isEquipment = false;
    this.resetSearchAndFilter();
  }

  cancelSelection() {
    this.isEquipment = false;
    this.revertToLastConfirmed();
    this.resetSearchAndFilter();
  }

  updateSelectAll() {
    this.selectAll = this.filteredEquipment.every(item => item.selected);
  }

  clearEquipment() {
    this.equipment.forEach(item => item.selected = false);
    this.selectAll = false;
    this.lastConfirmedSelection = [];
  }

  revertToLastConfirmed() {
    if (this.lastConfirmedSelection.length) {
      this.equipment = JSON.parse(JSON.stringify(this.lastConfirmedSelection)); // Revert to the confirmed state
      this.filteredEquipment = [...this.equipment]; // Reset the filtered list too
      this.updateSelectAll();
    } else {
      this.equipment.forEach(item => item.selected = false); // Deselect all items
      this.filteredEquipment = [...this.equipment]; // Reset filtered list
      this.updateSelectAll();
    }
  }


  findCommonAndUniqueEquipments(arr1: any[], arr2: any[], arr3: any[]): any[] {
    // Extracting the names of equipment
    const equipment1 = arr1.map(item => item.Equipment);
    const equipment2 = arr2.map(item => item.Equipment);
    const equipment3 = arr3.map(item => item.Equipment);

    // Finding common and unique equipment
    // const commonEquipments = equipment1.filter(e => equipment2.includes(e));
    const commonEquipments = equipment1.filter(e => equipment2.includes(e) && equipment3.includes(e));
    const uniqueEquipments = [
      // ...equipment1.filter(e => !equipment2.includes(e)),
      // ...equipment2.filter(e => !equipment1.includes(e)),

      ...equipment1.filter(e => !equipment2.includes(e) || !equipment3.includes(e)),
      ...equipment2.filter(e => !equipment1.includes(e) || !equipment3.includes(e)),
      ...equipment3.filter(e => !equipment1.includes(e) || !equipment2.includes(e)),
    ];

    const allEquipment = Array.from(new Set([...commonEquipments, ...uniqueEquipments]));

    // Return the final array of objects with 'name' instead of 'Equipment'
    return allEquipment.map(name => ({ name, selected: false }));
  }

  resetSearchAndFilter() {
    this.searchTerm = '';  // Clear the search input
    this.filteredEquipment = [...this.equipment];  // Reset to the full equipment list
    this.updateSelectAll();  // Update the select all checkbox state
  }
}
