import { Compo<PERSON>, HostListener, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { BreadCrumbItem } from '@progress/kendo-angular-navigation';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { CreateFolderRequestModel, DriveRootItemsModel, LocationFolderData, UserAssignedLocations } from './documentation';
import { JwtHelper } from 'angular2-jwt';
import { SessionStorageService } from 'ngx-webstorage';
import { fas,faFolder,faFolderPlus,faCheck} from '@fortawesome/free-solid-svg-icons';
import { saveAs } from 'file-saver';

@Component({
    selector: 'app-documentation',
    templateUrl: './documentation.component.html',
    styleUrls: ['./documentation.component.css'],
    encapsulation: ViewEncapsulation.None,
})
export class DocumentationComponent implements OnInit, OnDestroy, OnChanges {
    //Font-Awesome icons
    public fas = fas;
    public faFolder = faFolder;
    public faFolderPlus = faFolderPlus;
    public faCheck = faCheck;

    public pageHeight = window.innerHeight - 299;
    public NewMenuItems: any[] = [
        {
            name: 'New',
            iconName: 'plus',
            id: 1,
        },
        // {
        //     name: 'File',
        //     iconName: 'paste-from-word',
        //     id: 2,
        //     parentId: 1,
        // },
        {
            name: 'Folder',
            iconName: 'folder',
            id: 2,
            parentId: 1
        }];
    public UploadMenuItems: any[] = [
        {
            name: 'Upload',
            iconName: 'upload',
            id: 1,
        },
        {
            name: 'File',
            iconName: 'file',
            id: 2,
            parentId: 1,
        }
        // {
        //     name: 'Folder',
        //     iconName: 'folder',
        //     id: 3,
        //     parentId: 1
        // }
    ];

    public defaultBreadCrumbItems: BreadCrumbItem[] = [
        {
            text: 'Files',
            title: 'Files',
            icon: 'cloud'
        },
    ];

    public gridData: any[] = [];
    public contextMenuItem: any[] = [];
    loggedInUserID: string;

    allRowsSelected: boolean = false;
    rowsSelected: any;

    DriveData: Array<DriveRootItemsModel> = [];
    selectedItem: DriveRootItemsModel;
    createFolderData: CreateFolderRequestModel = new CreateFolderRequestModel();
    jwtHelper: JwtHelper = new JwtHelper();
    hideNewMenu: boolean = false;
    hideUploadMenu: boolean = false;
    isRoot: boolean = false;

    openCreateFolderPopup: boolean = false;
    openCreateFolderByLocationPopup: boolean = false;

    folderName: string;

    userAssignedLocations: Array<UserAssignedLocations> = [];
    locationDataByCustomer: Array<DriveRootItemsModel> = [];
    createLocationFolderData: Array<LocationFolderData> = [];
    createLocationFolderList: Array<LocationFolderData> = [];
    locationListForFolder: Array<LocationFolderData> = [];
    IsCustAgency: boolean = false;

    constructor(
        private coreDataService: CoreDataService,
        private sharedDataService: SharedDataService,
        private titleService: Title,
        private route: ActivatedRoute,
        private sessionStorage: SessionStorageService) {
        let pageTite = this.route.snapshot.data["title"];
        this.titleService.setTitle(pageTite);
        this.sharedDataService.removeBacklogData();
        //var token = localStorage.getItem('CPToken');
        var token = this.sharedDataService.getAccessToken();

        let tokenVal = this.jwtHelper.decodeToken(token);
        this.loggedInUserID = tokenVal['UserID'];
        this.IsCustAgency = (tokenVal['RoleName'] == 'CUST' || tokenVal['RoleName'] == 'AG') ? true : false;

        this.getAuthenticationToken();
    }
    //Dynamically updating the grid height
    @HostListener('window:resize', ['$event'])
    onResize(event) {
        this.pageHeight = event.target.innerHeight - 299;
    }

    ngOnInit() {
        if (this.selectedItem == undefined || this.selectedItem == null) {
            this.hideNewMenu = true;
            this.hideUploadMenu = true;
        }
    }

    ngOnChanges(): void {
    }

    ngOnDestroy(): void {

    }
    // When Click on the BreadCrumb item
    public onBreadCrumbClick(item: BreadCrumbItem): void {
        if (item != undefined && item != null) {
            if (item.text == 'Files') {
                this.isRoot = true;
                this.hideNewMenu = true;
                this.hideUploadMenu = true;
                this.selectedItem = undefined;
                this.getDriveData("", "CUST");
                this.sharedDataService.IsCustomerLeveldata = undefined;
            } else {
                this.isRoot = false;
                let name = item.text.split('+')[0];;
                let id = item.text.split('+')[1];
                let level = item.text.split('+')[2];
                if (level.toUpperCase() == "LOCT" || level.toUpperCase() == "ITEM") {
                    if (this.IsCustAgency==true) {
                        this.hideNewMenu = true;
                    } else {
                        this.hideNewMenu = false;
                    }
                    if (this.IsCustAgency == true) {
                        this.hideUploadMenu = true;
                    } else {
                        this.hideUploadMenu = false;
                    }

                } else if (level.toUpperCase() == "CUST")
                {
                    this.isRoot = true;
                    if (this.IsCustAgency==true) {
                        this.hideNewMenu = true;
                    } else {
                        this.hideNewMenu = false;
                    }
                    this.hideUploadMenu = true;
                }
                this.selectedItem.Name = name;
                this.selectedItem.Id = id;
                this.selectedItem.Level = level;


                if (id != undefined && id != null && level != undefined && level != null) {
                    this.getDriveData(id, level);
                }
            }

            const index = this.defaultBreadCrumbItems.findIndex(e => e.text === item.text);
            this.defaultBreadCrumbItems = this.defaultBreadCrumbItems.slice(0, index + 1);
        }
    }
    //Refreshing the Breadcrumb list (Default)
    public refreshBreadCrumb(): void {
        this.defaultBreadCrumbItems = [...this.defaultBreadCrumbItems];
    }


    //MS-1 : Get MSGraph Access Token API
    public getAuthenticationToken() {
        this.sharedDataService.IsCustomerLeveldata = undefined;
        this.coreDataService.getAuthentication().subscribe(
            (data) => {
                if (data != undefined) {
                    if (data.Status == 200) {
                        localStorage.setItem("GraphAccessToken", data.Response);
                        //default level is CUST for Root data
                        this.isRoot = true;
                        this.getDriveData("", "CUST");
                    }
                }
                else {
                    this.isRoot = false;
                    this.sharedDataService.showWarning("No data found");
                }
            },
            error => {
                this.sharedDataService.showError("An error occured, please contact support.");
            },
            () => {
                //console.log("Get Authentication Token Completed.");
            }
        );

    }



    //On Grid Row Click for Context Menu
    onCellClick({ dataItem, rowIndex }) {
        if (dataItem != undefined && dataItem != null) {
            if (dataItem.Type.toUpperCase() == "FOLDER") {
                this.contextMenuItem = ['Open', 'Download']
            } else {
                if (this.IsCustAgency) {
                    this.contextMenuItem = ['Preview', 'Download']
                } else {
                    this.contextMenuItem = ['Preview', 'Download','Delete']
                }

            }
        }
    }

    //MS -2 : Get Drive Data
    getDriveData(Id: string, Level: string, Item: string = null) {
        this.coreDataService.getDriveData(Id, Level, this.loggedInUserID).subscribe(
            (data) => {
                if (data != undefined && data != null) {
                    if (data.Status == 200) {
                        if (Item != undefined && Item != null) {
                            if (Level.toUpperCase() == "LOCT" || Level.toUpperCase() == "ITEM")
                            {
                                this.isRoot = false;
                                if (this.IsCustAgency==true) {
                                    this.hideNewMenu = true;
                                } else {
                                    this.hideNewMenu = false;
                                }
                                if (this.IsCustAgency == true) {
                                    this.hideUploadMenu = true;
                                } else {
                                    this.hideUploadMenu = false;
                                }
                            }
                            else if (Level.toUpperCase() == "CUST") {
                                this.isRoot = true;
                                if (this.IsCustAgency==true) {
                                    this.hideNewMenu = true;
                                } else {
                                    this.hideNewMenu = false;
                                }
                                this.hideUploadMenu = true;
                                // this.locationDataByCustomer = data.Response.filter((loc) => {
                                //     return loc.Type.toUpperCase() == "FOLDER"
                                // });
                                // this.getUserLoctCustByCustID(this.selectedItem.Name);
                            }
                            //MS: Updating BreadCrumb
                            if(this.selectedItem.CustName != null && this.selectedItem.CustName != undefined){
                                this.defaultBreadCrumbItems.push({
                                    text: this.selectedItem.CustName + "+" + this.selectedItem.Id + "+" + this.selectedItem.Level
                                });
                            }else if(this.selectedItem.LOCATNNM != null && this.selectedItem.LOCATNNM != undefined){
                                this.defaultBreadCrumbItems.push({
                                    text: this.selectedItem.LOCATNNM + "+" + this.selectedItem.Id + "+" + this.selectedItem.Level
                                });
                            }else {
                                this.defaultBreadCrumbItems.push({
                                    text: this.selectedItem.Name + "+" + this.selectedItem.Id + "+" + this.selectedItem.Level
                                });
                            }
                            // this.defaultBreadCrumbItems.push({
                            //     text: this.selectedItem.Name + "+" + this.selectedItem.Id + "+" + this.selectedItem.Level
                            // });
                            this.refreshBreadCrumb();
                        }
                        if(Level.toUpperCase() == "CUST" && this.sharedDataService.IsCustomerLeveldata != undefined && this.sharedDataService.IsCustomerLeveldata != null){
                            this.DriveData = [];
                            this.coreDataService.getLocatationByCustomerGP(this.sharedDataService.IsCustomerLeveldata.Name).subscribe((getLocationGpData)=> {
                                 if(getLocationGpData.StatusCode == 200){
                                    if(getLocationGpData != null && getLocationGpData != undefined){
                                         let locationData = data.Response;
                                         let complianceData = data.Response;
                                         const filteredLoc = getLocationGpData.response.filter((elem) => {
                                            return locationData.some((ele) => {
                                            return ele.Name == elem.ADRSCODE;
                                              });
                                            });
                                          if(filteredLoc.length > 0){
                                            const getfilteredLocGpData = locationData.filter((elem) => {
                                                return filteredLoc.some((ele) => {
                                                return ele.ADRSCODE == elem.Name;
                                                  });
                                                });
                                             getfilteredLocGpData.map((obj1)=> {
                                                filteredLoc.map((obj2)=> {
                                                  if(obj1.Name == obj2.ADRSCODE){
                                                     obj1.LOCATNNM = obj2.LOCATNNM;
                                                  }    
                                                })
                                             })    
                                             let filteredData = getfilteredLocGpData;
                                             const sortedList = filteredData.sort((a, b) =>
                                                  a.LOCATNNM.localeCompare(b.LOCATNNM));
                                               this.DriveData = sortedList;

                                               let arr : any = [];
                                               if(complianceData != null && complianceData != undefined){
                                                  if(complianceData.length > 0){
                                                      let allfilterData = complianceData.filter((obj)=> {
                                                          return obj.Name == "1. Compliance";
                                                      })
                                                      allfilterData.map((obj2)=> {
                                                          obj2.LOCATNNM = '1. Compliance';  
                                                      }) 
                                                      arr = allfilterData;
                                                  }
                                               }
                                               if(arr.length > 0){
                                                  for(let i = 0; i< arr.length; i++){
                                                    this.DriveData.push(arr[i]);
                                                  }
                                                  console.log(this.DriveData);
                                               }
                                          }else{
                                             this.DriveData = [];
                                          }
                                    }else{
                                        this.sharedDataService.showWarning("No data found");
                                        this.DriveData = [];
                                    }
                                 }else{
                                    this.sharedDataService.showError("An error occured, please try again or contact support");
                                 }
                            },
                            error=> {
                                this.sharedDataService.showError("An error occured, please contact support.");
                              }
                            )
                        }else{
                            let driveData = data.Response;
                            const sortedList = driveData.sort((a, b) =>
                            (a.CustName ? a.CustName : a.Name).localeCompare(b.CustName ? b.CustName : b.Name));
                            this.DriveData = sortedList;
                        }

                        if (Level.toUpperCase() == "CUST" && this.selectedItem != undefined && this.selectedItem != null)
                        {
                            this.isRoot = false;
                            this.locationDataByCustomer = data.Response.filter((loc) => {
                                return loc.Type.toUpperCase() == "FOLDER"
                            });
                            this.getUserLoctCustByCustID(this.selectedItem.Name);
                        }
                    }
                    else {
                        this.isRoot = false;
                        this.sharedDataService.showWarning("No data found");
                    }
                }
                else {
                    this.sharedDataService.showWarning("An error occured, please try again or contact support");
                }
            },
            error => {
                this.sharedDataService.showError("An error occured, please contact support.");
            },
            () => {
                //console.log("Get Drive Data Completed.");
            }
        );
    }

    // Applying the Icon for Grid Row Items
    public iconClass(dataItem): any {
        if (dataItem != undefined && dataItem != null) {
            let Type = (dataItem.Type == null || dataItem.Type == undefined) ? null : dataItem.Type.toUpperCase();
            let MimeType = (dataItem.MimeType == null || dataItem.MimeType == undefined) ? null : dataItem.MimeType.toLowerCase();
            if (Type != undefined && Type != null) {
                let extension ;
                if (Type == "FILE"){
                    extension = dataItem.Name != null ? dataItem.Name.split('.')[1] : null;
                }
                //let extension = MimeType != null ? MimeType.split('/')[1] : null;
                return {
                    //MS: File Types & Icons
                    "k-i-photo": extension == "tif",
                    "k-i-image": (extension == "jpg" || extension == "png" || extension == "jpeg"),
                    "k-i-pdf": extension == "pdf",
                    "k-i-txt": extension == "txt",
                    "k-i-csv": extension == "csv",
                    "k-i-excel": (extension == "xls" || extension == "xlsx"),
                    "k-i-word": (extension == "doc" || extension == "docx"),
                    "k-i-zip": extension == "zip",
                    "k-i-html5": (extension == "htm" || extension == "html"),
                    "k-i-psd": extension == "psd",
                    "k-i-css3": extension == "css",

                    //MS: Folder & Icon
                    "k-i-folder": Type == "FOLDER",

                    //MS: Icon Config
                    "k-icon": true,
                    "k-i-document-manager":
                        !Type &&
                        extension != "tif" &&
                        extension != "jpg" &&
                        extension != "pdf" &&
                        extension != "txt" &&
                        extension != "csv" &&
                        extension != "xls" &&
                        extension != "doc" &&
                        extension != "docx" &&
                        extension != "zip" &&
                        extension != "psd" &&
                        extension != "htm" &&
                        extension != "html" &&
                        extension != "css"
                };
            }
        }
    }
    //On Grid row click to Open the Folder.
    public cellClickHandler(dataItem): void {
        if (dataItem != undefined && dataItem != null) {
            if (dataItem.Type != null && dataItem.Type != undefined) {
                if (dataItem.Type.toUpperCase() == "FOLDER") {
                    if (dataItem.Id != null && dataItem.Id != undefined) {
                        this.selectedItem = dataItem;
                        if(dataItem.Level.toUpperCase() == 'CUST'){
                            this.sharedDataService.IsCustomerLeveldata = dataItem;
                        }
                        this.getDriveData(this.selectedItem.Id, this.selectedItem.Level, this.selectedItem.Name);
                    } else {
                        console.log("Invalid Item Id");
                    }
                }
                // Preview the item in New Tab when click on the Item.
                else if (dataItem.Type.toUpperCase() == "FILE") {
                    this.previewItem(dataItem.Id);
                }
            }
        }
    }

    //On selecting the context menu item for Action on the Folder or File.
    public onContextItemSelect({ dataItem, item }): void {
        if (item != undefined && item != null) {
            if (item.toUpperCase() == "PREVIEW") {
                this.previewItem(dataItem.Id);
            }
            else
                if (item.toUpperCase() == "OPEN") {
                    this.cellClickHandler(dataItem);
                }
                else
                    if (item.toUpperCase() == "DOWNLOAD") {
                        if (dataItem != undefined && dataItem != null) {
                            this.downloadDriveData(dataItem.Id, dataItem.Name);
                        }
                    }
                    else if (item.toUpperCase() == "DELETE") {
                        if (dataItem != undefined && dataItem != null) {
                            this.DeleteItem(dataItem.Id,this.selectedItem.Id);
                        }
            }
        }
    }
    // Applying the icons on the context menu items.
    public contextIconClass(items): any {
        if (items != undefined && items != null) {
            let itemName = items.toUpperCase();
            return {
                //MS: Context Menu Items Icon
                "k-i-preview": itemName == "PREVIEW",
                "k-i-folder-open": itemName == "OPEN",
                "k-i-download": itemName == "DOWNLOAD",
                "k-i-delete": itemName == "DELETE",

                //MS: Icon Config
                "k-icon": true
            };
        }
    }

    //MS-3 : Download File API by ID
    downloadDriveData(Id: string, Name: string) {
        if (Id != undefined && Id != null) {
            this.coreDataService.downloadDriveData(Id).subscribe(
                (data) => {
                    if (data != undefined && data.type != 0) {
                        if (data.status == 200) {
                            let extension = data.headers.get("content-type");
                            extension = extension.split('/')[1];
                            //let fileName = Name + "." + extension;
                            var blob = new Blob([data.body], { type: data.headers.get("content-type") + ';' + 'charset=utf - 8' });
                            saveAs(blob, Name);
                        }
                        else {
                            this.sharedDataService.showWarning("No data found");
                        }
                    }
                },
                error => {
                    this.sharedDataService.showError("An error occured, please contact support.");
                },
                () => {
                    //console.log("Get Drive Data Completed.");
                }
            );
        }
    }
    //MS-3.1 : Preview the file in New Tab
    previewItem(Id: string) {
        if (Id != undefined && Id != null) {
            this.coreDataService.downloadDriveData(Id).subscribe(
                (data) => {
                    if (data != undefined && data.type != 0) {
                        if (data.status == 200) {
                            let extension = data.headers.get("content-type");
                            extension = extension.split('/')[1];
                            var blob = new Blob([data.body], { type: data.headers.get("content-type") });
                            const reader = new FileReader();
                            reader.onload = (e: any) => {
                                let url = window.URL.createObjectURL(blob);

                                var tabWindowId = window.open('about:blank', '_blank');
                                tabWindowId.location.href = url;
                            };
                            reader.readAsArrayBuffer(blob);
                        }
                        else {
                            this.sharedDataService.showWarning("No data found");
                        }
                    }
                },
                error => {
                    this.sharedDataService.showError("An error occured, please contact support.");
                },
                () => {
                   // console.log("Get Drive Data Completed.");
                }
            );
        }
    }
     //MS-3.1 : Delete the file using Item ID
     DeleteItem(Id: string,parentID:string) {
        if (Id != undefined && Id != null) {
            this.coreDataService.deletedDriveData(Id).subscribe(
                (data) => {
                    if (data != undefined && data.type != 0) {
                        if (data.Status == 200) {
                            setTimeout(() => {
                                this.sharedDataService.showSuccess("File deleted successfully.");
                            }, 1500);
                        }
                    }
                },
                error => {
                    this.sharedDataService.showError("An error occured, please contact support.");
                },
                () => {
                    this.getDriveData(parentID, this.selectedItem.Level);
                }
            );
        }
    }

    //MS-4 Create New Folder API
    createFolder(createFor: string) {
        if (createFor.toUpperCase()=="FROMLOCATION") {
            this.openCreateFolderByLocationPopup = false;
            let foldersName = this.locationListForFolder.map((obj) => {
                return obj.LocationCode
            });
            this.folderName = foldersName.toString();
        }
        else {
            this.openCreateFolderPopup = false;
        }
        if (this.folderName != undefined && this.folderName != null) {
            if (this.selectedItem.Id != undefined && this.selectedItem.Id != null) {
                this.createFolderData.FolderName = this.folderName;
                this.createFolderData.ParentID = this.selectedItem.Id;

                this.coreDataService.createDriveFolder(this.createFolderData).subscribe(
                    (data) => {
                        if (data != undefined) {
                            if (data.Status == 200) {
                                this.getDriveData(this.selectedItem.Id, this.selectedItem.Level);
                            }
                            else {
                                this.sharedDataService.showWarning("No data found");
                            }
                        }
                    },
                    error => {
                        this.sharedDataService.showError("An error occured, please contact support.");
                    },
                    () => {
                        //"Create folder Completed.");
                    }
                );
            }
            else {
                this.sharedDataService.showWarning("An error occured, please refresh.");
            }
        }
    }

    // Validate before uploading the file
    onSelectUploadMenu(event) {
        if (event.item.text.toUpperCase() === "FILE") {
            // document.getElementById('uploadFile').click();
            this.chooseFile();
        }
    }
    // Handler to upload the file : Event
    chooseFile() {
        //document.querySelector('input').click()
        if (this.selectedItem.Id != undefined && this.selectedItem.Id != null) {
            document.getElementById("uploadFile").click();
        } else {
            this.sharedDataService.showWarning("Can not upload file on root.");
        }
    }

    //MS-5 Uplodad File API
    uploadSelectedFile(event) {
        this.coreDataService.uploadDriveData(event.target.files[0], this.selectedItem.Id, event.target.files[0].name).subscribe(
            (data) => {
                if (data != undefined && data != null) {
                    if (data.Status == 200) {
                        this.getDriveData(this.selectedItem.Id, this.selectedItem.Level);
                    }
                    else {
                        this.sharedDataService.showWarning("Unable to upload, please cotact support");
                    }
                }
            },
            error => {
                this.sharedDataService.showError("An error occured, please contact support.");
            },
            () => {
                //console.log("Upload Data Completed.");
            }
        );
    }

    //Validate & Open popup to provide New Folder Name
    onSelectNewMenu(event) {
        if (event.item.text.toUpperCase() === "FOLDER") {
            if (this.selectedItem != undefined && this.selectedItem != null) {
                if (this.selectedItem.Level.toUpperCase() == "CUST")
                {
                    this.locationListForFolder = undefined;
                    this.openCreateFolderByLocationPopup = true;
                } else {
                    this.folderName = undefined;
                    this.openCreateFolderPopup = true;
                }
            }
            else {
                this.folderName = undefined;
                this.openCreateFolderPopup = true;
            }
        }
    }
    closeFolderPopup() {
        this.openCreateFolderPopup = false;
        this.openCreateFolderByLocationPopup = false;
    }
    validateFolderNameLength(val) {
        if (val != undefined && val != null) {
            if (val.length >= 50) {
                this.folderName = this.folderName.slice(0, 49);
                this.sharedDataService.showWarning("Maximum 50 char(s) are allowed for folder name.");
            }
        }
    }

    //Getting all the Location-Customer Data by Selected Customer ID/Code
    getUserLoctCustByCustID(CustCode: string) {
        if (this.sessionStorage.retrieve('locations') != undefined && this.sessionStorage.retrieve('locations') != null) {
            this.userAssignedLocations = this.sessionStorage.retrieve('locations');
            if (this.userAssignedLocations.length > 0) {
                this.userAssignedLocations = this.userAssignedLocations.filter((rec) => {
                    return rec.CustomerCode.toUpperCase() == CustCode.toUpperCase()
                });
                this.getCreateLocationFolderData();
            }
        }
        else {
            if (this.loggedInUserID != undefined && this.loggedInUserID != null) {
                this.coreDataService.getCustomerLocation(this.loggedInUserID).subscribe(
                    (data) => {
                        if (data != undefined && data != null) {
                            if (data.StatusCode === 200) {
                                this.userAssignedLocations = data.response;

                                if (this.userAssignedLocations.length > 0) {
                                    this.userAssignedLocations = this.userAssignedLocations.filter((rec) => {
                                        return rec.CustomerCode.toUpperCase() == CustCode.toUpperCase()
                                    });
                                }

                            } else {
                                this.sharedDataService.showError("Unable to get the User Assigned sites, please retry.");
                            }
                        }
                    },
                    error => {
                        this.sharedDataService.showError("Unable to get the User Assigned sites, please retry.");
                    },
                    () => {
                       // console.log("User assigned locations are fetched, completed.");
                        //call to manipulate the final data for Location folder
                        this.getCreateLocationFolderData();
                    }
                );
            }
        }
    }
    //Get Final data to bind in the Create Locations from the Popup by selected Customer
    getCreateLocationFolderData() {
        if (this.locationDataByCustomer != undefined && this.locationDataByCustomer != null && this.userAssignedLocations != undefined && this.userAssignedLocations != null) {
            if (this.userAssignedLocations.length > 0) {
                if (this.locationDataByCustomer.length > 0) {
                    this.createLocationFolderData = [];
                    this.userAssignedLocations.forEach(element => {
                        let index = this.locationDataByCustomer.findIndex((rec) => {
                            return rec.Name.toUpperCase() == element.LocationCode.toUpperCase()
                        });
                        let item = new LocationFolderData();
                        item.CustomerCode = element.CustomerCode;
                        item.CustomerID = element.CustomerID;
                        item.CustomerName = element.CustName;
                        item.LocationCode = element.LocationCode;
                        item.LocationID = element.LocationID;
                        item.LocationName = element.Name;

                        if (index > -1) {
                            item.Status = 0;
                        } else {
                            item.Status = 1;
                        }
                        this.createLocationFolderData.push(item);
                    });
                    this.createLocationFolderList = this.createLocationFolderData;
                }
                else {
                    this.createLocationFolderData = [];
                    this.userAssignedLocations.forEach(element => {
                        let item = new LocationFolderData();
                        item.CustomerCode = element.CustomerCode;
                        item.CustomerID = element.CustomerID;
                        item.CustomerName = element.CustName;
                        item.LocationCode = element.LocationCode;
                        item.LocationID = element.LocationID;
                        item.LocationName = element.Name;
                        item.Status = 1;
                        this.createLocationFolderData.push(item);
                    });
                    this.createLocationFolderList = this.createLocationFolderData;
                }
            }
        }
    }
    public itemDisabled(itemArgs: { dataItem: any, index: number }): boolean {
        return !itemArgs.dataItem.Status;
    }

    close() {

    }
    clearAll() {
        this.locationListForFolder = undefined;
    }
    public filterChange(filter: any): void {
        if (filter.length >= 1) {
            let LocationDatabyLocationName = this.createLocationFolderList.filter((l) => l.LocationName.includes(filter.toUpperCase()));
            let LocationDatabyCustomer = this.createLocationFolderList.filter((c) => c.CustomerName.includes(filter.toUpperCase()));
            let LocationDatabyCustomerCode = this.createLocationFolderList.filter((c) => c.CustomerCode.includes(filter.toUpperCase()));
            let LocationDatabyLocationCode = this.createLocationFolderList.filter((c) => c.LocationCode.includes(filter.toUpperCase()));
            this.createLocationFolderData = LocationDatabyLocationName.concat(LocationDatabyCustomer.concat(LocationDatabyCustomerCode.concat(LocationDatabyLocationCode)));
        }
        else {
            this.createLocationFolderData = this.createLocationFolderList;
        }
    }
    onAllSelected() {
    }
}