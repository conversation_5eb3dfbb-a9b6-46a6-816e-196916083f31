.p-0 {
  padding: 0px;
}
.OpenDebtFooter {
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
//FK:
.scrollDownBtn {
  bottom: 70px;
  border-radius: 50%;
  border: none;
  position: relative;
  background-color: Transparent;
  background-repeat: no-repeat;
}

.ng-tooltip {
  position: absolute;
  max-width: 150px;
  font-size: 14px;
  text-align: center;
  color: #f8f8f2;
  padding: 3px 8px;
  background: #282a36;
  border-radius: 4px;
  z-index: 1000;
  opacity: 0;
}
.ng-tooltip:after {
  content: "";
  position: absolute;
  border-style: solid;
}

.ng-tooltip-top:after {
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-color: black transparent transparent transparent;
}

.ng-tooltip-show {
  opacity: 1;
}