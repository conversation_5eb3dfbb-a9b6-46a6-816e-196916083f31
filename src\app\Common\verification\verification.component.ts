import { TokenModel, otpToken } from "./../shared";
import { AuthenticationService } from "./../../Services/authentication.service";
import { Router } from "@angular/router";
import { CoreDataService } from "./../../Services/core-data.service";
import { SharedDataService } from "./../../Services/shared-data.service";
import { OnInit } from "@angular/core";
import { Component, ViewChild, ElementRef } from "@angular/core";
import { MFAModel } from "../shared";
import { SessionStorageService } from 'ngx-webstorage';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { NavigationLoggerService } from "src/app/Services/navigation-logger.service";



@Component({
  selector: "app-verification",
  templateUrl: "./verification.component.html",
  styleUrls: ["./verification.component.css"],
})
export class VerificationComponent implements OnInit {
  loginDetails: MFAModel;
  isNewLogin: boolean = false;
  isLoggedIn: boolean;
  tokenData: TokenModel = new TokenModel();
  resendOTP: boolean = false;
  counter: number;
  public changePassword: boolean;
  public password: string;
  public retypepassword: string;
  public otpToken: otpToken = new otpToken();

  constructor(
    private sharedDataService: SharedDataService,
    private coreDataService: CoreDataService,
    private authService: AuthenticationService,
    private router: Router,
    private sessionStorage: SessionStorageService,
    private recaptchaV3Service: ReCaptchaV3Service,
    private navigationLoggerService: NavigationLoggerService
  ) {
    this.loginDetails = new MFAModel();
    this.changePassword = this.sharedDataService.ChangePassword;
    this.sharedDataService.removeBacklogData();
  }

  ngOnInit() {

    this.Timer();

    var Pin1 = document.getElementById("pincode-1");
    Pin1.focus();
  }

  keyup(event, value) {
    if (event.code == "Backspace") {

      switch (value) {
        case "2": {
          var Pin1 = document.getElementById("pincode-1");
          Pin1.focus();
          break;
        }

        case "3": {
          var Pin2 = document.getElementById("pincode-2");
          Pin2.focus();
          break;
        }

        case "4": {
          var Pin3 = document.getElementById("pincode-3");
          Pin3.focus();
          break;
        }

        case "5": {
          var Pin4 = document.getElementById("pincode-4");
          Pin4.focus();
          break;
        }

        // case "6": {
        //   var Pin5 = document.getElementById("pincode-5");
        //   Pin5.focus();
        //   break;
        // }

        // case "7": {
        //   var Pin6 = document.getElementById("pincode-6");
        //   Pin6.focus();
        //   break;
        // }

        // case "8": {
        //   var Pin7 = document.getElementById("pincode-7");
        //   Pin7.focus();
        //   break;
        // }
      }
    }
    if (event.code.includes("Digit") || event.code.includes("Key")) {
      switch (value) {
        case "5":
          {
            if (!this.changePassword)
              this.submit();
            else
              this.ChangePassword();
          }
          break;
      }
    }
  }

  keydown(event, value) {

    if (event.code.includes("Digit") || event.code.includes("Key")) {
      switch (value) {
        case "1": {
          if (
            (<HTMLInputElement>document.getElementById("pincode-1")).value != ""
          ) {
            var Pin2 = document.getElementById("pincode-2");
            Pin2.focus();
          }
          break;
        }

        case "2":
          {
            var Pin3 = document.getElementById("pincode-3");
            Pin3.focus();
          }
          break;
        case "3":
          {
            var Pin4 = document.getElementById("pincode-4");
            Pin4.focus();
          }
          break;
        case "4":
          {
            var Pin5 = document.getElementById("pincode-5");
            Pin5.focus();
          }
          break;
        case "5":
          {
            var Pin6 = document.getElementById("pincode-6");
            // Pin6.focus();
          }
          //   break;
          // case "6":
          //   {
          //     var Pin7 = document.getElementById("pincode-7");
          //     Pin7.focus();
          //   }
          //   break;
          // case "7":
          //   {
          //     var Pin8 = document.getElementById("pincode-8");
          //     Pin8.focus();

          //   }
          break;

      }
    }
  }
  CreatePasscode() {
    var pin1 = (<HTMLInputElement>document.getElementById("pincode-1")).value;
    var pin2 = (<HTMLInputElement>document.getElementById("pincode-2")).value;
    var pin3 = (<HTMLInputElement>document.getElementById("pincode-3")).value;
    var pin4 = (<HTMLInputElement>document.getElementById("pincode-4")).value;
    var pin5 = (<HTMLInputElement>document.getElementById("pincode-5")).value;
    // var pin6 = (<HTMLInputElement>document.getElementById("pincode-6")).value;
    // var pin7 = (<HTMLInputElement>document.getElementById("pincode-7")).value;
    // var pin8 = (<HTMLInputElement>document.getElementById("pincode-8")).value;


    var OTP: string =
      pin1 + pin2 + pin3 + pin4 + pin5;

    if (/^([A-Za-z0-9]{5})$/.test(OTP)) {
      this.loginDetails.OTP = OTP;
    } else {
      this.sharedDataService.showError(
        "Passcode must be Alphanumeric and length must be 5 character long"
      );
    }


  }
  submit() {

    this.CreatePasscode();
    this.loginDetails.loginUsername = this.sharedDataService.loginUsername;
    this.loginDetails.password = this.sharedDataService.password;
    if (this.loginDetails.OTP.length == 5) {
      this.getLogin();
    }

  }

  getLogin() {
    this.sharedDataService.deleteAccessToken();
    this.sessionStorage.clear();
    this.coreDataService.getLogin(this.loginDetails).subscribe(
      (data: any) => {

        this.authService._setSession(data);
        this.getFilterDateByUserID();
      },
      (catchError) => {
        if (catchError) {
          this.sharedDataService.showError("Incorrect OTP");
        } else {
          this.sharedDataService.ErrorHandler(catchError);
        }
      }
    );
  }
  getFilterDateByUserID() {
    this.tokenData = this.authService.getTokenData();
    //let userId = this.tokenData.UserID;
    this.coreDataService.getFilterDateByUserID().subscribe(
      (res: any) => {
        if (res != null && res != undefined) {
          if (
            res.StatusCode === 200 &&
            res.response != null &&
            res.response != undefined
          ) {
            this.sharedDataService.setStartDateByUser(
              res.response.FromDateFilter
            );
            localStorage.setItem("IMUserOnly", res.response.IMUserOnly == null ? false : res.response.IMUserOnly);
            localStorage.setItem("TuningModule", res.response.TuningModule == null ? false : res.response.TuningModule);
            if (res.response.IMUserOnly) {
              this.router.navigate(["/IM-Dashboard"]);
            } else {
              this.router.navigate(["/Dashboard"]);
            }

            if (this.isNewLogin == true) {
              this.navigationLoggerService.logNavigation(this.tokenData, 'login');
              this.sharedDataService.showSuccess(
                "You have successfully logged in"
              );
            }
          } else if (res.StatusCode === 204) {
            this.router.navigate(["/Dashboard"]);
            if (this.isNewLogin == true) {
              this.navigationLoggerService.logNavigation(this.tokenData, 'login');
              this.sharedDataService.showSuccess(
                "You have successfully logged in"
              );
            }
          } else {
            this.sharedDataService.showError(
              "Error has occured please contact support."
            );
          }
        }
      },
      (catchError) => {
        if (catchError) {
          this.sharedDataService.ErrorHandler(catchError);
        }
      }
    );
  }
  ResendCode() {
    this.resendOTP = false;
    this.recaptchaV3Service.execute("importantAction").subscribe((token) => {
      if (token) {
        // console.log(token);
        this.otpToken.RecaptchaToken = token;
        this.otpToken.userName = this.sharedDataService.loginUsername;
        this.coreDataService
          .SendPasscode(this.otpToken)
          .subscribe((res) => {
            if (res["StatusCode"] === 200) {

              this.sharedDataService.showInfo("OTP Sent");
              this.Timer();
            }
          });
        this.ClearPasscode()

      }
    }, (error) => {
      this.sharedDataService.showError(
        "reCAPTCHA Error")
    }
    );

  }
  ClearPasscode() {
    (<HTMLInputElement>document.getElementById("pincode-1")).value = "";
    (<HTMLInputElement>document.getElementById("pincode-2")).value = "";
    (<HTMLInputElement>document.getElementById("pincode-3")).value = "";
    (<HTMLInputElement>document.getElementById("pincode-4")).value = "";
    (<HTMLInputElement>document.getElementById("pincode-5")).value = "";
    //  (<HTMLInputElement>document.getElementById("pincode-6")).value="";
    //  (<HTMLInputElement>document.getElementById("pincode-7")).value="";
    //  (<HTMLInputElement>document.getElementById("pincode-8")).value="";

    var Pin1 = document.getElementById("pincode-1");
    Pin1.focus();
  }
  Timer() {
    //   this.counter=60;
    //  const interval= setInterval(() => {
    //     this.counter--  
    //    }, 1000);
    //   setTimeout(() => {
    //     clearInterval(interval);
    //     this.resendOTP=true;

    //   }, 60000);
    //   this.counter=60;
    this.resendOTP = true;
  }

  ChangePassword() {
    this.CreatePasscode();
    if (this.password == undefined) {

      this.sharedDataService.showError("Password field is empty")
    }
    else if (this.retypepassword == undefined) {

      this.sharedDataService.showError("Retypepassword field is empty")
    }
    else if (this.password != this.retypepassword) {

      this.sharedDataService.showError("Password and Retype Password not match")
    }
    if (this.password == this.retypepassword && this.password != undefined && this.loginDetails.OTP != "") {
      if (/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{10,16}$/.test(this.retypepassword)) {

        this.coreDataService.ChangePassword(this.sharedDataService.loginUsername, this.loginDetails.OTP, this.password).subscribe(res => {
          if (res["response"] == "Password Changed Successfully") {
            this.sharedDataService.showSuccess("Password Changed Successfully")
            this.loginDetails.loginUsername = this.sharedDataService.loginUsername
            this.loginDetails.password = this.password;
            this.getLogin();

          }
          else {
            this.sharedDataService.showError("Invalid OTP")
          }
        })

      } else {
        this.sharedDataService.showError(
          "Password must be between 10-16 characters long and must contain a-z, A-Z, 0-9, '@,$,!,%,*,?,&'"
        );
      }



    }
    else
      this.sharedDataService.showError("OTP or Password field  empty")
  }
  Back() {

    this.router.navigate(["/Dashboard"]);
  }
}