import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, HostListener } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { BulkDocumentsEntity, DocumentDetailsEntities, DocDetailByPropertiesModel, DocumentsPropertiesEntity, CustomersEntities, CustomerLocationByName, locationDateFilterEntity } from "./generateBulkDoc-model";
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { AuthenticationService } from "../../Services/authentication.service";
import { DataSourceRequestState, process, State } from '@progress/kendo-data-query';
import {
  DataStateChangeEvent, GridDataResult, SelectAllCheckboxState
} from '@progress/kendo-angular-grid';
import { TokenDecodedEntities } from '../../Common/shared';

import { SpinnerVisibilityService } from 'ng-http-loader';
import { DocumentIDEntity } from './generateBulkDoc-model';
import { SelectableSettings } from '@progress/kendo-angular-grid';
import { Subscription } from 'rxjs';
import { SessionStorageService } from 'ngx-webstorage';
import { first } from 'rxjs/operators';
import { saveAs } from "file-saver";
export let isBrowserRefreshed: boolean = false;
@Component({
  selector: 'app-generate-bulk-report',
  templateUrl: './generate-bulk-report.component.html',
  styleUrls: ['./generate-bulk-report.component.scss']
})
export class GenerateBulkReportComponent implements OnInit, DoCheck, OnDestroy {
  public state: State = {
    skip: 0,
    take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  public selectableSettings: SelectableSettings;
  selectedRowIndex: number;
  isExpanded: boolean;
  docIds: Array<DocumentIDEntity> = [];
  progressPercentage: number;
  oldProgressPercentage: number;

  fileDownloaded: boolean = false;
  docID: DocumentIDEntity = new DocumentIDEntity();
  selectedCheckbox: Array<DocDetailByPropertiesModel> = [];
  decumentDetails: Array<DocumentDetailsEntities> = [];
  DocumentPropertiesListgridData: DocDetailByPropertiesModel[] = [];
  docDetailsByProperties: DocDetailByPropertiesModel[] = [];
  operator: string;
  public selectAllState: SelectAllCheckboxState = 'unchecked';
  public NumericTextBoxValue;
  public mySelection: any[] = [];

  public decimals: number = 7;
  locationInDropdown = ["In"];
  public selectedLocationList: Array<any> = [];
  public tempSelectedLocationList: Array<any> = [];
  locationsData: any;
  isBulkDownloadClicked: boolean = false;
  subscriptionLocationsList: Subscription;
  defaultPropety: any;
  hasAllValueSelected: boolean = false;
  allOperator: boolean = true;
  betweenOperator: boolean = false;
  defaultDropdown = "Select Locations";
  DefaultInputSerachText: string;
  selectedValue = this.locationInDropdown[0];
  DateNumberDropdown = ["<", ">", "<=", ">=", "=", "Between"];
  StringDataTypeDropdown = [{ key: "Contains", value: "Contains" }, { key: "Begins With", value: "BeginsWith" }, { key: "Ends With", value: "EndsWith" }];
  DocumentList: BulkDocumentsEntity[] = [];
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  locations = [];
  public date: Date;
  totalCount: number;
  defaultLocation = [];
  public opened = false;
  gridData: DocumentsPropertiesEntity[] = [];
  defaultLocationAsString: string;
  DocumentProperties: DocumentsPropertiesEntity[] = [];
  SelectedDocuments = [];
  public LocationData: Array<CustomerLocationByName> = [];
  totalSelectedLocations: string;
  locationList: CustomersEntities[] = [];
  public listItems: CustomerLocationByName[] = [];
  public gridDataDetails: GridDataResult = process(this.DocumentPropertiesListgridData, this.state);
  expanded: boolean = false;
  tokenData: TokenDecodedEntities = new TokenDecodedEntities();
  @ViewChild('list') listconfig: any;
  self = this;

  public bwrange: any;
  constructor(private session: SessionStorageService, private router: Router, private spinner: SpinnerVisibilityService, private authservice: AuthenticationService, private coredataservice: CoreDataService, private shareData: SharedDataService,
    private titleService: Title,
    private route: ActivatedRoute) {
      this.shareData.removeBacklogData();
    let tokenData = this.authservice.getTokenData();
    this.tokenData = tokenData;
    this.setSelectableSettings();
    isBrowserRefreshed = !router.navigated;
    if (isBrowserRefreshed === false) {
      this.loadCustomerLocationDropdown(tokenData.UserID, undefined);
    }

    this.loadAllDocuments();
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
  }
  itemDisabled(itemArgs: { dataItem: string, index: number }) {
    return itemArgs.dataItem['disabled'] === true;
  }
  iclose(event) {
    event.preventDefault();
  }
  public items: Array<any> = [
    { title: "Search & Download", isShow: true, expanded: true, keepItemContent: true }
  ];

  public onSelectedKeysChange(e) {
    const len = this.mySelection.length;
    if (len === 0) {
      this.selectAllState = 'unchecked';
    } else if (len > 0 && len < this.DocumentPropertiesListgridData.length) {
      this.selectAllState = 'indeterminate';
    } else {
      this.selectAllState = 'checked';
    }
  }
  public onSelectAllChange(checkedState: SelectAllCheckboxState) {
    if (checkedState === 'checked') {
      this.mySelection = this.DocumentPropertiesListgridData.map((item) => item.DocumentId);
      this.selectAllState = 'checked';
    } else {
      this.mySelection = [];
      this.selectAllState = 'unchecked';
    }
  }

  DownloadDoc(dataItem, index) {
    this.fileDownloaded = false;
    this.selectedRowIndex = index;
    if (this.selectedCheckbox.length > 0 || dataItem != undefined) {
      let hasDiffrentDocumentType: boolean;
      for (let key2 in this.selectedCheckbox) {
        if (this.selectedCheckbox[key2].DocumentType === this.selectedCheckbox[0].DocumentType) {
          hasDiffrentDocumentType = false;
        }
        else if (this.selectedCheckbox[key2].DocumentType != this.selectedCheckbox[0].DocumentType) {
          hasDiffrentDocumentType = true;
          break;
        }
      }

      this.docIds = [];
      if (dataItem != undefined) {
        this.docIds = [{ DocumentID: dataItem.DocumentId }];
      }
      else {
        this.selectedCheckbox.map(element => {
          this.docID.DocumentID = element.DocumentId;
          this.docIds.push({ DocumentID: this.docID.DocumentID });
        });
      }
      this.isBulkDownloadClicked = true;
      this.oldProgressPercentage = this.progressPercentage = 0;

      let docTostring = JSON.stringify(this.docIds);
      this.coredataservice.downloadBulkDocuments(docTostring).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              this.progressPercentage = 100;
              let result = res['body'];
              let fileType = result.type.split('/')[1];
              let ReportName;
              if (dataItem != undefined && this.selectedCheckbox.length === 0) { //if user  click on indivisual download button
                ReportName = res.headers.get('x-filename') + "." + fileType;
              }
              else if (dataItem != undefined && this.selectedCheckbox.length > 0) {
                ReportName = res.headers.get('x-filename') + "." + fileType;//if user click on indivisual download button but have already
                //selected some checkbox
              }
              else if (this.selectedCheckbox.length === 1 && dataItem === undefined) {
                ReportName = res.headers.get('x-filename') + "." + fileType;
              }
              else if (this.selectedCheckbox.length > 1 && dataItem === undefined) {
                ReportName = hasDiffrentDocumentType ? 'FLOW' + "." + fileType : this.selectedCheckbox[0].DocumentType + "." + fileType;
              }
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });

              if (this.progressPercentage == 100) {
                saveAs(blob, ReportName);
                this.fileDownloaded = true;
                this.isBulkDownloadClicked = true;
                this.shareData.showSuccess(this.selectedCheckbox.length === 1 ? this.selectedCheckbox[0].DocumentType + " is downloaded successfully" : 'Documents are' + " downloaded successfully");
              }
            }
            else if (res.status === 204) {
              this.shareData.showWarning("Document could not be found. Please Contact Support");
            }

          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.isBulkDownloadClicked = false;
          }
        });
    }
    else {
      this.shareData.showWarning("Please select at least one Document");
    }
  }
  removeTag(index) {
    if (this.selectedLocationList.length > 1) {
      let data = this.selectedLocationList.slice();
      data.splice(index, 1);
      if (data.length === 1) {
        this.selectedLocationList = data;

        let index = this.LocationData.findIndex(data => data.LocationID === this.selectedLocationList[0].LocationID);
        this.LocationData[index]['disabled'] = true;
        this.checkIsListSelectedAll();
      }
      else {
        this.selectedLocationList = data;
        this.checkIsListSelectedAll();
      }
    }
    else if (this.selectedLocationList.length === 1) {
      this.shareData.showWarning("Minimum 1 Location is Required");
    }
  }
  public close(value) {
    if (value === "cancel") {
      this.opened = false;
      this.locationsData = {};
      // this.selectedLocationList = [];
    }
  }
  clearAll() {
    //this.selectedLocationList = [this.listItems[0]];
    this.tempSelectedLocationList = [this.listItems[0]]
    this.LocationData.map(data => {
      data['disabled'] = false;
    });
    this.LocationData[0]['disabled'] = true;
    this.SetLocations(undefined);
  }
  ngDoCheck() {
    this.selectedCheckbox = [];
    this.mySelection.map(el => {
      let findElement = this.DocumentPropertiesListgridData.find(ele => ele.DocumentId === el);
      this.selectedCheckbox.push(findElement);
    });
    let locations = [];
    let defaultLocation = [];
    this.selectedLocationList.map((element, index) => {
      if ((index + 1) <= 1) {
        this.totalCount = index + 1;
        defaultLocation.push(element.Name);
        this.defaultLocation = defaultLocation;
      }
      this.defaultLocationAsString = this.defaultLocation.toString();
      locations.push(element.Name);
    });
    this.totalSelectedLocations = locations.toString();


  }
  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridDataDetails = process(this.DocumentPropertiesListgridData, this.state);
  }

  setValues() {
    if (this.tempSelectedLocationList.length === 1) {
      let index = this.LocationData.findIndex(data => data.LocationID === this.selectedLocationList[0].LocationID);
      this.LocationData[index]['disabled'] = true;
    }
    if (this.tempSelectedLocationList.length > 1) {
      this.LocationData.map(ele => {
        ele["disabled"] = false;
      })
    }
    if (this.tempSelectedLocationList.length === 0) {
      this.tempSelectedLocationList = [this.listItems[0]];
      this.shareData.showWarning("Minimum 1 Location is Required");
    }
    this.checkIsListSelectedAll();

  }

  loadCustomerLocationDropdown(id, locations) {
    if (locations === undefined) {
      let locationInSession = this.session.retrieve('locations');
      if (locationInSession === null) {
        this.coredataservice.getCustomerLocation(id).pipe(first()).subscribe(res => {
          if (res.StatusCode === 200) {
            this.locationList = res.response;
            this.listItems = this.locationList;
            this.LocationData = JSON.parse(JSON.stringify(this.listItems));
            this.selectedLocationList = this.LocationData;
            this.tempSelectedLocationList = this.selectedLocationList;
            this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
            if (JSON.parse(localStorage.getItem('location')) != null) {
              if (!!this.locationDateFilterData.locations) {
                let selectedLocationList = [];
                this.locations.map(element => {
                  let value = this.listItems.find(el => el.LocationID == element.toString());
                  selectedLocationList.push(value);
                });
                this.selectedLocationList = selectedLocationList;
                this.tempSelectedLocationList = this.selectedLocationList;
              }
            }
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
              // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
            }
          });
      }
      else if (locationInSession != null) {
        this.locationList = locationInSession;
        this.listItems = this.locationList;
        this.LocationData = JSON.parse(JSON.stringify(this.listItems));
        this.selectedLocationList = this.LocationData;
        this.tempSelectedLocationList = this.selectedLocationList;
        this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
        if (JSON.parse(localStorage.getItem('location')) != null) {
          if (!!this.locationDateFilterData.locations) {
            let selectedLocationList = [];
            this.locations.map(element => {
              let value = this.listItems.find(el => el.LocationID == element.toString());
              selectedLocationList.push(value);
            });
            this.selectedLocationList = selectedLocationList;
            this.tempSelectedLocationList = this.selectedLocationList;
          }
        }
      }
    }
    else if (locations != undefined) {
      this.locationList = locations;
      this.listItems = this.locationList;
      this.LocationData = JSON.parse(JSON.stringify(this.listItems));
      this.selectedLocationList = this.LocationData;
      this.tempSelectedLocationList = this.selectedLocationList;
      this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
      if (JSON.parse(localStorage.getItem('location')) != null) {
        if (!!this.locationDateFilterData.locations) {
          let selectedLocationList = [];
          this.locations.map(element => {
            let value = this.listItems.find(el => el.LocationID == element.toString());
            selectedLocationList.push(value);
          });
          this.selectedLocationList = selectedLocationList;
          this.tempSelectedLocationList = this.selectedLocationList;
        }
      }
    }

  }
  ngOnInit(): void {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;
    }
    this.shareData.progressEventBehaviorSubjectAsObservable.subscribe(data => {
      if (typeof (data) == 'number' && !isNaN(data)) { this.oldProgressPercentage = this.progressPercentage = data; } else {
        this.progressPercentage = this.oldProgressPercentage;
      }
    }
    );
  }
  loadAllDocuments() {
    this.coredataservice.getBulkDocuments().pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          this.DocumentList = res.response;
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }

  onItemChange(val) {
    this.betweenOperator = false;
    this.allOperator = true;
    this.bwrange = { start: null, end: null };
    if (this.SelectedDocuments.length === 1 && this.SelectedDocuments != null && this.SelectedDocuments != undefined) {
      this.gridData = [];
      this.decumentDetails = [];
      this.items = [{ title: "Search & Download", isShow: true, expanded: true, keepItemContent: true }]
      this.selectedLocationList = this.listItems;
      let documentsIds = this.SelectedDocuments.toString();
      this.loadDocumentsProperties(documentsIds);

    }
  }

  stringDateChange(event, operator) {
    this.CheckDocStatus(event, operator);
  }

  CheckDocStatus(event, operator) {
    if (operator === "Between") {
      this.allOperator = false;
      this.betweenOperator = true;
      this.bwrange = { start: null, end: null };
    }
    else {
      this.betweenOperator = false;
      this.allOperator = true;
      this.bwrange = { start: null, end: null };
    }
    let OperatorValue;
    if (operator.value === undefined) {
      OperatorValue = {
        key: '',
        value: operator
      };
    }
    else {
      OperatorValue = operator;
    }

    let index = this.decumentDetails.findIndex(element => element["PropertyID"] == event["PropertyId"]);
    if (index === -1) {
      let payload = { DocumentTypeID: this.SelectedDocuments.toString(), PropertyID: event.PropertyId, DataTypeVal: event.DataType, Operator: OperatorValue.value, SearchVal: this.DefaultInputSerachText };
      this.decumentDetails.push(payload);
    }
    else {
      this.decumentDetails[index].Operator = OperatorValue.value;
    }
  }

  CheckSearchStatus(event, operator) {
    operator = operator.trim();
    if ((event.DataType === 1 && event.PropertyName != 'Location ID') || (event.DataType === 2 && event.PropertyName != 'Location ID') || (event.DataType === 3 && event.PropertyName != 'Location ID') || (event.DataType === 4 && event.PropertyName != 'Location ID')) {
      this.defaultPropety = this.DateNumberDropdown[4];
    }
    else if (event.DataType === 0 && event.PropertyName != 'Location ID') {
      this.defaultPropety = this.StringDataTypeDropdown[0].value;
    }
    else if (event.PropertyName === 'Location ID') {
      this.defaultPropety = this.locationInDropdown[0];
    }
    let index = this.decumentDetails.findIndex(element => element["PropertyID"] == event["PropertyId"]);
    if (index === -1) {
      if (operator != "") {
        let payload = { DocumentTypeID: this.SelectedDocuments.toString(), PropertyID: event.PropertyId, DataTypeVal: event.DataType, Operator: this.defaultPropety, SearchVal: operator };
        this.decumentDetails.push(payload);
      }
    }
    else {
      this.decumentDetails[index].SearchVal = operator;
    }
  }
  stringChange(event, operator) {
    this.CheckDocStatus(event, operator);
    // CheckDocStatus
  }

  onKeyPress(data, item, field) {
    let fieldInput;
    if (field === 'searchBox') {
      fieldInput = data.target.value;
    }
    else if (field === 'numericbox') {
      fieldInput = data.value.toString();
    }
    else if (field === 'datePicker') {
      let date = new Date(data.value);
      fieldInput = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
      this.gridData.map(elemnet => {
        if (elemnet.PropertyName === item.PropertyName && elemnet.DataType === item.DataType) {
          elemnet['date'] = date;
        }
      });
    }
    this.CheckSearchStatus(item, fieldInput)
  }

  showRecords() {
    let docDetails = [];
    this.decumentDetails.map(elem => {
      if (elem.Operator === "Between") {
        if (this.bwrange.start != null && this.bwrange.end != null) {
          let bwStart = new Date(this.bwrange.start);
          let bwEnd = new Date(this.bwrange.end);
          elem.SearchVal = bwStart.getFullYear() + "-" + (bwStart.getMonth() + 1) + "-" + bwStart.getDate() + ","
            + bwEnd.getFullYear() + "-" + (bwEnd.getMonth() + 1) + "-" + bwEnd.getDate();
        }
      }
      if (elem.SearchVal != undefined && elem.SearchVal != "") {
        docDetails.push(elem);
      }
    });
    if (docDetails.length > 0) {
      docDetails.map((elem, index) => {
        elem["ID"] = index + 1;
      });
      let docData = JSON.stringify(docDetails);
      this.coredataservice.getDocumentDetails(docData).pipe(first()).subscribe(res => {
        if (res != null && res != undefined) {
          if (res.StatusCode === 200) {
            let removeduplicateDocumentId = [];
            this.docDetailsByProperties = res.response;
            this.docDetailsByProperties.map(element => {
              let indexofElement = removeduplicateDocumentId.findIndex(data => data.DocumentId === element.DocumentId);
              if (indexofElement === -1) {
                removeduplicateDocumentId.push(element);
              }
            });
            if (removeduplicateDocumentId.length > 200) {
              this.docDetailsByProperties = removeduplicateDocumentId.slice(0, 200);
              this.shareData.showInfo("There are more than 200 documents available, we're showing only 200. For desired document, please refine the search criteria");
            }
            else {
              this.docDetailsByProperties = removeduplicateDocumentId;
            }
            this.DocumentPropertiesListgridData = this.docDetailsByProperties;
            //MS- resetting the state of the Grid (filters) when loading the Grid with new Data everytime.
            this.state = {
              skip: 0,
              take: 10,
              filter: {
                logic: 'and',
                filters: []
              }
            }

            this.gridDataDetails = process(this.DocumentPropertiesListgridData, this.state);
            // let array = [{ title: "Search & Download", isShow: false, expanded: false, keepItemContent: true }, { title: "Search Results", expanded: true, keepItemContent: true }];
            // this.items = [];
            let array = { title: "Search Results", expanded: true, keepItemContent: true };
            this.items[0].expanded = false;
            this.items[0].isShow = false;
            this.items[1] = array;
          }
        }
      },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        });
    }

    else if (this.gridData.length === 0) {
      this.shareData.showWarning("Please Select the Document");
    }
    else if (docDetails.length === 0 && this.gridData.length > 0) {
      this.shareData.showWarning("Please Insert the Criteria");
    }
  }

  public open(dataItem) {
    this.opened = true;
    this.locationsData = dataItem;
    this.tempSelectedLocationList = [];
    this.tempSelectedLocationList = this.selectedLocationList;
    if (this.selectedLocationList.length === 1) {
      let index = this.LocationData.findIndex(data => data.LocationID === this.selectedLocationList[0].LocationID);
      this.LocationData[index]['disabled'] = true;
      this.checkIsListSelectedAll();
    }
  }

  SetLocations(value) {
    let locationIds = [];
    this.selectedLocationList = [];
    this.selectedLocationList = this.tempSelectedLocationList;
    this.selectedLocationList.map(eleme => {
      locationIds.push(eleme.LocationCode);
    }
    );
    if (this.locationsData === undefined) {
      this.locationsData = {
        DataType: 0,
        PropertyId: 50,
        PropertyName: "Location ID",
        UserPrompt: "Location ID"
      }
    }
    if (value === undefined) {
      this.opened = false;
      this.checkIsListSelectedAll();
    }
    this.CheckSearchStatus(this.locationsData, locationIds.toString());
  }
  loadDocumentsProperties(ids) {
    this.coredataservice.getPropertiesByDocID(ids).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          this.DocumentProperties = res.response;
          this.gridData = this.DocumentProperties;
          this.gridData.sort(function (a, b) {
            var nameA = a.PropertyName.toLowerCase(), nameB = b.PropertyName.toLowerCase()
            if (nameA < nameB) //sort string ascending
              return -1
            if (nameA > nameB)
              return 1
            return 0 //default return value (no sorting)
          });
          let locationIndex = this.gridData.findIndex(elem => elem.PropertyName === 'Location ID');
          if (locationIndex != -1) {
            let location = this.gridData.splice(locationIndex, 1);
            this.gridData.unshift(location[0]);
          }
          if (locationIndex != -1) {
            this.SetLocations(undefined);
            this.checkIsListSelectedAll();
          }
        }
      }
    },
      error => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      });
  }
  onOpenDatePicker(data, isEdited) {
    if (isEdited === undefined) {
      let fieldInput: string;
      if (data != null && data != undefined) {
        let currentDate = new Date();
        this.date = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        fieldInput = this.date.getFullYear() + "-" + (this.date.getMonth() + 1) + "-" + this.date.getDate();
        this.gridData.map(elemnet => {
          if (elemnet.PropertyName === data.PropertyName && elemnet.DataType === data.DataType) {
            elemnet['date'] = this.date;
            elemnet['isEdited'] = true
          }
        });
        this.CheckSearchStatus(data, fieldInput);
      }
    }
  }

  OnSelectAllChange() {
    this.hasAllValueSelected = true;
    this.tempSelectedLocationList = this.LocationData;
    //this.selectedLocationList = this.LocationData;
  }

  OnDeSelectAllChange() {
    if (this.LocationData.length != 1) {
      this.hasAllValueSelected = false;
      this.hasAllValueSelected = false;
      //this.selectedLocationList = [this.listItems[0]];
      this.tempSelectedLocationList = [this.listItems[0]];
      this.LocationData.map(data => {
        data['disabled'] = false;
      });
      this.LocationData[0]['disabled'] = true;
      this.SetLocations("Deselect");
    }
  }

  checkIsListSelectedAll() {
    if (this.LocationData.length === this.selectedLocationList.length) {
      this.hasAllValueSelected = true;
    }
    else if (this.selectedLocationList.length < this.LocationData.length) {
      this.hasAllValueSelected = false;
    }
  }

  public setSelectableSettings(): void {
    this.selectableSettings = {
      checkboxOnly: true,
      mode: 'multiple'
    };
  }
  // locationChange(event) {
  //  }
  locationChange(event) {
    this.loadCustomerLocationDropdown(this.tokenData.UserID, event);
  }

  ngOnDestroy() {
    // this.subscriptionLocationsList.unsubscribe();
  }
  filterLocation(value) {
    // this.LocationData = this.listItems.filter((s) => s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1);
    this.LocationData = this.listItems.filter((s) => 
    s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
    s.CustName.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
    s.CustomerCode.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
    s.LocationCode.toLowerCase().indexOf(value.toLowerCase()) !== -1
);
  }
  public onPanelChange(event: any) {

    this.isExpanded = event[0].expanded;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;

    }

  }
}

