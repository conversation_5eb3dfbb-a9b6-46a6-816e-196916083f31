 p
 {
     color: white !important;
 }
 .auto-padding p{
    padding: 10px !important;
 }
  a
 {
    font-size: 0.9em !important;

    margin-top: 0px;
    padding: 0px !important;
     color: white !important;
 }
.footer{
    margin: 0;
    padding: 0px;
}

// @media (min-width: 1454px) and (max-width: 1601px) {
//   .footer {
//     margin-left: 172px;
//   }
// }

// @media only screen and (min-width: 1601px) and (max-width: 1800px)  {
//   .footer {
//     margin-left: 188px;
//   }
// }

// @media only screen and (min-width: 1801px) and (max-width: 1920px) {
//   .footer {
//     margin-left: 198px;
//   }
// }

// @media only screen and (min-width: 1921px) and (max-width: 2115px) {
//   .footer {
//     margin-left: 218px;
//   }
// }

// @media only screen and (min-width: 2115px) {
//   .footer {
//     margin-left: 274px;
//   }
// }