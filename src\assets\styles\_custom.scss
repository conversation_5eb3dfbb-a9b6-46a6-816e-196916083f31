/* your styles go here */

.count-title {
  padding-top: 18px;
}
ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

// li {
//     float: left;
//     align-items: center;
// }

li a {
  display: block;
  color: white;
  text-align: center;
  padding: 18px;
  text-decoration: none;
}

.page {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transition: width 0.3s linear;
  transition: width 0.3s linear;
  width: calc(100% - 200px);
  background-color: #f4f7fa;
  min-height: 100vh;
  padding-bottom: 50px;
}

// li a:hover {
//   /*background-color: #111111;*/
// }
#outstandingQuote {
  font-family: calibri light , "Roboto";
  border-collapse: collapse;
  width: 100%;
}

#outstandingQuote td,
#outstandingQuote th {
  border: 1px solid #ddd;
  padding: 8px;
}

#outstandingQuote tr:nth-child(even) {
  background-color: #f2f2f2;
}

#outstandingQuote tr:hover {
  background-color: #ddd;
}

#outstandingQuote th {
  padding-top: 12px;
  padding-bottom: 12px;
  // text-align: left;
  background-color: #4caf50;
  color: white;
}

.DasBoardDialog .StateLocation
{
  width: 137px !important;
  position: relative !important;
  left: 23px !important;
}
.DasBoardDialog .StateLocation span
{
  font-size: 12px !important;
  color: white !important;

}
.DasBoardDialog .k-dropdown-wrap {
  outline: 0;
  border-bottom-color: white;
  cursor: pointer;
}




.ExportToExcelButtonCss {
  background: #00ccff !important;
  // background: rgb(0, 147, 215) !important;
  color: rgb(255, 255, 255) !important;
  font-family: calibri light , "Roboto" !important;
  /* width: 24%; */
  padding: 5px 13px !important;
  font-size: 10px !important;
  font-weight: 600 !important;
  border-radius: 4px !important;
}



.k-tooltip-wrapper
{
  width: 24rem !important;
  left: 47px !important;
}

 
.k-tooltip-wrapper .k-tooltip {
  font-size: 12px !important;
  padding: 2px 5px !important;
}


.DisabledTemplate > li
{
  pointer-events: none !important;
}
  
 .selectAllButtonCss
{
  background: #00ccff !important;
  font-size: 91% !important;
  color: white !important;
  padding: 2px 8px !important;
  font-family: calibri light , "Roboto" !important;
}
 .deselectAllButtonCss
{
  background-color: #00ccff !important;
  font-size: 91% !important;
  color: white !important;
  padding: 2px 8px !important;
  font-family: calibri light , "Roboto" !important;
}



.k-breadcrumb-container {
  display: flex;
  list-style-type: none;
  margin-top: 6px;
  margin-bottom: 0;
  // margin: 0;
  padding: 0;
  .k-cursor-pointer  {
    cursor: pointer;
  }
  .k-breadcrumb-item {
    margin: 5px;
  }
  .k-i-arrow-chevron-right:before, .k-i-arrowhead-e:before {
    content: "\E014";
    color: black;
  }
}




@media only screen and (max-width: 767px)
{
  .mutiselectTextPosition[_ngcontent-c10] {
 
    font-size: 9px !important;
   
}
.col-3 button
{
  position: relative !important;
  right: 12px !important;
}
.md-size
{
  font-size: 9px !important;
}
.span-content
{
  font-size: 10px !important;
}
.location-position
{
  position: relative !important;
  right: 13px !important;
}
.k-button
{
  font-size: 9px !important;

}
}



@media only screen and (max-width: 770px)
{
.priorityCss .k-dropdown-wrap
{
  font-size: 11px !important;
}
}




@media only screen and (max-width:992px)
{
  .quoteStatusGraphheight
  {
    height: 94% !important;
  }

}
@media only screen and (max-width:767px)
{
  .margin-bottom-cards
  {
    margin-bottom: -25px !important;

  }

}