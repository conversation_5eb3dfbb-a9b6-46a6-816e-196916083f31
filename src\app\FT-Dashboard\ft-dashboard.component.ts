import { ChangeDetectorRef, Component, EventEmitter, Inject, OnDestroy, OnInit, Output, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { LegendTitle, SeriesClickEvent, SeriesLabels, SeriesLabelsVisualArgs, SeriesPoint } from '@progress/kendo-angular-charts';
import { StatusCodes } from '../Common/shared';
import { CoreDataService } from '../Services/core-data.service';
import { SharedDataService } from '../Services/shared-data.service';
import { GetFtPPADataModel, ServiceCallGraphDataModel } from './ft-dashboard.model';
import { AxisLabelVisualArgs } from "@progress/kendo-angular-charts";
import {
  ApexAxisChartSeries,
  ApexChart,
  ChartComponent,
  ApexDataLabels,
  ApexPlotOptions,
  ApexYAxis,
  ApexXAxis,
  ApexTooltip,
  ApexLegend
} from "ng-apexcharts";
import { locationDateFilterEntity } from '../Dashboard/dashboard.model';
import { LineCap, RadialLabels } from '@progress/kendo-angular-gauges';
import { first } from 'rxjs/operators';
import { DataStateChangeEvent, GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';
import { State } from '@progress/kendo-data-query';
import { process, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { ActivatedRoute, Router } from '@angular/router';
import { saveAs } from "file-saver";
import * as Highcharts from 'highcharts/highstock';
import { ToastrService } from 'ngx-toastr';
import { AuthenticationService } from '../Services/authentication.service';
import { DatePipe } from '@angular/common';
import { HeaderComponent } from '../Common/header/header.component';
import { SessionStorageService } from 'ngx-webstorage';
import { DOCUMENT } from "@angular/common";
import { DialogService } from '@progress/kendo-angular-dialog';
import { animate, state, style, transition, trigger } from '@angular/animations';
interface EquipmentTasks {
  Equipment: string;
  TasksCompleted: number;
  BacklogTasks: number;
  TuningTasks: number;
}
export interface EquipmentThermalComfort {
  Equipment: string;
  PerOfOccupancy: number;
  TotalHours: number;
  TooHotHours: number;
  TooColdHours: number;
  TasksCompleted: number;
  BacklogTasks: number;
}
declare module 'highcharts' {
  interface Point {
    arrow?: Highcharts.SVGElement;
    graphic?: Highcharts.SVGElement;
  }
}
@Component({
  selector: 'app-ft-dashboard',
  templateUrl: './ft-dashboard.component.html',
  styleUrls: ['./ft-dashboard.component.scss'],
  // encapsulation: ViewEncapsulation.None
  animations: [
      trigger('expandCollapse', [
        state('expanded', style({
          height: '*',
          opacity: 1,
          overflow: 'hidden',
          padding: '15px'
        })),
        state('collapsed', style({
          height: '0px',
          opacity: 0,
          overflow: 'hidden',
          padding: '0 15px'
        })),
        transition('expanded <=> collapsed', [
          animate('300ms ease-in-out')
        ])
      ])
    ],
  })

export class FTDashboardComponent implements OnInit, OnDestroy {

  panels: any[] = [
    { title: 'Asset Performance', key: 'assetPerformance' },
    { title: 'Workflow', key: 'workflow' },
    { title: 'Backlog', key: 'backlog' },
    { title: 'Tuning', key: 'tuning' },
    {title: 'Awaiting Approval', key: 'awaitingApproval'}
  ];

  collapsedSections: { [key: string]: boolean } = {};

  toggleCollapse(key: string) {
    const current = this.collapsedSections[key];
    const newValue = !current;

    this.collapsedSections[key] = newValue;

    // This updates the shared state
    this.shareData.updateToggle(key, newValue);
}

  AnalyticRuleIssue!: string;
  loc!: string;
  lastTCompletedFilteredRecord: any = null;
  lastTInBacklogFilteredRecord: any = null;
  ServiceCallGraphData = [];
  EquipmentThermalComfortData_Sorted = [];
  hotSeries: any;
  coldSeries: any;
  equipcategories: any;
  EquipmentThermalComfortdata = [
  ];

  EquipmentThermalComfortTabledata: EquipmentThermalComfort[] = []



  EquipmentComfortList = []
  TotalRealisedEnergyWaste: number;
  TotalUnRealisedEnergyWaste: number;
  ReductionInServiceCalls: number;
  ThermalComfortCount: number;
  EquipmentReliabilityCount: number;
  EnergySavingCount: number;
  TotalProblemAddressedData = [];
  TotalProblemIdentifiedData = [];
  TotalTaskBacklogData = [];
  SumOfTasksBacklog: number = 0;
  SumOfTasksProposed: number = 0;

  getIMDataDrivenTaskCompleteList: any[];
  getIMDataDrivenTaskBacklogList: any[];
  getIMDataDrivenTaskTuningList: any[];
  getIMDataDrivenTAwaitingApprovalList: GetFtPPADataModel[];


  IsOneMonthSiteComfort: boolean = true;
  IsThreeMonthSiteComfort: boolean = false;
  IsSixMonthSiteComfort: boolean = false;
  IsTwelveMonthComfort: boolean = false;

  IsThreeDayMovingAverage: boolean = true;
  IsOneWeekMovingAverage: boolean = false;
  IsOneMonthMovingAverage: boolean = false;
  IsThreeMonthMovingAverage: boolean = false;

  OneMonthSiteData = [];
  ThreeMonthSiteData = [];
  SixMonthSiteData = [];
  TwelveMonthSiteData = [];

  ThreeMonthSiteDates: string[] = [];
  SixMonthSiteDates: string[] = [];
  TwelveMonthSiteDates: string[] = [];

  getThreeMonthMovingAverage = [];
  getSixMonthMovingAverage = [];
  getTwelveMonthMovingAverage = [];
  getOneMonthMovingAverage = [];
  getTwelveMonthChangePercentage: number;
  completedCounts: any
  backlogCounts: any
  tuningCounts: any
  private suffix = "%";
  animateChart: boolean = true;

  getSiteComfortData = [];
  ThermalComfortCountWaitingAction: number;
  EquipmentReliabilityCountWaitingAction: number;
  EnergySavingCountWaitingAction: number;
  HVACAssetPerformanceScore: number;
  config: any;
  Co2Reduction: number;
  Co2ReductionUnit: any;
  lessServiceCallCount: number;
  legendMarker: any = {
    type: "circle",
    height: 11,
    width: 11
  }
  fromDateFilter: any;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  public range: any;
  originalDate = new Date();
  locations = [];
  startDate: string;
  endDate: string;
  HVACStatus: string;
  ServiceCallCategory = [];
  public SumHistoricalBaseline = 0;
  public SumCurrentServiceCalls = 0;

  public HVACColors = [];
  public startAngle = 0;
  public endAngle = 180;
  public rangeSize = 50;
  public target;
  public HitsPerAssetRatio;
  public HitsPerAssetOccurences;
  public ticksColor = '#342342';

  public IMDataDrivenTCompleteListState: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public IMDataDrivenTTuningListState: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public EquipComfortState: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };

  public IMDataDrivenTBacklogListState: State = {
    skip: 0,
    //take: 10,
    sort: [{ field: 'DateLastHit', dir: 'desc' }],
    filter: {
      logic: 'and',
      filters: []
    }
  };

  public gridDataForIMCompletedList: GridDataResult;
  public gridDataForIMBacklogList: GridDataResult;
  public gridDataForIMTuningList: GridDataResult;

  public IMDataDrivenTAwaitingListState: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridDataForIMAwaitingList: GridDataResult;
  public equipComfortGrid: GridDataResult;
  SelectedRowData: any;
  SelectBacklogRowData: any;
  buildingFilterIMCompleteList = [];
  equipmentFilterIMCompleteList = [];
  taskTypeFilterIMCompleteList = [];
  outcomeFilterIMCompleteList = [];

  buildingFilterIMBacklogList = [];
  equipmentFilterIMBacklogList = [];
  taskTypeFilterIMBacklogList = [];

  buildingFilterIMTuningList = [];
  equipmentFilterIMTuningList = [];
  taskTypeFilterIMTuningList = [];
  outcomeFilterIMTuningList = [];

  buildingFilterIMAwaitingList = [];
  equipmentFilterIMAwaitingList = [];
  taskTypeFilterAwaitingList = [];
  resolvedOutcomeDataCount: number;
  improvedOutcomeDataCount: number;
  quoteRequiredOutcomeDataCount: number;
  escalatedOutcomeDataCount: number;
  ImTaskQtyDataCount: number;
  NoAccessOutcomeDataCount: number
  ExpiredOutcomeDataCount: number;
  LostOutcomeDataCount: number;
  WonOutcomeDataCount: number;
  AwaitingApprovalDataCount: number;
  FT2_Total: number = 0;
  REFGEN_NEXTRUN: string = '';
  DDC_NEXTRUN: string = ''
  REFGEN_TASKS: string
  DDC_TASKS: string = ''
  IMDataDrivenTaskTypeList = [];
  IMDataDrivenTaskBacklogList = [];
  draggedItemIndex: number | null = null;
  priorityLevels = ['High', 'Medium', 'Low'];
  priorityItems = [
    { label: 'Equipment Reliability', priority: 'High', icon: '../../../assets/images/equipment.png' },
    { label: 'Thermal Comfort', priority: 'Medium', icon: '../../../assets/images/thermal-comfort.png' },
    { label: 'Energy Waste', priority: 'Low', icon: '../../../assets/images/energy-waste.png' }
  ];
  serviceCallMarginUnit = 1;
  AATotalUnrealisedEnergyWaste: number;
  AATotalUnrealisedEnergyWasteAvoidanceKWH: number;

  UnrealisedEnergyWasteCostAvoidanceBacklog: number;
  UnrealisedEnergyWasteCostAvoidanceKWHBacklog: number;

  ThermalComfortBacklogCount: number;
  EnergySavingBacklogCount: number;
  EquipmentReliablityBacklogCount: number;
  IMTaskQTyStyling: boolean = false;

  resolved: any = { label: 'RESOLVED', isSelected: false };
  improved: any = { label: 'IMPROVED', isSelected: false };
  escalated: any = { label: 'FURTHER CONSIDERATION REQ.', isSelected: false };
  noAccess: any = { label: 'NO ACCESS', isSelected: false };
  quotesRequired: any = { label: 'QUOTE REQUIRED', isSelected: false };
  awaitingApproval: any = { label: 'AWAITING APPROVAL', isSelected: false };
  quotesAccepted: any = { label: 'QUOTE ACCEPTED', isSelected: false };
  quotesExpired: any = { label: 'QUOTE EXPIRED', isSelected: false };
  quotesNotAccepted: any = { label: 'QUOTE NOT ACCEPTED', isSelected: false };
  resolvedOnPreIMTask: any = { label: 'RESOLVED ON PREV IM TASK', isSelected: false };
  qtAwaitingApproval: any = { label: 'QT_AWAITING APPROVAL', isSelected: false };
  openIMTasks: any = { label: 'OPEN TASK', isSelected: false };

  causedByPlant: any = { label: 'CAUSED BY PLANT CONFIG/DESIGN/USE', isSelected: false };
  causedByEquipment: any = { label: 'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE', isSelected: false };
  causedByInstallation: any = { label: 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR', isSelected: false };
  equipmentDeCommissioned: any = { label: 'EQUIPMENT DE-COMMISSIONED', isSelected: false };




  selectedWidgets: any[] = [];
  popupContent: string;
  showPopup: boolean = false;
  popupTaskDescriptionContent: string;
  showPopupTaskDescription: boolean = false;
  movingAverageCount: number;
  userRole: any;
  outcomeContent: string;
  outDexID: any;
  backlogRecordID: any;
  showOutcomePopup: boolean = false;
  showBacklogOutcomePopup: boolean = false;
  popupOutcomeContent: string;
  showPopupOutcome: boolean = false;
  allTaskTypeData: any[] = [];
  allBacklogTaskTypeData: any[] = [];
  getAwaitingListCount: any[] = [];
  getTaskTpeOnEquipments: any[] = [];
  isEquipment: boolean = false;
  equipment = []
  filteredEquipment = [];
  searchTerm: string = '';
  selectAll: boolean = false;
  lastConfirmedSelection: any[] = [];
  isTuningModuleActive: boolean = false;
  @Output() equipmentsData = new EventEmitter();
  constructor(@Inject(DOCUMENT) private document: Document, private coreDataService: CoreDataService, private shareData: SharedDataService,
    private sessionStorage: SessionStorageService,
    private spinner: SpinnerVisibilityService,
    private route: ActivatedRoute,
    private router: Router, private toastrService: ToastrService, private authService: AuthenticationService, private datePipe: DatePipe) {
    this.clearWidgets();
    this.shareData.removeBacklogData();
    this.fromDateFilter = this.shareData.getStartDateByUser();
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (this.locationDateFilterData === null) {
      if (this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == "") {
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations.split(",").map(el => parseInt(el));
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
    this.userRole = this.authService.getTokenData();

    this.route.queryParamMap.subscribe(params => {
      this.AnalyticRuleIssue = params.get('issue');
      this.loc = params.get('loc');
      if (this.loc != null) {
        localStorage.setItem("location", JSON.stringify({
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate()),
          locations: params.get('loc')}));
      }
    });
  }

  async ngOnInit(): Promise<void> {
    this.shareData.toggles$.subscribe(values => {
      this.collapsedSections = { ...values };
    });

    this.isTuningModuleActive = JSON.parse(localStorage.getItem("TuningModule"));
    if (this.AnalyticRuleIssue == null || this.loc == null) {

      await this.loadAllAPIs();

      // this.createEquipmentRunningTooHotCold();
      localStorage.setItem("IsIMDataUpdated", JSON.stringify(true));
      this.allSelectedEquipments = [];
      this.getCompleteList = [];
      this.getAwaitingListCount = [];
      this.getTaskTpeOnEquipments = [];
    }
  }

  ngAfterViewInit(): void {
    if (this.loc !== null && this.AnalyticRuleIssue !== null) {
      this.locationChange(localStorage.getItem("location"));

      this.IMDataDrivenTBacklogListState.filter = {
        logic: 'and',
        filters: [
          {
            logic: 'or',
            filters: [{
              field: 'TaskType',
              operator: 'eq',
              value: this.AnalyticRuleIssue
            }]
          }
        ]
      };
      const tableElement = document.getElementById('BacklogTable');
      if (tableElement) {
        tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  }

  async loadAllAPIs(): Promise<void> {
    try {
      const startDate = new Date(this.range["start"]);
      const endDate = new Date(this.range["end"]);
      const locations = this.locations;

      const [
        serviceGraph,
        completedList,
        tuningList,
        awaitingList,
        performance,
        taskType,
        backlogData,
        backlogList,
        _
      ] = await Promise.all([
        this.coreDataService.getServiceCallGraphData(locations).pipe().toPromise(),
        this.coreDataService.getFtPPAData("Complete", locations, startDate, endDate).toPromise(),
        this.coreDataService.getFtPPAData("Tuning", locations, startDate, endDate).toPromise(),
        this.coreDataService.getFtPPAData("Awaiting Approval", locations, startDate, endDate).toPromise(),
        this.coreDataService.getPerformanceData(locations, startDate, endDate).toPromise(),
        this.coreDataService.getIMDataDrivenTaskTypeData("Complete", locations, startDate, endDate).toPromise(),
        this.coreDataService.getIMDataDrivenTaskTypeData("Backlog", locations, startDate, endDate).toPromise(),
        this.coreDataService.getFtPPAData("Backlog", locations, startDate, endDate).toPromise(),
        this.GetAuxiliaryInformation()
      ]);

      this.getServiceCallGraphData(serviceGraph);
      this.getIMDataDrivenTCompletedList(completedList);
      this.getIMDataDrivenTTuningList(tuningList);
      this.getIMDataDrivenTAwaitingApplList(awaitingList);
      this.getPerformanceData(performance);
      this.getIMDataDrivenTaskTypeData(taskType);
      this.getIMDataDrivenTaskBacklogData(backlogData);
      this.getIMDataDrivenTBacklogList(backlogList);

    } catch (error) {
      this.shareData.ErrorHandler(error);
    }
  }


  async loadGetIMDataDrivenTCompletedList(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getFtPPAData("Complete", this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getIMDataDrivenTCompletedList(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }

  async loadGetPerformanceData(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getPerformanceData(this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getPerformanceData(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }

  async loadGetIMDataDrivenTBacklogList(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getFtPPAData("Backlog", this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getIMDataDrivenTBacklogList(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }

  async loadGetIMDataDrivenTaskTypeData(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getIMDataDrivenTaskTypeData("Complete", this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getIMDataDrivenTaskTypeData(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }

  async loadGetIMDataDrivenTaskBacklogData(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getIMDataDrivenTaskTypeData("Backlog", this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getIMDataDrivenTaskBacklogData(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }

  async loadGetIMDataDrivenTTuningList(): Promise<void> {
    try {
      const res = await this.coreDataService
        .getFtPPAData("Tuning", this.locations, new Date(this.range["start"]), new Date(this.range["end"]))
        .toPromise();
      this.getIMDataDrivenTTuningList(res);
    } catch (err) {
      this.shareData.ErrorHandler(err);
    }
  }


  public majorGridLines = {
    color: 'black',
    visible: false
  }
  public minorGridLines = {
    color: 'lightgray',
    visible: false
  }

  public labelContent(e: SeriesLabelsVisualArgs): string {
    return e.value;
  }
  public color = (e: SeriesPoint): string => {
    switch (e.dataItem.value) {
      case 1:
        return 'rgba(99,190,123,255)';
      case 2:
        return 'rgba(133,200,125,255)';
      case 3:
        return 'rgba(168,210,127,255)';
      case 4:
        return 'rgba(203,220,129,255)';
      case 5:
        return 'rgba(237,230,131,255)';
      case 6:
        return 'rgba(255,221,130,255)';
      case 7:
        return 'rgba(253,192,124,255)';
      case 8:
        return 'rgba(252,163,119,255)';
      default:
      case 9:
        return 'rgba(250,134,113,255)';
      case 10:
        return 'rgba(248,105,107,255)';
    }
  };



  // getting Service Call Data
  getServiceCallGraphData(response) {
    // this.coreDataService.getServiceCallGraphData(this.locations).pipe(first()).subscribe(
    //   (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          let ServiceData = [];
          for (let value of response.response) {
            const { ServiceCalls, ...others } = value;
            ServiceData.push({
              name: value.ServiceCalls,
              data: Object.values(others)
            })
          }
          this.getServiceCallCategory();
          ServiceData[0].data = this.ShuffleCurrentServiceCallData(ServiceData);
          ServiceData[1].data = this.ShuffleHistoricalData(ServiceData);
          let swapData = ServiceData;
          const temp = swapData[0];
          swapData[0] = swapData[1];
          swapData[1] = temp;
          this.ServiceCallGraphData = swapData;

          let serviceCallData = this.ServiceCallGraphData;

          this.CreatMajorUnitForServiceCall(serviceCallData);

          this.ServiceCallGraphData[0].data.forEach(x => {
            this.SumHistoricalBaseline += x;
          });
          this.ServiceCallGraphData[1].data.forEach(x => {
            this.SumCurrentServiceCalls += x;
          });
          this.lessServiceCallCount = 0;
          if (this.SumHistoricalBaseline - this.SumCurrentServiceCalls) {
            this.lessServiceCallCount = this.SumHistoricalBaseline - this.SumCurrentServiceCalls
            this.SumHistoricalBaseline = 0
            this.SumCurrentServiceCalls = 0
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
      // },
      // error => {
      //   if (error) {
      //     this.shareData.ErrorHandler(error);
      //   }
      // }
    // )
  }
  togglePriority(dataItem: any, isPriority: boolean): void {
    dataItem.Priority = isPriority; // Update the priority state
    const data = { ID: dataItem.ID, Value: dataItem.Priority }
    this.coreDataService.UpdateBacklogPriority(data).subscribe((res) => {
      if (res.StatusCode == StatusCodes.OK) {
        this.shareData.showSuccess("Priority Updated Successfully");
      } else {
        this.shareData.showError("Unable to update priority, Please contact support.");
      }
    })
  }

  async GetAuxiliaryInformation(): Promise<void> {
    this.REFGEN_NEXTRUN = ''
    this.DDC_NEXTRUN = ''
    this.REFGEN_TASKS = ''
    this.DDC_TASKS = ''
    const allLocations = this.sessionStorage.retrieve('locations');
    const filteredLocations = allLocations?.filter((res) =>
      this.locations.includes(parseInt(res.LocationID?.trim()))
    );

    // Concatenate LocationCodes with a comma
    const locationCodes = filteredLocations?.map(res => res.LocationCode).join(',');

    if (locationCodes) {
      try {
        const res = await this.coreDataService.GetFTAuxiliaryData(locationCodes).toPromise();
        if (res.StatusCode == StatusCodes.OK) {
          // this.FT2_Total = res.response.find((item) => item.Type === "FT2_Total")?.Value;
          this.REFGEN_NEXTRUN = res.response.find((item) => item.Type === "REFGEN")?.Value;
          this.DDC_NEXTRUN = res.response.find((item) => item.Type === "DDC")?.Value;
          this.REFGEN_TASKS = res.response.find((item) => item.Type === "REFGEN_TASKS")?.Value;
          this.DDC_TASKS = res.response.find((item) => item.Type === "DDC_TASKS")?.Value;
        }
      }
      catch (error) {
        this.shareData?.ErrorHandler?.(error);
      }
    }
  }

  // getting performance data
  getPerformanceData(response) {
    // this.coreDataService.getPerformanceData(this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          this.TotalRealisedEnergyWaste = response.response.WasteAndServiceCalls.TotalRealisedEnergyWaste;
          this.TotalUnRealisedEnergyWaste = response.response.WasteAndServiceCalls.TotalUnRealisedEnergyWaste;
          this.ReductionInServiceCalls = response.response.WasteAndServiceCalls.ReductionInServiceCalls;
          this.ThermalComfortCount = response.response.WasteAndServiceCalls.TotalUnrealisedEnergyWasteOutstanding_kWh;
          this.EquipmentReliabilityCount = response.response.WasteAndServiceCalls.TotalRealisedEnergyWasteAvoidance_kWh;
          this.EnergySavingCount = response.response.CompleteAction.EnergySavingCount;
          this.Co2Reduction = response.response.WasteAndServiceCalls.Co2Reduction;
          this.Co2ReductionUnit = response.response.WasteAndServiceCalls.Co2ReductionUnit;

          this.AATotalUnrealisedEnergyWaste = response.response.WasteAndServiceCalls.AATotalUnRealisedEnergyWaste;
          this.AATotalUnrealisedEnergyWasteAvoidanceKWH = response.response.WasteAndServiceCalls.AATotalUnrealisedEnergyWasteOutstanding_kWh

          this.UnrealisedEnergyWasteCostAvoidanceBacklog = response.response.WasteAndServiceCalls.UnrealisedEnergyWasteCostAvoidanceBacklog;
          this.UnrealisedEnergyWasteCostAvoidanceKWHBacklog = response.response.WasteAndServiceCalls.UnrealisedEnergyWasteCostAvoidanceKWHBacklog

          let DataForCompleteAction = [];
          DataForCompleteAction = [{
            name: 'Equipment Reliablity', value: response.response.CompleteAction.EquipmentReliabilityCount
          }, {
            name: 'Thermal Comfort', value: response.response.CompleteAction.ThermalComfortCount
          }, {
            name: 'Energy', value: this.EnergySavingCount
          }];
          this.TotalProblemAddressedData = DataForCompleteAction;

          this.ThermalComfortCountWaitingAction = response.response.WaitingAction.ThermalComfortCountWaitingAction;
          this.EquipmentReliabilityCountWaitingAction = response.response.WaitingAction.EquipmentReliabilityCountWaitingAction;
          this.EnergySavingCountWaitingAction = response.response.WaitingAction.EnergySavingCountWaitingAction;

          let DataForWaitingAction = [];
          DataForWaitingAction = [{
            name: 'Equipment Reliablity', value: this.EquipmentReliabilityCountWaitingAction
          }, {
            name: 'Thermal Comfort', value: this.ThermalComfortCountWaitingAction
          }, {
            name: 'Energy', value: this.EnergySavingCountWaitingAction
          }];
          this.TotalProblemIdentifiedData = DataForWaitingAction;
          this.SumOfTasksProposed = this.TotalProblemIdentifiedData.reduce((sum, item) => sum + item.value, 0);

          this.ThermalComfortBacklogCount = response.response.BacklogAction.ThermalComfortCountBacklog;
          this.EquipmentReliablityBacklogCount = response.response.BacklogAction.EquipmentReliabilityCountBacklog
          this.EnergySavingBacklogCount = response.response.BacklogAction.EnergySavingCountBacklog;

          let DataForBacklogAction = [];
          DataForBacklogAction = [{
            name: 'Equipment Reliablity', value: this.EquipmentReliablityBacklogCount
          }, {
            name: 'Thermal Comfort', value: this.ThermalComfortBacklogCount
          }, {
            name: 'Energy', value: this.EnergySavingBacklogCount
          }];

          this.TotalTaskBacklogData = DataForBacklogAction;
          this.SumOfTasksBacklog = this.TotalTaskBacklogData.reduce((sum, item) => sum + item.value, 0);

          this.HVACAssetPerformanceScore = +response.response.HVACAssetPerformanceScore;
          this.target = +response.response.Target;
          this.HitsPerAssetRatio = +response.response.HitsPerAssetRatio.Issues
          this.HitsPerAssetOccurences = +response.response.HitsPerAssetRatio.Occurances
          let allColor = [];
          for (let i = -1; i < 101; i++) {
            if (i <= this.HVACAssetPerformanceScore) {
              allColor.push({
                from: i,
                to: i + 2,
                color: 'rgba(0,17,50)',
              });
            } else {
              allColor.push({
                from: i,
                to: i + 2,
                color: '#e6e5e5',
              });
            }
          }
          this.getColorChange(allColor);
          this.getHVACStatus();

          this.getHVACAssetsPerformanceData();
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }

  //getting IM-Data-Driven Task Completed Table data
  getIMDataDrivenTCompletedList(response) {
    this.getIMDataDrivenTaskCompleteList = [];
    this.gridDataForIMCompletedList = undefined;
    this.buildingFilterIMCompleteList = [];
    this.equipmentFilterIMCompleteList = [];
    this.taskTypeFilterIMCompleteList = [];
    this.outcomeFilterIMCompleteList = [];
    this.resolvedOutcomeDataCount = 0;
    this.improvedOutcomeDataCount = 0;
    this.quoteRequiredOutcomeDataCount = 0;
    this.escalatedOutcomeDataCount = 0;
    this.NoAccessOutcomeDataCount = 0;
    this.ExpiredOutcomeDataCount = 0;
    this.LostOutcomeDataCount = 0;
    this.WonOutcomeDataCount = 0;
    this.AwaitingApprovalDataCount = 0;
    this.ImTaskQtyDataCount = 0;
    this.FT2_Total = 0;
    // this.coreDataService.getFtPPAData("Complete", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        const processedResponse = response.response.map((item) => {
          if (item.IsServiceCallOpen === 1) {
            item.WS_Response_String = 'OPEN TASK';
          }
          return item;
        });
        localStorage.setItem('CompleteList', JSON.stringify(processedResponse));
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            ///Count of Backlog

            const count = this.countTasksByEquipment(processedResponse, "TasksCompleted");
            this.completedCounts = count;

            // Iterate over the EquipmentThermalComfortTabledata to update or add new equipment counts
            // const updatedData = this.EquipmentThermalComfortTabledata.map(item => {
            //   const matchingTask = count[item.Equipment]; // Access the task count using equipment as the key
            //   return {
            //     ...item,
            //     TasksCompleted: matchingTask ? matchingTask.TasksCompleted : null,
            //   };
            // });

            // If any equipment from `count` is missing in the existing data, we need to insert it
            // const newData = Object.keys(count).reduce((acc, equipment) => {
            //   const matchingItem = updatedData.find(item => item.Equipment === equipment);
            //   if (!matchingItem) {
            //     acc.push({
            //       Equipment: equipment,
            //       PerOfOccupancy: 0, // Set default or modify as per your needs
            //       TotalHours: 0, // Set default or modify as per your needs
            //       TooHotHours: 0, // Set default or modify as per your needs
            //       TooColdHours: 0, // Set default or modify as per your needs
            //       TasksCompleted: count[equipment].TasksCompleted,
            //       BacklogTasks: null
            //     });
            //   }
            //   return acc;
            // }, []);

            // this.EquipmentThermalComfortTabledata = [...updatedData];
            // this.equipComfortGrid = process(this.EquipmentThermalComfortTabledata, this.EquipComfortState);
            // console.log("Updated from Completed", this.EquipmentThermalComfortTabledata);
            // ///
            let data = processedResponse.map(function (obj) {
              if (obj.CustomerRating && !isNaN(obj.CustomerRating)) {
                let rating = parseFloat(obj.CustomerRating);
                if (rating % 1 !== 0 && rating.toString().split('.')[1].length > 2) {
                  obj.CustomerRating = rating.toFixed(2);
                }
              }

              if (obj.AccountManagerRating && !isNaN(obj.AccountManagerRating)) {
                let rating = parseFloat(obj.AccountManagerRating);
                if (rating % 1 !== 0 && rating.toString().split('.')[1].length > 2) {
                  obj.AccountManagerRating = rating.toFixed(2);
                }
              }

              if (obj.WS_Response_String) {
                obj.WS_Response_String = obj.WS_Response_String.trim();
              }

              if (!obj.CompletionDate) {
                const extractedDate = extractDate(obj.IsServiceCallOpen ? '' : obj.Technician_Summary);
                if (extractedDate) {
                  obj.CompletionDate = extractedDate;
                }
              }

              function extractDate(summary: string): string {
                const datePattern = /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{4}\b/;
                const match = summary?.match(datePattern);
                if (match) {
                  const date = new Date(match[0]);
                  return formatDate(date); // Format date to "YYYY-MM-DDTHH:mm:ss"
                }
                return ""; // Return empty string if no date found
              }

              function formatDate(date: Date): string {
                const year = date.getFullYear();
                const month = padZero(date.getMonth() + 1); // Month is zero-indexed
                const day = padZero(date.getDate());
                const hours = padZero(date.getHours());
                const minutes = padZero(date.getMinutes());
                const seconds = padZero(date.getSeconds());
                return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
              }

              function padZero(num: number): string {
                return num < 10 ? '0' + num : num.toString();
              }

              return obj;
            });
            this.getIMDataDrivenTaskCompleteList = data;
            this.IMDataDrivenTCompleteListState.sort = [{ dir: "desc", field: "CompletionDate" }];
            this.gridDataForIMCompletedList = process(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState);
            if (this.getIMDataDrivenTaskCompleteList.length > 0 && this.getIMDataDrivenTaskCompleteList != null && this.getIMDataDrivenTaskCompleteList != undefined) {
              // const building: any = [];
              // const equipment = [];
              // const taskType = [];
              // const outcomes = []
              // this.getIMDataDrivenTaskCompleteList.forEach((elm) => {
              //   building.push({
              //     value: elm.Building,
              //     code: elm.Building
              //   })
              //   equipment.push({
              //     value: elm.Equipment,
              //     code: elm.Equipment
              //   })
              //   taskType.push({
              //     value: elm.Notes,
              //     code: elm.Notes
              //   })
              //   outcomes.push({
              //     value: elm.WS_Response_String,
              //     code: elm.WS_Response_String
              //   })
              //   const uniqueBuidling = building.filter((a, i) => building.findIndex((s) => a.value === s.value) === i);
              //   this.buildingFilterIMCompleteList = uniqueBuidling;
              //   const uniqueEquipment = equipment.filter((a, i) => equipment.findIndex((s) => a.value === s.value) === i);
              //   this.equipmentFilterIMCompleteList = uniqueEquipment
              //   const uniqueTaskType = taskType.filter((a, i) => taskType.findIndex((s) => a.value === s.value) === i);
              //   this.taskTypeFilterIMCompleteList = uniqueTaskType
              //   const uniqueOutcomes = outcomes.filter((a, i) => outcomes.findIndex((s) => a.value === s.value) === i);
              //   this.outcomeFilterIMCompleteList = uniqueOutcomes
              // })


              const start = Date.now();
              // this.getIMDataDrivenTaskCompleteList.forEach((elm) => {
              //   building.push({ value: elm.Building, code: elm.Building });
              //   equipment.push({ value: elm.Equipment, code: elm.Equipment });
              //   taskType.push({ value: elm.Notes, code: elm.Notes });
              //   outcomes.push({ value: elm.WS_Response_String, code: elm.WS_Response_String });
              // });
              // console.log('second', equipment);
              // const getUniqueByValue = (arr: any[]) =>
              //   arr.filter((item, index) => arr.findIndex(i => i.value === item.value) === index);

              // this.buildingFilterIMCompleteList = getUniqueByValue(building);
              // this.equipmentFilterIMCompleteList = getUniqueByValue(equipment);
              // this.taskTypeFilterIMCompleteList = getUniqueByValue(taskType);
              // this.outcomeFilterIMCompleteList = getUniqueByValue(outcomes);

              // map Style
              const uniqueMap = {
                building: new Map<string, any>(),
                equipment: new Map<string, any>(),
                taskType: new Map<string, any>(),
                outcomes: new Map<string, any>()
              };

              this.getIMDataDrivenTaskCompleteList.reduce((acc, elm) => {
                if (elm.Building && !uniqueMap.building.has(elm.Building)) {
                  uniqueMap.building.set(elm.Building, { value: elm.Building, code: elm.Building });
                }
                if (elm.Equipment && !uniqueMap.equipment.has(elm.Equipment)) {
                  uniqueMap.equipment.set(elm.Equipment, { value: elm.Equipment, code: elm.Equipment });
                }
                if (elm.Notes && !uniqueMap.taskType.has(elm.Notes)) {
                  uniqueMap.taskType.set(elm.Notes, { value: elm.Notes, code: elm.Notes });
                }
                if (elm.WS_Response_String && !uniqueMap.outcomes.has(elm.WS_Response_String)) {
                  uniqueMap.outcomes.set(elm.WS_Response_String, { value: elm.WS_Response_String, code: elm.WS_Response_String });
                }
                return acc;
              }, {});

              this.buildingFilterIMCompleteList = Array.from(uniqueMap.building.values());
              this.equipmentFilterIMCompleteList = Array.from(uniqueMap.equipment.values());
              // console.log('equipmentFilterIMCompleteList', this.equipmentFilterIMCompleteList);
              this.taskTypeFilterIMCompleteList = Array.from(uniqueMap.taskType.values());
              this.outcomeFilterIMCompleteList = Array.from(uniqueMap.outcomes.values());
               const end = Date.now();
               console.log(`getIMDataDrivenTCompletedList Duration: ${end - start} ms`);
            }


            let filterRecordsOnDexID = this.getHighestDEXIDRecords(this.getIMDataDrivenTaskCompleteList);

            if (filterRecordsOnDexID.length > 0 && filterRecordsOnDexID != null && filterRecordsOnDexID != undefined) {
              let serviceCallCompleteDataForResolved = filterRecordsOnDexID;
              let resolvedFilterData = serviceCallCompleteDataForResolved.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "resolved"
              })

              let ImprovedFilterData = serviceCallCompleteDataForResolved.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "improved"
              })


              if (ImprovedFilterData.length > 0 && ImprovedFilterData != undefined && ImprovedFilterData != null) {
                this.improvedOutcomeDataCount = ImprovedFilterData.length;
              } else {
                this.improvedOutcomeDataCount = 0;
              }


              let ResolvedPreIMTaskFilterData = serviceCallCompleteDataForResolved.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "resolved on prev im task"
              })

              if (resolvedFilterData.length > 0 && resolvedFilterData != undefined && resolvedFilterData != null) {
                this.resolvedOutcomeDataCount = resolvedFilterData.length + ResolvedPreIMTaskFilterData.length;
              } else {
                this.resolvedOutcomeDataCount = 0;
              }


              let serviceCallCompleteDataForQuoteRequired = filterRecordsOnDexID;
              let quoteRequiredFilterData = serviceCallCompleteDataForQuoteRequired.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "quote required"
              })
              if (quoteRequiredFilterData.length > 0 && quoteRequiredFilterData != undefined && quoteRequiredFilterData != null) {
                this.quoteRequiredOutcomeDataCount = quoteRequiredFilterData.length;
              } else {
                this.quoteRequiredOutcomeDataCount = 0;
              }



              let serviceCallCompleteDataForEscalated = filterRecordsOnDexID;
              let escalatedFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "further consideration req."
              })

              let NoAccessFilterData = serviceCallCompleteDataForResolved.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "no access"
              })


              let causedbyPlantFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "caused by plant config/design/use"
              })

              let causedbyEquipmentFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "caused by equipment config/design/use"
              })

              let causedbyInstallationFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "caused by installation or commissioning error"
              })
              let EQUIPMENT_DE_COMMISSIONED = serviceCallCompleteDataForEscalated.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "EQUIPMENT DE-COMMISSIONED"
              })

              if (escalatedFilterData.length > 0 && escalatedFilterData != undefined && escalatedFilterData != null) {
                this.escalatedOutcomeDataCount = escalatedFilterData.length + causedbyPlantFilterData.length + causedbyEquipmentFilterData.length + causedbyInstallationFilterData.length + EQUIPMENT_DE_COMMISSIONED.length;
              } else {
                this.escalatedOutcomeDataCount = 0 + causedbyPlantFilterData.length + causedbyEquipmentFilterData.length + causedbyInstallationFilterData.length + EQUIPMENT_DE_COMMISSIONED.length;
              }

              // this.ImTaskQtyDataCount = this.resolvedOutcomeDataCount + this.quoteRequiredOutcomeDataCount + this.escalatedOutcomeDataCount;


              // New Outcome Count No Access...
              let serviceCallCompleteDataForNoAccess = filterRecordsOnDexID;
              let NoAccessOutcomeData = serviceCallCompleteDataForNoAccess.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "no access"
              })
              if (NoAccessOutcomeData.length > 0 && NoAccessOutcomeData != undefined && NoAccessOutcomeData != null) {
                this.NoAccessOutcomeDataCount = NoAccessOutcomeData.length;
              } else {
                this.NoAccessOutcomeDataCount = 0;
              }

              // New Outcome Count Expired
              let serviceCallCompleteDataForExpired = filterRecordsOnDexID;
              let ExpiredOutcomeData = serviceCallCompleteDataForExpired.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "quote expired"
              })
              if (ExpiredOutcomeData.length > 0 && ExpiredOutcomeData != undefined && ExpiredOutcomeData != null) {
                this.ExpiredOutcomeDataCount = ExpiredOutcomeData.length;
              } else {
                this.ExpiredOutcomeDataCount = 0;
              }

              // New Outcome Count Lost
              let serviceCallCompleteDataForLost = filterRecordsOnDexID;
              let LostOutcomeData = serviceCallCompleteDataForLost.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "quote not accepted"
              })
              if (LostOutcomeData.length > 0 && LostOutcomeData != undefined && LostOutcomeData != null) {
                this.LostOutcomeDataCount = LostOutcomeData.length;
              } else {
                this.LostOutcomeDataCount = 0;
              }


              // New Outcome Count Won
              let serviceCallCompleteDataForWon = filterRecordsOnDexID;
              let WonOutcomeData = serviceCallCompleteDataForWon.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "quote accepted"
              })
              if (WonOutcomeData.length > 0 && WonOutcomeData != undefined && WonOutcomeData != null) {
                this.WonOutcomeDataCount = WonOutcomeData.length;
              } else {
                this.WonOutcomeDataCount = 0;
              }

              // New Outcome Count Awaiting Approval

              let serviceCallCompleteDataForAwaitingApproval = filterRecordsOnDexID;
              let AwaitingApprovalOutcomeData = serviceCallCompleteDataForAwaitingApproval.filter((res) => {
                return res.WS_Response_String.trim().toLowerCase() === "awaiting approval"
              })
              if (AwaitingApprovalOutcomeData.length > 0 && AwaitingApprovalOutcomeData != undefined && AwaitingApprovalOutcomeData != null) {
                this.AwaitingApprovalDataCount = AwaitingApprovalOutcomeData.length;
              } else {
                this.AwaitingApprovalDataCount = 0;
              }

              this.ImTaskQtyDataCount = this.resolvedOutcomeDataCount + this.improvedOutcomeDataCount + this.quoteRequiredOutcomeDataCount + this.escalatedOutcomeDataCount + this.NoAccessOutcomeDataCount + this.AwaitingApprovalDataCount + this.ExpiredOutcomeDataCount + this.WonOutcomeDataCount + this.LostOutcomeDataCount;

              let serviceCallCompleteDataForOpenTask = filterRecordsOnDexID;
              let openTaskFilterData = serviceCallCompleteDataForOpenTask.filter((res) => {
                return res.IsServiceCallOpen === 1;
              });

              if (openTaskFilterData.length > 0 && openTaskFilterData != undefined && openTaskFilterData != null) {
                this.FT2_Total = openTaskFilterData.length;
              } else {
                this.FT2_Total = 0;
              }
            }
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }
  countTasksByEquipment(table: any[], key: 'TasksCompleted' | 'BacklogTasks' | 'TuningTasks'): Record<string, EquipmentTasks> {
    return table.reduce((acc: Record<string, EquipmentTasks>, row) => {
      const Equipment = row.Equipment;
      if (!acc[Equipment]) {
        acc[Equipment] = { Equipment, TasksCompleted: null, BacklogTasks: null, TuningTasks: null };
      }
      acc[Equipment][key] += 1; // TypeScript is now explicitly informed about the key
      return acc;
    }, {});
  }

  getIMDataDrivenTTuningList(response) {
    this.getIMDataDrivenTaskTuningList = [];
    this.gridDataForIMTuningList = undefined;
    this.buildingFilterIMTuningList = [];
    this.equipmentFilterIMTuningList = [];
    this.taskTypeFilterIMTuningList = [];
    this.outcomeFilterIMTuningList = [];
    // this.coreDataService.getFtPPAData("Tuning", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        const processedResponse = response.response.map((item) => {
          return item;
        });
        localStorage.setItem('TuningList', JSON.stringify(processedResponse));
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            const count = this.countTasksByEquipment(processedResponse, "TuningTasks");
            this.tuningCounts = count;

            let data = processedResponse.map(function (obj) {
              if (obj.CustomerRating && !isNaN(obj.CustomerRating)) {
                let rating = parseFloat(obj.CustomerRating);
                if (rating % 1 !== 0 && rating.toString().split('.')[1].length > 2) {
                  obj.CustomerRating = rating.toFixed(2);
                }
              }

              if (obj.AccountManagerRating && !isNaN(obj.AccountManagerRating)) {
                let rating = parseFloat(obj.AccountManagerRating);
                if (rating % 1 !== 0 && rating.toString().split('.')[1].length > 2) {
                  obj.AccountManagerRating = rating.toFixed(2);
                }
              }

              if (obj.WS_Response_String) {
                obj.WS_Response_String = obj.WS_Response_String.trim();
              }

              obj.Technician_Date = extractDate(obj.Technician_Summary);
              function extractDate(summary: string): string {
                const datePattern = /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{4}\b/;
                const match = summary?.match(datePattern);
                if (match) {
                  const date = new Date(match[0]);
                  return formatDate(date);
                }
                return "";
              }

              function formatDate(date: Date): string {
                const year = date.getFullYear();
                const month = padZero(date.getMonth() + 1);
                const day = padZero(date.getDate());
                const hours = padZero(date.getHours());
                const minutes = padZero(date.getMinutes());
                const seconds = padZero(date.getSeconds());
                return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
              }

              function padZero(num: number): string {
                return num < 10 ? '0' + num : num.toString();
              }

              return obj;
            });
            this.getIMDataDrivenTaskTuningList = data;
            this.IMDataDrivenTTuningListState.sort = [{ dir: "desc", field: "Technician_Date" }];
            this.gridDataForIMTuningList = process(this.getIMDataDrivenTaskTuningList, this.IMDataDrivenTTuningListState);
            if (this.getIMDataDrivenTaskTuningList.length > 0 && this.getIMDataDrivenTaskTuningList != null && this.getIMDataDrivenTaskTuningList != undefined) {
              const building: any = [];
              const equipment = [];
              const taskType = [];
              const outcomes = []

              // this.getIMDataDrivenTaskTuningList.forEach((elm) => {
              //   building.push({
              //     value: elm.Building,
              //     code: elm.Building
              //   })
              //   equipment.push({
              //     value: elm.Equipment,
              //     code: elm.Equipment
              //   })
              //   taskType.push({
              //     value: elm.Notes,
              //     code: elm.Notes
              //   })
              //   outcomes.push({
              //     value: elm.WS_Response_String,
              //     code: elm.WS_Response_String
              //   })

              //   const uniqueBuidling = building.filter((a, i) => building.findIndex((s) => a.value === s.value) === i);
              //   this.buildingFilterIMTuningList = uniqueBuidling;
              //   const uniqueEquipment = equipment.filter((a, i) => equipment.findIndex((s) => a.value === s.value) === i);
              //   this.equipmentFilterIMTuningList = uniqueEquipment
              //   const uniqueTaskType = taskType.filter((a, i) => taskType.findIndex((s) => a.value === s.value) === i);
              //   this.taskTypeFilterIMTuningList = uniqueTaskType
              //   const uniqueOutcomes = outcomes.filter((a, i) => outcomes.findIndex((s) => a.value === s.value) === i);
              //   this.outcomeFilterIMTuningList = uniqueOutcomes

              // })
              const start = Date.now();
              const uniqueMap = {
                building: new Map<string, any>(),
                equipment: new Map<string, any>(),
                taskType: new Map<string, any>(),
                outcomes: new Map<string, any>()
              };

              this.getIMDataDrivenTaskTuningList.forEach(elm => {
                const { Building, Equipment, Notes, WS_Response_String } = elm;

                if (Building && !uniqueMap.building.has(Building)) {
                  uniqueMap.building.set(Building, { value: Building, code: Building });
                }

                if (Equipment && !uniqueMap.equipment.has(Equipment)) {
                  uniqueMap.equipment.set(Equipment, { value: Equipment, code: Equipment });
                }

                if (Notes && !uniqueMap.taskType.has(Notes)) {
                  uniqueMap.taskType.set(Notes, { value: Notes, code: Notes });
                }

                if (WS_Response_String && !uniqueMap.outcomes.has(WS_Response_String)) {
                  uniqueMap.outcomes.set(WS_Response_String, { value: WS_Response_String, code: WS_Response_String });
                }
              });

              this.buildingFilterIMTuningList = Array.from(uniqueMap.building.values());
              this.equipmentFilterIMTuningList = Array.from(uniqueMap.equipment.values());
              this.taskTypeFilterIMTuningList = Array.from(uniqueMap.taskType.values());
              this.outcomeFilterIMTuningList = Array.from(uniqueMap.outcomes.values());

              // console.log('equipmentFilterIMTuningList', this.equipmentFilterIMTuningList);
              const end = Date.now();
              console.log(`getIMDataDrivenTTuningList Duration: ${end - start} ms`);
            }
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }


  LoadEquipmentComfortTableData(res) {
    this.EquipmentThermalComfortdata = [];
    // this.coreDataService.GetEquipmentComfortData(this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe((res) => {
      // this.EquipmentThermalComfortdata = res.response
      let sortedData = res.response.sort((a, b) => {

        const totalA = (a.TooHotHours ?? 0) + (a.TooColdHours ?? 0);
        const totalB = (b.TooHotHours ?? 0) + (b.TooColdHours ?? 0);

        return totalB - totalA;
      });
      this.equipComfortGrid = process(sortedData, this.EquipComfortState);
      this.EquipmentThermalComfortdata = sortedData;
      // console.log(this.equipComfortGrid);
      this.createEquipmentThermalGraph(res.response)

    // })
  }
  // getting IM-Data Driven Task Awaiting Approval Table data
  getIMDataDrivenTAwaitingApplList(response) {
    this.getIMDataDrivenTAwaitingApprovalList = [];
    this.gridDataForIMAwaitingList = undefined
    this.buildingFilterIMAwaitingList = [];
    this.equipmentFilterIMAwaitingList = [];
    this.taskTypeFilterAwaitingList = [];
    this.getAwaitingListCount = [];
    // this.coreDataService.getFtPPAData("Awaiting Approval", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        localStorage.setItem('AwaitingList', JSON.stringify(response.response));
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {

            this.getIMDataDrivenTAwaitingApprovalList = response.response;
            this.getAwaitingListCount = this.getIMDataDrivenTAwaitingApprovalList;
            this.IMDataDrivenTAwaitingListState.sort = [{ dir: "desc", field: "DATE" }];
            this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);

            if (this.getIMDataDrivenTAwaitingApprovalList.length > 0 && this.getIMDataDrivenTAwaitingApprovalList != null && this.getIMDataDrivenTAwaitingApprovalList != undefined) {
              // const building: any = [];
              // const equipment = [];
              // const taskType = [];
              // this.getIMDataDrivenTAwaitingApprovalList.forEach((elm) => {
              //   building.push({
              //     value: elm.Building,
              //     code: elm.Building
              //   })
              //   equipment.push({
              //     value: elm.Equipment,
              //     code: elm.Equipment
              //   })
              //   taskType.push({
              //     value: elm.Notes,
              //     code: elm.Notes
              //   })

              //   const uniqueBuidling = building.filter((a, i) => building.findIndex((s) => a.value === s.value) === i);
              //   this.buildingFilterIMAwaitingList = uniqueBuidling;
              //   const uniqueEquipment = equipment.filter((a, i) => equipment.findIndex((s) => a.value === s.value) === i);
              //   this.equipmentFilterIMAwaitingList = uniqueEquipment
              //   const uniqueTaskType = taskType.filter((a, i) => taskType.findIndex((s) => a.value === s.value) === i);
              //   this.taskTypeFilterAwaitingList = uniqueTaskType
              // })
              const uniqueMap = {
                building: new Map<string, any>(),
                equipment: new Map<string, any>(),
                taskType: new Map<string, any>()
              };

              this.getIMDataDrivenTAwaitingApprovalList.forEach(elm => {
                const { Building, Equipment, Notes } = elm;

                if (Building && !uniqueMap.building.has(Building)) {
                  uniqueMap.building.set(Building, { value: Building, code: Building });
                }

                if (Equipment && !uniqueMap.equipment.has(Equipment)) {
                  uniqueMap.equipment.set(Equipment, { value: Equipment, code: Equipment });
                }

                if (Notes && !uniqueMap.taskType.has(Notes)) {
                  uniqueMap.taskType.set(Notes, { value: Notes, code: Notes });
                }
              });

              this.buildingFilterIMAwaitingList = Array.from(uniqueMap.building.values());
              this.equipmentFilterIMAwaitingList = Array.from(uniqueMap.equipment.values());
              this.taskTypeFilterAwaitingList = Array.from(uniqueMap.taskType.values());

            }
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }

  //Formatting the dates for site comfort data
  customDivisionsForThreeMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.ThreeMonthSiteDates, 10).map((d) => new Date(d).toLocaleDateString()));
        console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        day: 'numeric',
        month: '2-digit'
      })
      : '';
    };
  })();

  customDivisionsForSixMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.SixMonthSiteDates, 10).map(
          (d) => new Date(d).toLocaleDateString()));
        console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit'
      })
      : '';
    };
  })();

  customDivisionsForTwelveMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.TwelveMonthSiteDates, 10).map(
          (d) => new Date(d).toLocaleDateString()));
        // console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit'
      })
      : '';
    };
  })();


  generateSmartLabels(categories: string[], maxDivisions: number): string[] {
    if (!categories?.length || maxDivisions <= 0) return [];

    const sortedDates = categories
      .map((d) => new Date(d))
      .sort((a, b) => a.getTime() - b.getTime());

    const min = sortedDates[0].getTime();
    const max = sortedDates[sortedDates.length - 1].getTime();
    const interval = (max - min) / maxDivisions;

    const timeIntervals = [
      { label: '1 day', ms: 1 * 24 * 60 * 60 * 1000 },
      { label: '1 week', ms: 7 * 24 * 60 * 60 * 1000 },
      { label: '2 weeks', ms: 14 * 24 * 60 * 60 * 1000 },
      { label: '1 month', ms: 30 * 24 * 60 * 60 * 1000 },
      { label: '2 months', ms: 60 * 24 * 60 * 60 * 1000 },
      { label: '3 months', ms: 90 * 24 * 60 * 60 * 1000 },
      { label: '6 months', ms: 180 * 24 * 60 * 60 * 1000 },
      { label: '1 year', ms: 365 * 24 * 60 * 60 * 1000 },
    ];

    const chosenInterval =
      timeIntervals.find((t) => t.ms >= interval) ||
      timeIntervals[timeIntervals.length - 1];

    const labels: string[] = [];
    let currentDate = sortedDates[0];

    labels.push(currentDate.toISOString());

    while (true) {
      const label = chosenInterval.label.toLowerCase();

      if (label.includes('day') || label.includes('week')) {
        const incrementDays = parseInt(label.split(' ')[0]) * (label.includes('week') ? 7 : 1);
        currentDate = new Date(currentDate.getTime() + incrementDays * 24 * 60 * 60 * 1000);
      } else if (label.includes('month')) {
        const incrementMonths = parseInt(label.split(' ')[0]);
        currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + incrementMonths, 1);
      } else if (label.includes('year')) {
        const incrementYears = parseInt(label.split(' ')[0]);
        currentDate = new Date(currentDate.getFullYear() + incrementYears, currentDate.getMonth(), 1);
      }

      if (currentDate.getTime() > max) break;

      labels.push(currentDate.toISOString());
    }

    return labels;
  }

  formatDate(date: string): string {
    const parts = date.split('/');
    const [day, month, year] = parts.map((p) => p.padStart(2, '0'));
    return new Date(year + '-' + month + '-' + day).toISOString().split('T')[0];
  }

  checkGeneratedLabels(labels: string[]): string[] {
    const formattedLabels = labels.map((d) => this.formatDate(d));

    const matchingDates = formattedLabels.filter((label) =>
      this.categories.some(
        (cat) => new Date(cat).toDateString() === new Date(label).toDateString()
      )
    );

    const missingDates = formattedLabels.filter(
      (label) =>
        !this.categories.some(
          (cat) =>
            new Date(cat).toDateString() === new Date(label).toDateString()
        )
    );

    const filledDates = missingDates
      .map((missingDate) => {
        const target = new Date(missingDate);
        const availableDates = this.categories.filter((cat) => {
          const date = new Date(cat);
          return (
            date.getMonth() === target.getMonth() &&
            date.getFullYear() === target.getFullYear()
          );
        });

        return availableDates.sort(
          (a, b) => new Date(a).getTime() - new Date(b).getTime()
        )[0];
      })
      .filter(Boolean);

    const validLabels = [...matchingDates, ...filledDates];
    validLabels.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    return validLabels;
  }

  //getting three month for Site Comfort Data
  getThreeMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);
    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let lastMonth = monthNumber[monthNumber.length - 1];
    // let threeMonth = [];
    // for (let i = lastMonth - 3; i <= lastMonth; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == i
    //   })
    //   newArr.forEach(element => {
    //     threeMonth.push(element);
    //   });
    // }
    // this.ThreeMonthSiteData = threeMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let threeMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

    let lastThreeMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= threeMonthsAgo && itemDate <= currentDate;
    });
    this.ThreeMonthSiteData = lastThreeMonthsData;
    this.ThreeMonthSiteDates = this.ThreeMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getThreeMonthMovingAverage = [];

    let getLastThreeMonthData = lastThreeMonthsData;
    if (getLastThreeMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getThreeMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getThreeMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getThreeMonthMovingAverageData(data, 30);
        oneMonthData = oneMonthData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData, 30, 31);
        arr = value.slice(30);
        // for(let i = 0; i< 30; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getThreeMonthMovingAverage = arr;
    } else {
      this.getThreeMonthMovingAverage = [];
    }
  }

  // getting one month for Site Comfort Data
  getOneMonthSiteComfortData() {
    // let arr = [];
    // let data = this.getSiteComfortData;
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);
    // })
    // let unique: any = new Set(arr);
    // let monthNumber = Array.from(unique);
    // let oneMonth = data.filter((month) => {
    //   let generateDate = new Date(month.AllDates);
    //   return (generateDate.getMonth() + 1) == monthNumber[monthNumber.length - 1]
    // })
    // this.OneMonthSiteData = oneMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let oneMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    oneMonthsAgo.setMonth(currentDate.getMonth() - 1);

    let lastOneMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= oneMonthsAgo && itemDate <= currentDate;
    });
    this.OneMonthSiteData = lastOneMonthsData;

    this.getOneMonthMovingAverage = [];

    let getLastOneMonthData = lastOneMonthsData;
    if (getLastOneMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getOneMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }
      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getOneMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getOneMonthMovingAverage = arr;
    } else {
      this.getOneMonthMovingAverage = [];
    }
  }


  // getting Six month for Site Comfort Data
  getSixMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);

    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let lastMonth = monthNumber[monthNumber.length - 1];
    // let sixMonth = [];
    // for (let i = lastMonth - 6; i <= lastMonth; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == i;
    //   })
    //   newArr.forEach(element => {
    //     sixMonth.push(element);
    //   });
    // }
    // this.SixMonthSiteData = sixMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let SixMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    SixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    let lastSixMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= SixMonthsAgo && itemDate <= currentDate;
    });
    this.SixMonthSiteData = lastSixMonthsData;
    this.SixMonthSiteDates = this.SixMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getSixMonthMovingAverage = [];

    let getLastSixMonthData = lastSixMonthsData;
    if (getLastSixMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getSixMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getSixMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getSixMonthMovingAverageData(data, 30);
        oneMonthData = oneMonthData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData, 30, 31);
        arr = value.slice(30);
        // for(let i = 0; i< 30; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getSixMonthMovingAverage = arr;
    } else {
      this.getSixMonthMovingAverage = [];
    }
  }


  changeMonthTabSiteComfort(tab) {
    this.IsThreeDayMovingAverage = true;
    this.IsOneWeekMovingAverage = false
    this.IsOneMonthMovingAverage = false
    this.IsThreeMonthMovingAverage = false;
    if (tab == '1M') {
      this.IsOneMonthSiteComfort = true;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = false;
      this.getOneMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '3M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = true
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = false;
      this.getThreeMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '6M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = true
      this.IsTwelveMonthComfort = false;
      this.getSixMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '13M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = true;
      this.getTwelveMonthSiteComfortData();
      this.animateChart = true;
    }
  }

  public SiteComfortlabelContent = (e: AxisLabelVisualArgs): string => {
    return e.value + this.suffix;
  };

  public toggleSeries(): void {
    this.animateChart = !this.animateChart;
    if (!this.animateChart) {
      if (this.IsOneMonthSiteComfort) {
        this.OneMonthSiteData = [];
        this.getOneMonthMovingAverage = [];
      }
      if (this.IsThreeMonthSiteComfort) {
        this.ThreeMonthSiteData = [];
        this.getThreeMonthMovingAverage = [];
      }
      if (this.IsSixMonthSiteComfort) {
        this.SixMonthSiteData = [];
        this.getSixMonthMovingAverage = [];
      }
      if (this.IsTwelveMonthComfort) {
        this.TwelveMonthSiteData = [];
        this.getTwelveMonthMovingAverage = [];
      }
    }
    else {
      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }
      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
  }
  public categoryAxis = {
    max: new Date(2022, 1, 0),
    maxDivisions: 10
  };

  categories = []
  // getting Site Comfort data
  getAllSiteComfortData(response) {
    // this.coreDataService.getSiteComfortData(this.locations).subscribe(
    //   (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          let data = response.response.SiteComfortData.map(function (obj) {
            let date;
            if (obj.AllDates != undefined && obj.AllDates != null)
              date = new Date(obj.AllDates);
            // obj.AllDates = (date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()).toString();
            obj.AllDates = date;
            return obj;
          });
          let filteredData = data.filter(
            (item) => (item.TotalPctComfort ?? 0) !== 0
          );
          this.getSiteComfortData = filteredData;
          this.categories = this.getSiteComfortData.map(item => item.AllDates);
          this.getOneMonthSiteComfortData();
          this.changeMonthTabSiteComfort('1M')
          this.getTwelveMonthChangePercentage = 0
          this.getTwelveMonthChangePercentage = response.response.PercentageChange;
          this.movingAverageCount = response.response.MonthsDiff;
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }


  // getting HVAC Score
  getHVACAssetsPerformanceData() {
    return this.config = {
      type: "gauge",
      globals: {
        fontSize: 14,
        placement: 'inner',
        color: 'lightorange',
        fontWeight: 600,
      },
      plotarea: {
        marginTop: 50,
      },
      plot: {
        size: '100%',
        tooltip: {
          text: "<div><h4>This is dummy text</h4></div>",
          'html-mode': true,
          'font-color': "white",
          'font-family': "calibri light , 'Roboto'",
          'font-size': 22,
          'font-weight': "bold",
          'font-style': "normal",
          placement: "node:bottom",
          padding: "15%",
          "margin-top": "20%",
          "offset-x": 0,
          "offset-y": 30
        }
      },
      tooltip: {
        borderRadius: 2
      },
      'scale-r-2': {
        tick: {  //Tick Markers
          'line-color': "white",
          'line-style': "solid", //solid, dashed, or dotted
          'line-width': 3,
          size: 50,
          placement: "inner",//outer, inner, or cross
        },
        aperture: 185,
        minValue: 10,
        maxValue: 100,
        center: {
          visible: false,
        },
        // tick: {
        //   visible: false
        // },
        item: {
          offsetR: -70,
          rules: [
            {
              rule: '%i == 1',
              offsetX: 20,
              'font-color': "#3b494f",
            },
            {
              rule: '%i == 4',
              'font-color': "#3b494f",
            },
            {
              rule: '%i == 7',
              'font-color': "#3b494f",
            },
            {
              rule: '%i == 9',
              offsetX: 5,
              offsetY: -20,
              'font-color': "#3b494f",
            }
          ]
        },
        // step: 10,
        labels: ['', 'Poor', '', '', 'Average', '', '', 'Good', '', 'Excellent'],
        ring: {
          size: 50,
          rules: [
            {
              rule: '%v <= 10 && %v >= 0',
              backgroundColor: '#f2f2f2'
            },
            {
              rule: '%v <= 20 && %v >= 10',
              backgroundColor: '#f2f2f2'
            },
            {
              rule: '%v <= 30 && %v >= 20',
              backgroundColor: '#f2f2f2'
            },
            {
              rule: '%v <= 40 && %v >= 30',
              backgroundColor: '#d9d9d9'
            },
            {
              rule: '%v <= 50 && %v >= 40',
              backgroundColor: '#d9d9d9'
            }
            ,
            {
              rule: '%v <= 60 && %v >= 50',
              backgroundColor: '#bfbfbf'
            },
            {
              rule: '%v <= 70 && %v >= 60',
              backgroundColor: '#a6a6a6'
            },
            {
              rule: '%v <= 80 && %v >= 70',
              backgroundColor: '#7f7f7f'
            },
            {
              rule: '%v <= 90 && %v >= 80',
              backgroundColor: '#404040'
            },
            {
              rule: '%v <= 100 && %v >= 90',
              backgroundColor: '#0d0d0d'
            }
          ]
        },
      },
      scaleR: {
        aperture: 184,
        // minValue: 0,
        // maxValue: 100,
        // step: 10,
        values: '0:100',
        "items-overlap": true,
        "max-labels": 12,
        "max-ticks": 5,
        center: {
          visible: false,
        },
        tick: {
          visible: false
        },
        item: {
          offsetR: 0,
          rules: [
            {
              rule: '%i == 100',
              'font-color': "#808080",
            },
            {
              rule: '%i == 60',
              'font-color': "white",
            },
            {
              rule: '%i == 80',
              'font-color': "white",
              // offsetY: 8
            },
            {
              rule: '%i == 90',
              'font-color': "white",
            },
            {
              rule: '%i == 70',
              'font-color': "white",
            },
          ],
          offsetX: 0,
        },
        // labels: ['0','10', '20', '30', '40', '50', '60', '70', '80', '90', '100'],
        ring: {
          size: 120,
          rules: [
            {
              rule: '%v <= 10 && %v >= 0',
              backgroundColor: '#cdf2ff',
            },
            {
              rule: '%v <= 20 && %v >= 10',
              backgroundColor: '#cdf2ff'
            },
            {
              rule: '%v <= 30 && %v >= 20',
              backgroundColor: '#cdf2ff'
            },
            {
              rule: '%v <= 40 && %v >= 30',
              backgroundColor: '#9fe6ff'
            },
            {
              rule: '%v <= 50 && %v >= 40',
              backgroundColor: '#9fe6ff'
            }
            ,
            {
              rule: '%v <= 60 && %v >= 50',
              backgroundColor: '#9fe6ff'
            },
            {
              rule: '%v <= 70 && %v >= 60',
              backgroundColor: '#9fe6ff'
            },
            {
              rule: '%v <= 80 && %v >= 70',
              backgroundColor: '#47cfff'
            },
            {
              rule: '%v <= 90 && %v >= 80',
              backgroundColor: '#47cfff'
            },
            {
              rule: '%v <= 100 && %v >= 90',
              backgroundColor: '#00b0f0'
            }
          ]
        },
      },

      gui: {
        behaviors: [
          {
            id: 'DownloadPDF',
            enabled: 'none'
          },
          {
            id: 'CrosshairHide',
            enabled: 'none'
          },
          {
            id: 'ViewSource',
            enabled: 'none'
          },
          {
            id: 'Reload',
            enabled: 'none'
          },
          {
            id: 'DownloadCSV',
            enabled: 'none'
          },
          {
            id: 'DownloadSVG',
            enabled: 'none'
          },
          {
            id: 'DownloadXLS',
            enabled: 'none'
          },
          {
            id: 'Print',
            enabled: 'none'
          },
          {
            id: 'SaveAsImagePNG',
            enabled: 'none'
          },
          {
            id: 'ShowAll',
            enabled: 'none'
          }
        ]
      },
      series: [
        {
          values: [this.HVACAssetPerformanceScore],
          backgroundColor: 'black',
          csize: "5%",
          size: "100%",
          animation: {
            effect: 2,
            method: 1,
            sequence: 4,
            speed: 900
          },
        }
      ]
    };
  }


  async locationChange(event) {
    if (event != undefined) {
      this.locationDateFilterData = JSON.parse(event);
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations.split(",").map(el => parseInt(el));
      }
      else {
        this.locations = [];
      }
      this.range.start = this.locationDateFilterData.start;
      this.range.end = this.locationDateFilterData.end;
      this.clearWidgets()
      await this.loadAllAPIs();
      this.headerComponent.clearEquipment();
      localStorage.setItem("IsIMDataUpdated", JSON.stringify(true));
      this.allSelectedEquipments = [];
      this.getCompleteList = [];
      this.getAwaitingListCount = [];
      this.getTaskTpeOnEquipments = [];
    }
  }
  public labelContentOfOneMonth = (e: any) => {
    let days = ["S", "M", "T", "W", "T", "F", "S"]
    let month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    if (e.value.getDate() == 5 || e.value.getDate() == 10 || e.value.getDate() == 15 || e.value.getDate() == 20 || e.value.getDate() == 25 || e.value.getDate() == 30) {
      return `${days[e.value.getDay()]} \n ${month[(e.value.getMonth())]} ${e.value.getDate()}`;
    } else {
      return days[e.value.getDay()];
    }
  };
  public seriesLabels: SeriesLabels = {
    visible: true, // Note that visible defaults to false
    padding: 0,
    font: 'bold 24px Calibri Light !important',
    background: 'none',
    margin: {
      right: 12
    },
    align: 'center',
    color: 'white',
    content: (args) => {
      if (args.value === 0) {
        return '';
      }
      return args.value;
    },
  };

  public seriesLabelsForBarGraph: SeriesLabels = {
    visible: true,
    position: 'center',
    color: 'white',
    background: 'none',
    font: '17px Roboto !important',
    content: (args) => {
      if (args.value === 0) {
        return '';
      }
      return args.value;
    },
  };
  public colors = [
    {
      from: 0,
      to: 25,
      color: "rgb(0, 17, 50)",
    },
    {
      from: 25,
      to: 50,
      color: "rgb(0, 17, 50)",
    },
    {
      from: 50,
      to: 75,
      color: "rgb(0, 17, 50)",
    },
    {
      from: 75,
      to: 100,
      color: "rgb(0, 17, 50)",
    },
  ];

  // getting Status for HVAC Score
  getHVACStatus() {
    if (this.HVACAssetPerformanceScore) {
      if (this.HVACAssetPerformanceScore >= 0 && this.HVACAssetPerformanceScore <= 30) {
        this.HVACStatus = "Poor"
      }
      if (this.HVACAssetPerformanceScore >= 30 && this.HVACAssetPerformanceScore <= 60) {
        this.HVACStatus = "Average"
      }
      if (this.HVACAssetPerformanceScore >= 60 && this.HVACAssetPerformanceScore <= 80) {
        this.HVACStatus = "Good"
      }
      if (this.HVACAssetPerformanceScore >= 80 && this.HVACAssetPerformanceScore <= 100) {
        this.HVACStatus = "Excellent"
      }
    }
  }

  // getting categories for Service Call Graph
  getServiceCallCategory() {
    const monthCountArr = new Array(12).fill(0);
    this.ServiceCallCategory = [];
    monthCountArr[new Date().getMonth()] += 1;
    let month = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    let arr = [];
    let index = 0;
    for (let i = 0; i <= monthCountArr.length; i++) {
      if (monthCountArr[i] != undefined) {
        if (monthCountArr[i] == 1) {
          index = monthCountArr.indexOf(monthCountArr[i]);
        }
      }
    }
    function removeAllBefore(array, index) {
      var position = index;
      if (position === -1)
        return array;
      return array.slice(position);
    }

    let seperateData = removeAllBefore(month, index);
    for (let j = 0; j <= seperateData.length; j++) {
      if (seperateData[j] != undefined) {
        arr.push(seperateData[j]);
      }
    }
    for (let i = 0; i < index; i++) {
      arr.push(month[i]);
    }
    this.ServiceCallCategory = arr;
  }

  ShuffleCurrentServiceCallData(data) {
    const monthCountArr = new Array(12).fill(0);
    monthCountArr[new Date().getMonth()] += 1;
    let index = 0;
    let arr = [];
    for (let i = 0; i <= monthCountArr.length; i++) {
      if (monthCountArr[i] != undefined) {
        if (monthCountArr[i] == 1) {
          index = monthCountArr.indexOf(monthCountArr[i]);
        }
      }
    }
    function removeAllBefore(array, index) {
      var position = index;
      if (position === -1)
        return array;
      return array.slice(position);
    }

    let seperateData = removeAllBefore(data[0].data, index);
    for (let j = 0; j <= seperateData.length; j++) {
      if (seperateData[j] != undefined) {
        arr.push(seperateData[j]);
      }
    }
    for (let i = 0; i < index; i++) {
      arr.push(data[0].data[i]);
    }
    return arr;
  }

  ShuffleHistoricalData(data) {
    const monthCountArr = new Array(12).fill(0);
    monthCountArr[new Date().getMonth()] += 1;
    let index = 0;
    let arr = [];
    for (let i = 0; i <= monthCountArr.length; i++) {
      if (monthCountArr[i] != undefined) {
        if (monthCountArr[i] == 1) {
          index = monthCountArr.indexOf(monthCountArr[i]);
        }
      }
    }
    function removeAllBefore(array, index) {
      var position = index;
      if (position === -1)
        return array;
      return array.slice(position);
    }

    let seperateData = removeAllBefore(data[1].data, index);
    for (let j = 0; j <= seperateData.length; j++) {
      if (seperateData[j] != undefined) {
        arr.push(seperateData[j]);
      }
    }
    for (let i = 0; i < index; i++) {
      arr.push(data[1].data[i]);
    }
    return arr;
  }

  getColorChange(color) {
    let objIndex = color.findIndex((obj) => obj.from === this.target);
    color[objIndex].color = 'rgba(75,208,255)';
    this.HVACColors = color;
  }
  getGaugeLabels(args: SeriesLabelsVisualArgs) {
    console.log(args);
    if (args.value === 0 || args.value === 100) {
      return args.value;
    }
    return '';
  }

  public labels: RadialLabels = {
    content: (args) => {
      if (
        args.value === this.target
      ) {
        return args.value;
      }
      return '';
    },
    position: 'outside'
  };

  // public dataStateChangeCompleted(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTCompleteListState = state;
  //   this.gridDataForIMCompletedList = process(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState);
  // }

  // public dataStateChangeCompleted(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTCompleteListState = state;
  //   console.log(this.IMDataDrivenTCompleteListState);
  //   const filters = state.filter.filters;

  //   if(filters.length > 0){
  //     let filteredData = this.getIMDataDrivenTaskCompleteList.slice(0);

  //     // Function to apply filters
  //     const applyFilter = (item: any, filter: FilterDescriptor) => {
  //       const filterValue = filter.value.toString().toLowerCase();
  //       if (filter.field === "EnergyWasteCostAvoidanceKWH") {
  //           // return item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase().includes(filterValue) ||
  //           //        item.EnergyWasteCostAvoidance$.toString().toLowerCase().includes(filterValue);

  //           // const kwhValue = item.EnergyWasteCostAvoidanceKWH ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase() : '';
  //           // const dollarValue = item.EnergyWasteCostAvoidance$ ? item.EnergyWasteCostAvoidance$.toString().toLowerCase() : '';
  //           // return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);

  //           const kwhValue = item.EnergyWasteCostAvoidanceKWH !== null && item.EnergyWasteCostAvoidanceKWH !== undefined ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase() : '';
  //           const dollarValue = item.EnergyWasteCostAvoidance$ !== null && item.EnergyWasteCostAvoidance$ !== undefined ? item.EnergyWasteCostAvoidance$.toString().toLowerCase() : '';
  //           return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
  //       } else if (typeof filter.field === 'string') {
  //         // const fieldValue = item[filter.field] ? item[filter.field].toString().toLowerCase() : '';

  //         const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined ? item[filter.field].toString().toLowerCase() : '';

  //           // return item[filter.field].toString().toLowerCase().includes(filterValue);
  //         return fieldValue.includes(filterValue);
  //       }
  //       return true;
  //      };

  //       // Apply all filters
  //       filters.forEach(filterGroup => {
  //         if ('filters' in filterGroup) { // Check if it's a CompositeFilterDescriptor
  //             filteredData = filteredData.filter(item =>
  //                 (filterGroup as CompositeFilterDescriptor).filters.every(subFilter => applyFilter(item, subFilter as FilterDescriptor))
  //             );
  //         } else {
  //             filteredData = filteredData.filter(item => applyFilter(item, filterGroup as FilterDescriptor));
  //         }
  //      });

  //     // Process the filtered data for the grid
  //     this.gridDataForIMCompletedList = process(filteredData, {});
  //   }else{
  //     // No filters applied, so use the original data
  //     this.gridDataForIMCompletedList = process(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState);
  //   }
  // }

  // public dataStateChangeAwaiting(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTAwaitingListState = state;
  //   this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);
  // }

  // public dataStateChangeAwaiting(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTAwaitingListState = state;
  //   const filters = state.filter.filters;

  //   if(filters.length > 0){
  //     let filteredData = this.getIMDataDrivenTAwaitingApprovalList.slice(0);

  //     // Function to apply filters
  //     const applyFilter = (item: any, filter: FilterDescriptor) => {
  //       const filterValue = filter.value.toString().toLowerCase();
  //       if (filter.field === "EnergyWasteCostAvoidanceKWH") {
  //           // const kwhValue = item.EnergyWasteCostAvoidanceKWH ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase() : '';
  //           // const dollarValue = item.EnergyWasteCostAvoidance$ ? item.EnergyWasteCostAvoidance$.toString().toLowerCase() : '';
  //           // return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);

  //           const kwhValue = item.EnergyWasteCostAvoidanceKWH !== null && item.EnergyWasteCostAvoidanceKWH !== undefined ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase() : '';
  //           const dollarValue = item.EnergyWasteCostAvoidance$ !== null && item.EnergyWasteCostAvoidance$ !== undefined ? item.EnergyWasteCostAvoidance$.toString().toLowerCase() : '';
  //           return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
  //       } else if (typeof filter.field === 'string') {
  //         // const fieldValue = item[filter.field] ? item[filter.field].toString().toLowerCase() : '';
  //         // return fieldValue.includes(filterValue);

  //         const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined ? item[filter.field].toString().toLowerCase() : '';
  //         return fieldValue.includes(filterValue);
  //       }
  //       return true;
  //      };

  //      // Apply all filters
  //      filters.forEach(filterGroup => {
  //       if ('filters' in filterGroup) { // Check if it's a CompositeFilterDescriptor
  //           filteredData = filteredData.filter(item =>
  //               (filterGroup as CompositeFilterDescriptor).filters.every(subFilter => applyFilter(item, subFilter as FilterDescriptor))
  //           );
  //       } else {
  //           filteredData = filteredData.filter(item => applyFilter(item, filterGroup as FilterDescriptor));
  //       }
  //    });
  //    // Process the filtered data for the grid
  //    this.gridDataForIMAwaitingList = process(filteredData, {});
  //   }else{
  //     this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);
  //   }
  // }

  DownloadSummaryReport(data) {
    if (data.ServiceCallID != null && data.ServiceCallID != undefined && data.ServiceCallID != "") {
      let payload = {
        DocumentType: "Call Summary Report",
        PropertyType: "Service Job Number",
        PropertyValue: data.ServiceCallID
      };
      this.spinner.show();
      this.coreDataService.getDocLinkByID(payload).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              let result = res['_body'];
              let fileType = result.type.split('/')[1];
              let ReportName = 'ServiceReport-' + data.ServiceCallID + "." + fileType;
              this.shareData.showSuccess("Tech Summary Report downloaded successfully");
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
              saveAs(blob, ReportName);
            }
            else if (res.status === 204) {
              this.shareData.showWarning("No Tech Summary Report found for Service Call : " + data.ServiceCallID);
            }
            this.spinner.hide();

          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    } else {
      this.shareData.showWarning("Service Call ID not found");
    }
  }

  isRowSelected(event: any): void {
    if (event != undefined) {
      this.SelectedRowData = event;
    }
  }

  getDetail() {
    if (this.SelectedRowData != undefined) {
      this.coreDataService.getQuoteDetail(this.SelectedRowData.selectedRows[0].dataItem.QuoteID).subscribe(response => {
        response.QuoteID = this.SelectedRowData.selectedRows[0].dataItem.QuoteID
        this.shareData.quoteDetail.next(response);
      },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        });
      this.router.navigate(["/QuoteDetail"]);
    }
  }

  public labelContentOfIMTaskType = (e: any) => {
    return e.value;
  };


  // getting IM Data Driven Task Type Data
  getIMDataDrivenTaskTypeData(response) {
    this.IMDataDrivenTaskTypeList = [];
    this.allTaskTypeData = [];
    // this.coreDataService.getIMDataDrivenTaskTypeData("Complete", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            let data = response.response
            this.allTaskTypeData = data;
            const grouped = data.reduce((acc, curr) => {
              const { AnalyticRule, AnalyticRuleCount } = curr;
              if (!acc[AnalyticRule]) {
                acc[AnalyticRule] = 0;
              }
              acc[AnalyticRule] += AnalyticRuleCount;
              return acc;
            }, {} as { [key: string]: number });

            this.IMDataDrivenTaskTypeList = Object.entries(grouped);
            if (this.IMDataDrivenTaskTypeList.length > 0) {
              this.createTaskTypeChart(this.IMDataDrivenTaskTypeList);
            }

          } else {
            let data = response.response;
            this.allTaskTypeData = data;

            const grouped = data.reduce((acc, curr) => {
              const { AnalyticRule, AnalyticRuleCount } = curr;
              if (!acc[AnalyticRule]) {
                acc[AnalyticRule] = 0;
              }
              acc[AnalyticRule] += AnalyticRuleCount;
              return acc;
            }, {} as { [key: string]: number });

            this.IMDataDrivenTaskTypeList = Object.entries(grouped);
            this.createTaskTypeChart(this.IMDataDrivenTaskTypeList);
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }

  public get chartHeight(): number {
    return this.IMDataDrivenTaskTypeList.length * 100;
  }

  CreatMajorUnitForServiceCall(serviceCallData) {
    this.serviceCallMarginUnit = 1;
    let MaxCount = [];

    for (let i = 0; i <= serviceCallData.length - 1; i++) {
      let arr = serviceCallData[i].data;
      MaxCount.push(Math.max(...arr))
    }
    let count = Math.max(...MaxCount)
    if (count == 0) {
      this.serviceCallMarginUnit = 1;
    } else if (count <= 30) {
      this.serviceCallMarginUnit = 2;
    } else if (count >= 30 || count <= 100) {
      this.serviceCallMarginUnit = 5;
    } else {
      this.serviceCallMarginUnit = 10;
    }
  }
  selectedBarIndex = null;
  createTaskTypeChart(data) {
    const sortedData = data.sort((a, b) => b[1] - a[1]);
    const chart = Highcharts.chart('container', {
      chart: {
        type: 'bar',
        marginLeft: 150,
        // plotBackgroundColor: '#F8F8F8',
        scrollablePlotArea: {
          minHeight: sortedData.length * 50,
          opacity: 1
        },
      },

      title: {
        text: ''
      },
      subtitle: {

      },
      xAxis: {
        type: 'category',
        title: {
          text: ''
        },
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Roboto !important',
            color: '#000000',
            fontSize: '0.6vw !important',
            textWrap: 'wrap !important'
          }
        },
        tickLength: 0
      },
      yAxis: {
        title: false,
        allowDecimals: false,
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Calibri Light !important',
            color: '#000000',
            fontSize: '0.7vw !important',
            textWrap: 'wrap !important'
          }
        },
        plotBands: [{
          from: 0,
          to: sortedData.length + 100,
          color: '#F8F8F8'
        }],
        gridLineWidth: 0
      },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: false
          }
        }
      },
      legend: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      series: [{
        name: '',
        data: sortedData,
        color: 'rgb(75, 208, 255)',
        groupPadding: 0.1,
        pointPadding: 0
      }]
    } as any);

  }

  createEquipmentThermalGraph(data: any[]) {
    let sortedData = data.sort((a, b) => {
      // Calculate the total hours for each item
      const totalA = (a.TooHotHours ?? 0) + (a.TooColdHours ?? 0);
      const totalB = (b.TooHotHours ?? 0) + (b.TooColdHours ?? 0);

      // Sort in descending order
      return totalB - totalA;
    });
    this.hotSeries = sortedData.map(item => (item.TooHotHours > 0 ? item.TooHotHours : null));
    this.coldSeries = sortedData.map(item => (item.TooColdHours > 0 ? item.TooColdHours : null));
    this.equipcategories = sortedData.map(item => item.Equipment);

    // console.log(Math.max(...this.coldSeries.concat(this.hotSeries)) + 100);
    const chart2 = Highcharts.chart('EquipContainer', {
      chart: {
        type: 'bar',
        height: 410,
        // plotBackgroundColor: '#F8F8F8',
        scrollablePlotArea: {
          minHeight: sortedData.length * 50,
          opacity: 1
        },
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false // Disable the Highcharts watermark
      },
      xAxis: {
        categories: this.equipcategories,
        title: {
          text: null
        },
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Roboto !important',
            color: '#000000',
            fontSize: '0.7vw !important',
            textWrap: 'wrap !important'
          }
        },
        min: -0.5, // Add padding to the left
        max: this.equipcategories.length - 0.5,
      },
      yAxis: {
        min: 0,
        title: {
          text: ''
        },
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Calibri Light !important',
            color: '#000000',
            fontSize: '0.7vw !important',
            textWrap: 'wrap !important'
          }
        },
        stackLabels: {
          enabled: false,
          style: {
            fontWeight: 'bold',
            color: ( // theme
              Highcharts.defaultOptions.title.style &&
              Highcharts.defaultOptions.title.style.color
            ) || 'gray'
          }
        },
        plotBands: [{
          from: 0,
          to: Math.max(...this.coldSeries.concat(this.hotSeries)) + 1000,
          color: '#F8F8F8'
        }],
        gridLineWidth: 0
      },
      tooltip: {
        headerFormat: '<b>{point.x}</b><br/>',
        pointFormat: '{series.name}: {point.y} hours'
      },
      plotOptions: {
        series: {
          stacking: 'normal',
          point: {
            // events: {
            //   click: (event) => {
            //     const category = event.point.category; // Use event.point to get the category
            //     // Check if the clicked bar is already selected
            //     if (this.selectedBarIndex === event.point.index && this.selectedBarIndex != undefined && this.selectedBarIndex != null) {
            //       // Reset colors to original for both series
            //       event.point.series.chart.series.forEach(series => {
            //         series.data.forEach(point => {
            //           point.update({
            //             // color: point.series.color // Reset to original color
            //             borderColor: null, // Reset to no border
            //             borderWidth: 0 // Reset border width
            //           });
            //         });
            //       });

            //       this.selectedBarIndex = null; // Deselect the bar
            //       this.EquipmentComfortSelected("Deselected"); // Pass the category to the function
            //     } else {
            //       // Reset previous selected bar colors
            //       if (this.selectedBarIndex !== null) {
            //         event.point.series.chart.series.forEach(series => {
            //           series.data.forEach(point => {
            //             point.update({
            //               borderColor: null, // Reset to no border
            //               borderWidth: 0 // Reset border width
            //             });
            //           });
            //         });

            //       }
            //       // Highlight both bars for the selected category
            //       this.selectedBarIndex = event.point.index;
            //       event.point.series.chart.series.forEach(series => {
            //         series.data[this.selectedBarIndex].update({
            //           borderColor: 'black', // Set the border color
            //           borderWidth: 3 // Set the border width
            //         });
            //       });

            //       this.EquipmentComfortSelected(category); // Pass the category to the function
            //     }
            //   }
            //   // (event) => this.EquipmentComfortSelected(event.point.category)

            // },
          },
          dataLabels: {
            enabled: true,
            formatter: function () {
              // return this.series.name; // Show "Hot" or "Cold"
            },
            style: {
              color: 'white', // Change text color to black for better visibility
              fontWeight: 'bold', // Make the text bold for clarity
              textOutline: 'none' // Remove any outline for clearer text
            }
          },
          pointPlacement: 'on',
        }
      },
      legend: {
        enabled: true,
        align: 'left',
        itemStyle: {
          color: '#888888',
          font: '13px Calibri Light !important'
        },
      },
      series: [{
        name: 'Too cold',
        data: this.coldSeries,
        color: '#3DCDFF',
        pointWidth: 30, // Adjust the width of the bars if needed
        borderColor: null, // Ensure no border by default
        borderWidth: 0 // Ensure no border by defaultm
      }, {
        name: 'Too hot',
        data: this.hotSeries,
        color: '#001132',
        pointWidth: 30, // Adjust the width of the bars if needed
        borderColor: null, // Ensure no border by default
        borderWidth: 0 // Ensure no border by default
      }]
    } as any);
  }

  EquipmentComfortSelected(Equip?: string) {

    if (!this.EquipmentFiltered) {
      let completeData = JSON.parse(localStorage.getItem("CompleteList"))
      let awaitingData = JSON.parse(localStorage.getItem("AwaitingList"))
      let backlogData = JSON.parse(localStorage.getItem("BacklogList"))

      if (completeData != null && completeData != undefined && awaitingData != null && awaitingData != undefined && backlogData != null && backlogData != undefined) {
        let getEquipments = this.findCommonAndUniqueEquipments(completeData, awaitingData, backlogData);
        getEquipments.sort((a, b) => a.name.localeCompare(b.name));
        this.equipment = [...getEquipments];
      }
      this.confirmSelection(Equip)
    } else {
      this.confirmSelection(Equip, true)
    }

  }

  getUrl(data) {
    window.open(data.SkySparkURL);
  }

  // getting twelve month Data for Site Comfort
  getTwelveMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);

    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let twelveMonth = [];
    // for (let i = 0; i < monthNumber.length; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == monthNumber[i]
    //   })
    //   newArr.forEach(element => {
    //     twelveMonth.push(element);
    //   });
    // }
    // this.TwelveMonthSiteData = twelveMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let TwelveMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    TwelveMonthsAgo.setMonth(currentDate.getMonth() - 13);

    let lastTwelveMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= TwelveMonthsAgo && itemDate <= currentDate;
    });
    this.TwelveMonthSiteData = lastTwelveMonthsData;
    this.TwelveMonthSiteDates = this.TwelveMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getTwelveMonthMovingAverage = [];

    let getLastTwelveMonthData = lastTwelveMonthsData;
    if (getLastTwelveMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getTwelveMonthMovingAverageData(data, 2);
        threeDayData.movingdata = threeDayData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData.movingdata, 2, 3);
        arr = value;
        if (threeDayData.recordsExist) {
          arr = arr.slice(2);
        } else {
          // let totalrecordsExist = 2 - threeDayData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }
          if (threeDayData.existRecordCount > 0) {
            arr = arr.slice(threeDayData.existRecordCount);
            let difference = 2 - threeDayData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 2; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getTwelveMonthMovingAverageData(data, 6);
        oneWeekData.movingdata = oneWeekData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData.movingdata, 6, 7);
        arr = value;
        if (oneWeekData.recordsExist) {
          arr = arr.slice(6);
        } else {
          // let totalrecordsExist = 6 - oneWeekData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (oneWeekData.existRecordCount > 0) {
            arr = arr.slice(oneWeekData.existRecordCount);
            let difference = 6 - oneWeekData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 6; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getTwelveMonthMovingAverageData(data, 30);
        oneMonthData.movingdata = oneMonthData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData.movingdata, 30, 31);
        arr = value;
        if (oneMonthData.recordsExist) {
          arr = arr.slice(30);
        } else {
          // let totalrecordsExist = 30 - oneMonthData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (oneMonthData.existRecordCount > 0) {
            arr = arr.slice(oneMonthData.existRecordCount);
            let difference = 30 - oneMonthData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 30; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }

        }
      }

      if (this.IsThreeMonthMovingAverage) {
        let threeMonthData = this.getTwelveMonthMovingAverageData(data, 89);
        threeMonthData.movingdata = threeMonthData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeMonthData.movingdata, 89, 90);
        arr = value;
        if (threeMonthData.recordsExist) {
          arr = arr.slice(89);
        } else {
          // let totalrecordsExist = 89 - threeMonthData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (threeMonthData.existRecordCount > 0) {
            arr = arr.slice(threeMonthData.existRecordCount);
            let difference = 89 - threeMonthData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 89; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      this.getTwelveMonthMovingAverage = arr;
    } else {
      this.getTwelveMonthMovingAverage = [];
    }
  }


  getIMDataDrivenTaskBacklogData(response) {
    this.IMDataDrivenTaskBacklogList = [];
    this.allBacklogTaskTypeData = []
    // this.coreDataService.getIMDataDrivenTaskTypeData("Backlog", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            let data = response.response
            this.allBacklogTaskTypeData = data;
            data.forEach((obj) => {
              this.IMDataDrivenTaskBacklogList.push([obj.AnalyticRule, obj.AnalyticRuleCount])
            })
            if (this.IMDataDrivenTaskBacklogList.length > 0) {
              this.createTaskBacklogChart(this.IMDataDrivenTaskBacklogList);
            }
          } else {
            let data = response.response
            data.forEach((obj) => {
              this.IMDataDrivenTaskBacklogList.push([obj.AnalyticRule, obj.AnalyticRuleCount])
            })
            this.createTaskBacklogChart(this.IMDataDrivenTaskBacklogList);
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }


  createTaskBacklogChart(data) {
    const sortedData = data.sort((a, b) => b[1] - a[1]);
    const chart = Highcharts.chart('container1', {
      chart: {
        type: 'bar',
        marginLeft: 120,
        // plotBackgroundColor: '#F8F8F8',
        scrollablePlotArea: {
          minHeight: sortedData.length * 50,
          opacity: 1
        },
        height: 440
      },

      title: {
        text: ''
      },
      subtitle: {

      },
      xAxis: {
        type: 'category',
        title: {
          text: ''
        },
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Roboto !important',
            color: '#000000',
            fontSize: '0.6vw !important',
            textWrap: 'wrap !important'
          }
        },
        tickLength: 0
      },
      yAxis: {
        title: false,
        allowDecimals: false,
        labels: {
          style: {
            fontWeight: 'bold !important', // Make x-axis labels bold
            fontFamily: 'Calibri Light !important',
            color: '#000000',
            fontSize: '0.7vw !important',
            textWrap: 'wrap !important'
          }
        },
        plotBands: [{
          from: 0,
          to: sortedData.length + 100,
          color: '#F8F8F8'
        }],
        gridLineWidth: 0
      },
      plotOptions: {
        bar: {
          dataLabels: {
            enabled: false
          },
          borderWidth: 0,
          maxPointWidth: 60
        }
      },
      legend: {
        enabled: false
      },
      credits: {
        enabled: false
      },
      series: [{
        name: '',
        data: sortedData,
        color: 'rgb(0, 17, 50)',
        groupPadding: 0.1,
        pointPadding: 0
        // zIndex: 99999
      }]
    } as any);
  }


  getIMDataDrivenTBacklogList(response) {
    this.getIMDataDrivenTaskBacklogList = [];
    this.gridDataForIMBacklogList = undefined;
    this.buildingFilterIMBacklogList = [];
    this.equipmentFilterIMBacklogList = [];
    this.taskTypeFilterIMBacklogList = [];
    // this.coreDataService.getFtPPAData("Backlog", this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
    //   (response) => {
        localStorage.setItem('BacklogList', JSON.stringify(response.response));
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            // Count of Backlog tasks
            const count = this.countTasksByEquipment(response.response, "BacklogTasks");
            this.backlogCounts = count;

            // Iterate over the EquipmentThermalComfortTabledata to update or add new equipment counts
            // const updatedData = this.EquipmentThermalComfortTabledata.map(item => {
            //   const matchingTask = count[item.Equipment]; // Access the task count using equipment as the key
            //   return {
            //     ...item,
            //     BacklogTasks: matchingTask ? matchingTask.BacklogTasks : null,
            //   };
            // });

            // If any equipment from `count` is missing in the existing data, we need to insert it
            // const newData = Object.keys(count).reduce((acc, equipment) => {
            //   const matchingItem = updatedData.find(item => item.Equipment === equipment);
            //   if (!matchingItem) {
            //     acc.push({
            //       Equipment: equipment,
            //       PerOfOccupancy: 0, // Set default or modify as per your needs
            //       TotalHours: 0, // Set default or modify as per your needs
            //       TooHotHours: 0, // Set default or modify as per your needs
            //       TooColdHours: 0, // Set default or modify as per your needs
            //       TasksCompleted: null,
            //       BacklogTasks: count[equipment].BacklogTasks,
            //     });
            //   }
            //   return acc;
            // }, []);

            //  this.EquipmentThermalComfortTabledata = [...updatedData];
            // this.equipComfortGrid = process(this.EquipmentThermalComfortTabledata, this.EquipComfortState);
            // console.log("Updated from Backlog", this.EquipmentThermalComfortTabledata);

            ///
            //  console.log("backlogCounts", this.backlogCounts)
            response.response.forEach(ele => {
              ele['DateLastHit'] = ele['DateLastHit'] == '0001-01-01T00:00:00+00:00' ? '-' : new Date(ele['DateLastHit'])
            })
            this.getIMDataDrivenTaskBacklogList = response.response;
            // this.IMDataDrivenTCompleteListState.sort = [{ dir: "desc", field: "DateLastHit" }];
            this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState);

            if (this.getIMDataDrivenTaskBacklogList.length > 0 && this.getIMDataDrivenTaskBacklogList != null && this.getIMDataDrivenTaskBacklogList != undefined) {
              // console.log("getIMDataDrivenTaskBacklogList", this.getIMDataDrivenTaskBacklogList);
              // const{LOCATNNM,Equipment,TaskType} = this.getIMDataDrivenTaskBacklogList[0];
              // console.log("LOCATNNM", LOCATNNM, Equipment, TaskType);
              const uniqueMap = {
                building: new Map<string, any>(),
                equipment: new Map<string, any>(),
                taskType: new Map<string, any>()
              };
              const start = Date.now();
              this.getIMDataDrivenTaskBacklogList.reduce(elm => {
                if (elm.LOCATNNM && !uniqueMap.building.has(elm.LOCATNNM)) {
                  uniqueMap.building.set(elm.LOCATNNM, { value: elm.LOCATNNM, code: elm.LOCATNNM });
                }
                if (elm.Equipment && !uniqueMap.equipment.has(elm.Equipment)) {
                  uniqueMap.equipment.set(elm.Equipment, { value: elm.Equipment, code: elm.Equipment });
                }
                if (elm.TaskType && !uniqueMap.taskType.has(elm.TaskType)) {
                  uniqueMap.taskType.set(elm.TaskType, { value: elm.TaskType, code: elm.TaskType });
                }
              });

              this.buildingFilterIMBacklogList = Array.from(uniqueMap.building.values());
              this.equipmentFilterIMBacklogList = Array.from(uniqueMap.equipment.values());
              this.taskTypeFilterIMBacklogList = Array.from(uniqueMap.taskType.values());

              const end = Date.now();
              console.log(` getIMDataDrivenTBacklogList Duration: ${end - start} ms`);
            }

          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
    //   },
    //   error => {
    //     if (error) {
    //       this.shareData.ErrorHandler(error);
    //     }
    //   }
    // )
  }

  // public dataStateChangeBacklog(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTBacklogListState = state;
  //   this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState);
  // }


  // public dataStateChangeBacklog(state: DataStateChangeEvent): void {
  //   this.IMDataDrivenTBacklogListState = state;
  //   const filters = state.filter.filters;

  //   if (filters.length > 0) {
  //       let filteredData = this.getIMDataDrivenTaskBacklogList.slice(0);

  //       // Function to apply filters
  //       const applyFilter = (item: any, filter: FilterDescriptor) => {
  //           const filterValue = filter.value.toString().toLowerCase();
  //           if (filter.field === "PotentialEnergyWasteCostAvoidanceKWH") {
  //               // return item.PotentialEnergyWasteCostAvoidanceKWH.toString().includes(filterValue) ||
  //               //        item.PotentialEnergyWasteCostAvoidance$.toString().includes(filterValue);
  //           const kwhValue = item.PotentialEnergyWasteCostAvoidanceKWH !== null && item.PotentialEnergyWasteCostAvoidanceKWH !== undefined ? item.PotentialEnergyWasteCostAvoidanceKWH.toString().toLowerCase() : '';
  //           const dollarValue = item.PotentialEnergyWasteCostAvoidance$ !== null && item.PotentialEnergyWasteCostAvoidance$ !== undefined ? item.PotentialEnergyWasteCostAvoidance$.toString().toLowerCase() : '';
  //           return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);

  //           } else if (typeof filter.field === 'string') {
  //               // return item[filter.field].toString().includes(filterValue);
  //             const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined ? item[filter.field].toString().toLowerCase() : '';
  //             return fieldValue.includes(filterValue);
  //           }
  //           return true;
  //       };

  //       // Apply all filters
  //       filters.forEach(filterGroup => {
  //           if ('filters' in filterGroup) { // Check if it's a CompositeFilterDescriptor
  //               filteredData = filteredData.filter(item =>
  //                   (filterGroup as CompositeFilterDescriptor).filters.every(subFilter => applyFilter(item, subFilter as FilterDescriptor))
  //               );
  //           } else {
  //               filteredData = filteredData.filter(item => applyFilter(item, filterGroup as FilterDescriptor));
  //           }
  //       });

  //       // Process the filtered data for the grid
  //       this.gridDataForIMBacklogList = process(filteredData, {});
  //   } else {
  //       // No filters applied, so use the original data
  //       this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState);
  //   }
  // }


  getUrlForBacklog(data) {
    window.open(data.TrendingData);
  }

  public rowCallback = (context: RowClassArgs) => {
    if (context.dataItem.ServiceCallID != null && context.dataItem.ServiceCallID != undefined && context.dataItem.ServiceCallID != "") {
      return { gray: true };
    } else {
      return { white: true };
    }
  };

  calculateMovingAverage(number, allData) {
    const index = allData.indexOf(number);
    if (index === -1) {
      return 0;
    }
    let sum = 0;
    for (let i = index; i >= 0; i--) {
      sum += allData[i].TotalPctComfort;
    }
    return sum / (index + 1);
  }

  toggleSelected(widget: any): void {
    // widget.isSelected = !widget.isSelected;
    // if (widget.isSelected) {
    //   this.selectedWidgets.push(widget);
    // } else {
    //   const index = this.selectedWidgets.indexOf(widget);
    //   if (index !== -1) {
    //     this.selectedWidgets.splice(index, 1);
    //   }
    // }
    // console.log(this.selectedWidgets);
    // if(this.selectedWidgets.length > 0){
    //   this.IMTaskQTyStyling = false;
    // }else{
    //   this.IMTaskQTyStyling = true;
    // }


    widget.isSelected = !widget.isSelected;
    if (widget.isSelected) {
      const index = this.selectedWidgets.indexOf(this.openIMTasks);
      if (index !== -1) {
        this.selectedWidgets.splice(index, 1);
        this.openIMTasks.isSelected = false;
      }
      this.selectedWidgets.push(widget);
      if (widget === this.resolved) {
        if (this.resolvedOnPreIMTask) {
          this.resolvedOnPreIMTask.isSelected = true;
          this.selectedWidgets.push(this.resolvedOnPreIMTask);
        }
      }
      if (widget === this.awaitingApproval) {
        if (this.qtAwaitingApproval) {
          this.qtAwaitingApproval.isSelected = true;
          this.selectedWidgets.push(this.qtAwaitingApproval);
        }
      }

      if (widget === this.escalated) {
        if (this.causedByPlant && this.causedByEquipment && this.causedByInstallation) {
          this.causedByPlant.isSelected = true;
          this.causedByEquipment.isSelected = true;
          this.causedByInstallation.isSelected = true;
          this.selectedWidgets.push(this.causedByPlant);
          this.selectedWidgets.push(this.causedByEquipment);
          this.selectedWidgets.push(this.causedByInstallation);
        }
      }

      if (widget === this.openIMTasks) {
        this.clearWidgets(true);
        widget.isSelected = true;
        this.selectedWidgets.push(widget);
      }

    } else {
      const index = this.selectedWidgets.indexOf(widget);
      if (index !== -1) {
        this.selectedWidgets.splice(index, 1);
      }
      if (widget === this.resolved) {
        if (this.resolvedOnPreIMTask) {
          this.resolvedOnPreIMTask.isSelected = false;
          const resolvedOnPreIMTaskIndex = this.selectedWidgets.indexOf(this.resolvedOnPreIMTask);
          if (resolvedOnPreIMTaskIndex !== -1) {
            this.selectedWidgets.splice(resolvedOnPreIMTaskIndex, 1);
          }
        }
      }
      if (widget === this.awaitingApproval) {
        if (this.qtAwaitingApproval) {
          this.qtAwaitingApproval.isSelected = false;
          const qtAwaitingApprovalIndex = this.selectedWidgets.indexOf(this.qtAwaitingApproval);
          if (qtAwaitingApprovalIndex !== -1) {
            this.selectedWidgets.splice(qtAwaitingApprovalIndex, 1);
          }
        }
      }


      if (widget === this.escalated) {
        if (this.causedByPlant && this.causedByEquipment && this.causedByInstallation) {
          this.causedByPlant.isSelected = false;
          this.causedByEquipment.isSelected = false;
          this.causedByInstallation.isSelected = false;
          const causedByPlantIndex = this.selectedWidgets.indexOf(this.causedByPlant);
          if (causedByPlantIndex !== -1) {
            this.selectedWidgets.splice(causedByPlantIndex, 1);
          }
          const causedByEquipmentIndex = this.selectedWidgets.indexOf(this.causedByEquipment);
          if (causedByEquipmentIndex !== -1) {
            this.selectedWidgets.splice(causedByEquipmentIndex, 1);
          }

          const causedByInstallationIndex = this.selectedWidgets.indexOf(this.causedByInstallation);
          if (causedByInstallationIndex !== -1) {
            this.selectedWidgets.splice(causedByInstallationIndex, 1);
          }

        }
      }

    }
    this.Co2Reduction = 0;
    this.EquipmentReliabilityCount = 0;
    this.TotalRealisedEnergyWaste = 0;
    this.ThermalComfortCount = 0;
    this.TotalUnRealisedEnergyWaste = 0;
    this.TotalProblemAddressedData = [];
    this.filterIMDataDrivenTaskCompleted(this.selectedWidgets);
    if (widget !== this.openIMTasks) {
      this.filterTaskTypeData(this.selectedWidgets);
    }
    if (this.selectedWidgets.length > 0) {
      this.IMTaskQTyStyling = false;
      let outcomeData = JSON.parse(localStorage.getItem('getOutcomeData'));
      if (outcomeData != null && outcomeData != undefined) {
        let data = [];
        data = outcomeData;
        const filteredArray = data.filter(item1 =>
          this.selectedWidgets.some(item2 => item1.Outcome.trim() === item2.label)
        );
        let Er = 0;
        let Tc = 0;
        let Energy = 0;
        let TotalRealsedWCAvoidKWH = 0;
        let TotalRealsedWCAvoid = 0;
        let TotalUnRealsedWCAvoidKWH = 0;
        let TotalUnRealsedWCAvoid = 0;

        if (filteredArray.length > 0) {
          filteredArray.forEach(obj => {
            Er += obj.EquipmentReliability;
            Tc += obj.ThermalCount;
            Energy += obj.Energy;
            TotalRealsedWCAvoidKWH += obj.Total_RealisedEnergyWasteCostAvoidanceKWH;
            TotalRealsedWCAvoid += obj.Total_RealisedEnergyWasteCostAvoidance;
            TotalUnRealsedWCAvoidKWH += obj.Total_UnrealisedEnergyWasteCostAvoidanceKWH;
            TotalUnRealsedWCAvoid += obj.Total_UnrealisedEnergyWasteCostAvoidance;
          });

          if (this.Co2ReductionUnit == "kg CO2") {
            this.Co2Reduction = TotalRealsedWCAvoidKWH / 0.934;
          } else {
            let calculateUnitData = TotalRealsedWCAvoidKWH / 0.934
            this.Co2Reduction = calculateUnitData / 1000;
          }
          this.EquipmentReliabilityCount = TotalRealsedWCAvoidKWH;
          this.TotalRealisedEnergyWaste = TotalRealsedWCAvoid;
          this.ThermalComfortCount = TotalUnRealsedWCAvoidKWH;
          this.TotalUnRealisedEnergyWaste = TotalUnRealsedWCAvoid

          let DataForCompleteAction = [];
          DataForCompleteAction = [{
            name: 'Equipment Reliablity', value: Er
          }, {
            name: 'Thermal Comfort', value: Tc
          }, {
            name: 'Energy', value: Energy
          }];
          this.TotalProblemAddressedData = DataForCompleteAction;
        }
      } else {
        this.getOutcomeData();
      }
    } else {
      this.toggleIMTaskQty();
    }

  }

  toggleIMTaskQty() {
    this.IMTaskQTyStyling = true;
    this.resolved.isSelected = false;
    this.improved.isSelected = false;
    this.escalated.isSelected = false;
    this.noAccess.isSelected = false;
    this.quotesRequired.isSelected = false;
    this.awaitingApproval.isSelected = false;
    this.quotesAccepted.isSelected = false;
    this.quotesExpired.isSelected = false;
    this.quotesNotAccepted.isSelected = false;
    this.resolvedOnPreIMTask.isSelected = false;
    this.qtAwaitingApproval.isSelected = false;
    this.openIMTasks.isSelected = false;
    this.causedByPlant.isSelected = false;
    this.causedByEquipment.isSelected = false;
    this.causedByInstallation.isSelected = false;
    this.selectedWidgets = [];
    this.clearIMDataDrivenFilter();
    this.clearTaskTypeFilter();
    this.Co2Reduction = 0;
    this.EquipmentReliabilityCount = 0;
    this.TotalRealisedEnergyWaste = 0;
    this.ThermalComfortCount = 0;
    this.TotalUnRealisedEnergyWaste = 0;
    this.TotalProblemAddressedData = [];

    let outcomeData = JSON.parse(localStorage.getItem('getOutcomeData'));

    if (outcomeData != null && outcomeData != undefined) {
      let data = [];
      data = outcomeData;

      let Er = 0;
      let Tc = 0;
      let Energy = 0;
      let TotalRealsedWCAvoidKWH = 0;
      let TotalRealsedWCAvoid = 0;
      let TotalUnRealsedWCAvoidKWH = 0;
      let TotalUnRealsedWCAvoid = 0;

      if (data.length > 0) {
        data.forEach(obj => {
          Er += obj.EquipmentReliability;
          Tc += obj.ThermalCount;
          Energy += obj.Energy;
          TotalRealsedWCAvoidKWH += obj.Total_RealisedEnergyWasteCostAvoidanceKWH;
          TotalRealsedWCAvoid += obj.Total_RealisedEnergyWasteCostAvoidance;
          TotalUnRealsedWCAvoidKWH += obj.Total_UnrealisedEnergyWasteCostAvoidanceKWH;
          TotalUnRealsedWCAvoid += obj.Total_UnrealisedEnergyWasteCostAvoidance;
        });

        if (this.Co2ReductionUnit == "kg CO2") {
          this.Co2Reduction = TotalRealsedWCAvoidKWH / 0.934;
        } else {
          let calculateUnitData = TotalRealsedWCAvoidKWH / 0.934
          this.Co2Reduction = calculateUnitData / 1000;
        }
        this.EquipmentReliabilityCount = TotalRealsedWCAvoidKWH;
        this.TotalRealisedEnergyWaste = TotalRealsedWCAvoid;
        this.ThermalComfortCount = TotalUnRealsedWCAvoidKWH;
        this.TotalUnRealisedEnergyWaste = TotalUnRealsedWCAvoid

        let DataForCompleteAction = [];
        DataForCompleteAction = [{
          name: 'Equipment Reliablity', value: Er
        }, {
          name: 'Thermal Comfort', value: Tc
        }, {
          name: 'Energy', value: Energy
        }];
        this.TotalProblemAddressedData = DataForCompleteAction;
      }
    } else {
      this.getOutcomeDataForIMTaskQty();
    }
  }

  getOutcomeData() {
    this.coreDataService.getOutcomeData(this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
      (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            const setOutcomeData = JSON.stringify(response.response);
            localStorage.setItem('getOutcomeData', setOutcomeData)

            let outcomeData = response.response;
            if (outcomeData != null && outcomeData != undefined) {
              let data = [];
              data = outcomeData;
              const filteredArray = data.filter(item1 =>
                this.selectedWidgets.some(item2 => item1.Outcome.trim() === item2.label)
              );

              let Er = 0;
              let Tc = 0;
              let Energy = 0;
              let TotalRealsedWCAvoidKWH = 0;
              let TotalRealsedWCAvoid = 0;
              let TotalUnRealsedWCAvoidKWH = 0;
              let TotalUnRealsedWCAvoid = 0;


              if (filteredArray.length > 0) {
                filteredArray.forEach(obj => {
                  Er += obj.EquipmentReliability;
                  Tc += obj.ThermalCount;
                  Energy += obj.Energy;
                  TotalRealsedWCAvoidKWH += obj.Total_RealisedEnergyWasteCostAvoidanceKWH;
                  TotalRealsedWCAvoid += obj.Total_RealisedEnergyWasteCostAvoidance;
                  TotalUnRealsedWCAvoidKWH += obj.Total_UnrealisedEnergyWasteCostAvoidanceKWH;
                  TotalUnRealsedWCAvoid += obj.Total_UnrealisedEnergyWasteCostAvoidance;
                });

                if (this.Co2ReductionUnit == "kg CO2") {
                  this.Co2Reduction = TotalRealsedWCAvoidKWH / 0.934;
                } else {
                  let calculateUnitData = TotalRealsedWCAvoidKWH / 0.934
                  this.Co2Reduction = calculateUnitData / 1000;
                }
                this.EquipmentReliabilityCount = TotalRealsedWCAvoidKWH;
                this.TotalRealisedEnergyWaste = TotalRealsedWCAvoid;
                this.ThermalComfortCount = TotalUnRealsedWCAvoidKWH;
                this.TotalUnRealisedEnergyWaste = TotalUnRealsedWCAvoid

                let DataForCompleteAction = [];
                DataForCompleteAction = [{
                  name: 'Equipment Reliablity', value: Er
                }, {
                  name: 'Thermal Comfort', value: Tc
                }, {
                  name: 'Energy', value: Energy
                }];
                this.TotalProblemAddressedData = DataForCompleteAction;
              }
            }
          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
      },
      error => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      }
    )
  }


  getOutcomeDataForIMTaskQty() {
    this.coreDataService.getOutcomeData(this.locations, new Date(this.range["start"]), new Date(this.range["end"])).subscribe(
      (response) => {
        if (response.StatusCode == StatusCodes.OK && response != null) {
          if (response.response.length > 0) {
            const setOutcomeData = JSON.stringify(response.response);
            localStorage.setItem('getOutcomeData', setOutcomeData)

            let outcomeData = response.response;
            if (outcomeData != null && outcomeData != undefined) {
              let data = [];
              data = outcomeData;

              let Er = 0;
              let Tc = 0;
              let Energy = 0;
              let TotalRealsedWCAvoidKWH = 0;
              let TotalRealsedWCAvoid = 0;
              let TotalUnRealsedWCAvoidKWH = 0;
              let TotalUnRealsedWCAvoid = 0;

              if (data.length > 0) {
                console.log("data test", data);
                data.forEach(obj => {
                  Er += obj.EquipmentReliability;
                  Tc += obj.ThermalCount;
                  Energy += obj.Energy;
                  TotalRealsedWCAvoidKWH += obj.Total_RealisedEnergyWasteCostAvoidanceKWH;
                  TotalRealsedWCAvoid += obj.Total_RealisedEnergyWasteCostAvoidance;
                  TotalUnRealsedWCAvoidKWH += obj.Total_UnrealisedEnergyWasteCostAvoidanceKWH;
                  TotalUnRealsedWCAvoid += obj.Total_UnrealisedEnergyWasteCostAvoidance;
                });

                if (this.Co2ReductionUnit == "kg CO2") {
                  this.Co2Reduction = TotalRealsedWCAvoidKWH / 0.934;
                } else {
                  let calculateUnitData = TotalRealsedWCAvoidKWH / 0.934
                  this.Co2Reduction = calculateUnitData / 1000;
                }
                this.EquipmentReliabilityCount = TotalRealsedWCAvoidKWH;
                this.TotalRealisedEnergyWaste = TotalRealsedWCAvoid;
                this.ThermalComfortCount = TotalUnRealsedWCAvoidKWH;
                this.TotalUnRealisedEnergyWaste = TotalUnRealsedWCAvoid

                let DataForCompleteAction = [];
                DataForCompleteAction = [{
                  name: 'Equipment Reliablity', value: Er
                }, {
                  name: 'Thermal Comfort', value: Tc
                }, {
                  name: 'Energy', value: Energy
                }];
                this.TotalProblemAddressedData = DataForCompleteAction;
              }
            }

          }
        } else {
          this.toastrService.error("Internal Server Error, Please Contact Support")
        }
      },
      error => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      }
    )
  }

  clearWidgets(IsOpenTask?: boolean) {
    this.IMTaskQTyStyling = false;
    this.resolved.isSelected = false;
    this.improved.isSelected = false;
    this.escalated.isSelected = false;
    this.noAccess.isSelected = false;
    this.quotesRequired.isSelected = false;
    this.awaitingApproval.isSelected = false;
    this.quotesAccepted.isSelected = false;
    this.quotesExpired.isSelected = false;
    this.quotesNotAccepted.isSelected = false;
    this.resolvedOnPreIMTask.isSelected = false;
    this.qtAwaitingApproval.isSelected = false;
    this.openIMTasks.isSelected = false;
    this.causedByPlant.isSelected = false;
    this.causedByEquipment.isSelected = false;
    this.causedByInstallation.isSelected = false;
    this.selectedWidgets = [];

    this.Co2Reduction = 0;
    this.EquipmentReliabilityCount = 0;
    this.TotalRealisedEnergyWaste = 0;
    this.ThermalComfortCount = 0;
    this.TotalUnRealisedEnergyWaste = 0;
    this.TotalProblemAddressedData = [];
    if (!IsOpenTask){
      localStorage.removeItem('getOutcomeData');
    }
    this.IMDataDrivenTCompleteListState = {
      skip: 0,
      filter: {
        logic: 'and',
        filters: []
      },
    };

    this.IMDataDrivenTAwaitingListState = {
      skip: 0,
      filter: {
        logic: 'and',
        filters: []
      },
    };

    this.IMDataDrivenTBacklogListState = {
      skip: 0,
      filter: {
        logic: 'and',
        filters: []
      },
    };
  }

  changeMovingAverageTrending(trendLine) {
    if (trendLine == '3D') {
      this.IsThreeDayMovingAverage = true;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '1W') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = true
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '1M') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = true
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '3M') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = true;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
  }

  calculateMovingAverageTrending(data, recordCount, trendingCount) {
    const movingAverages: any[] = [];
    for (let i = 0; i < data.length; i++) {
      let sum = 0;
      let count = 0;
      for (let j = Math.max(0, i - recordCount); j <= i; j++) {
        if (data[j]) {
          sum += data[j].TotalPctComfort;
          count++;
        }
      }
      const average = sum / trendingCount;
      movingAverages.push({
        'AllDates': data[i].AllDates,
        'TotalPctComfort': average
      });
    }
    return movingAverages;
  }

  getTecSummaryCodeWithDate(entry: string): string {
    if (entry != null && entry != undefined && entry != "") {
      let amIndex = entry.indexOf('AM');
      let pmIndex = entry.indexOf('PM');
      let endIndex: number;
      if (amIndex !== -1 && pmIndex !== -1) {
        endIndex = Math.min(amIndex, pmIndex) + 2;
      } else if (amIndex !== -1) {
        endIndex = amIndex + 2;
      } else if (pmIndex !== -1) {
        endIndex = pmIndex + 2;
      } else {
        endIndex = entry.length;
      }

      return entry.substring(0, endIndex);
    } else {
      return entry;
    }

  }

  openPopup(technicianSummary: string): void {
    this.popupContent = technicianSummary;
    this.showPopup = true;
  }

  closePopup(value): void {
    if (value == 'cancel') {
      this.showPopup = false;
    }
  }

  openPopupTaskDescription(taskDescription: string): void {
    this.popupTaskDescriptionContent = taskDescription;
    this.showPopupTaskDescription = true;
  }

  closePopupTaskDescription(value): void {
    if (value == 'cancel') {
      this.showPopupTaskDescription = false;
    }
  }

  splitTextWithIcon(text: string | null | undefined): {textWithoutLastWord: string, lastWord: string } {
    if (!text || !text.trim()) {
      return { textWithoutLastWord: '', lastWord: ''};
    }

    const words = text.split(' ');
    const lastWord = words.pop() || '';
    const textWithoutLastWord = words.join(' ');
    return { textWithoutLastWord, lastWord};
  }

  onInputChange(event: Event, dataItem: any): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Check if the value has more than two decimal places
    if (value.includes('.')) {
      const [integerPart, decimalPart] = value.split('.');
      if (decimalPart.length > 2) {
        value = `${integerPart}.${decimalPart.substring(0, 2)}`;
        inputElement.value = value; // Update the input value to enforce two decimal places
      }
    }

    // Update the model value
    dataItem.CustomerRating = parseFloat(value);
  }

  enterRating(dataItem) {
    let IsBackProcessed = false;
    if (dataItem?.ServiceCallID == undefined || dataItem?.ServiceCallID == null || dataItem?.ServiceCallID == '') {
      IsBackProcessed = true;
    }
    if (dataItem.CustomerRating !== null &&
      dataItem.CustomerRating !== undefined &&
      !isNaN(dataItem.CustomerRating) &&
      dataItem.CustomerRating >= 0 &&
      dataItem.CustomerRating <= 100) {
      this.coreDataService.updateCustomerRating(IsBackProcessed == true ? dataItem.ID : dataItem.DEX_ID, dataItem.CustomerRating, "Customer", IsBackProcessed).subscribe(
        (response) => {
          if (response.StatusCode == StatusCodes.OK && response != null) {
            this.editItem = null;
            this.shareData.showSuccess("Customer Rating Updated Successfully");
          } else {
            this.toastrService.error("Internal Server Error, Please Contact Support")
          }
        },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        }
      )
    } else {
      this.shareData.showWarning("Customer rating must be a number between 0 and 100");
    }
  }
  editItem: any = null;
  editRating(dataItem: any) {
    this.editItem = dataItem;
  }

  editItemEnergyWaste: any = null;
  editEnergyWaste(dataItem: any) {
    this.editItemEnergyWaste = dataItem;
  }

  onInputChangeEnergyWaste(event: Event, dataItem: any): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    if (value.includes('.')) {
      const [integerPart, decimalPart] = value.split('.');
      if (decimalPart.length > 2) {
        value = `${integerPart}.${decimalPart.substring(0, 2)}`;
        inputElement.value = value;
      }
    }

    dataItem.EnergyWasteCostAvoidanceKWH = parseFloat(value);
  }

  enterEnergyWaste(dataItem) {
    let IsBackProcessed = false;
    if (dataItem?.ServiceCallID == undefined || dataItem?.ServiceCallID == null || dataItem?.ServiceCallID == '') {
      IsBackProcessed = true;
    }
    if (dataItem.EnergyWasteCostAvoidanceKWH !== null &&
      dataItem.EnergyWasteCostAvoidanceKWH !== undefined &&
      !isNaN(dataItem.EnergyWasteCostAvoidanceKWH) &&
      dataItem.EnergyWasteCostAvoidanceKWH >= 0) {

      this.coreDataService.updateEnergyWasteCostAvoidanceKwh(IsBackProcessed == true ? dataItem.ID : dataItem.DEX_ID, dataItem.EnergyWasteCostAvoidanceKWH, IsBackProcessed).subscribe(
        (response) => {
          if (response.StatusCode == StatusCodes.OK && response != null) {
            this.editItemEnergyWaste = null;
            this.loadGetIMDataDrivenTCompletedList();
            this.loadGetPerformanceData();
            this.shareData.showSuccess("Energy Waste Cost Avoidance KWH Updated Successfully");
          } else {
            this.toastrService.error("Internal Server Error, Please Contact Support")
          }
        },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        }
      )
    } else {
      this.shareData.showWarning("Energy waste must be greater than 0");
    }
  }

  getOneMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let oneMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    oneMonthsAgo.setMonth(currentDate.getMonth() - 1);

    let lastOneMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= oneMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastOneMonthsData[0].AllDates);

    let lastTwoRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastTwoRecords.push(data[i]);
        if (lastTwoRecords.length > count) {
          lastTwoRecords.shift();
        }
      }
    }
    lastTwoRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastTwoRecords.length; j++) {
      lastOneMonthsData.unshift(lastTwoRecords[j]);
    }
    return lastOneMonthsData;
  }

  getThreeMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let threeMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

    let lastThreeMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= threeMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastThreeMonthsData[0].AllDates);

    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }
    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastRecords.length; j++) {
      lastThreeMonthsData.unshift(lastRecords[j]);
    }
    return lastThreeMonthsData;
  }

  getSixMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let SixMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    SixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    let lastSixMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= SixMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastSixMonthsData[0].AllDates);
    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }
    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastRecords.length; j++) {
      lastSixMonthsData.unshift(lastRecords[j]);
    }
    return lastSixMonthsData;
  }


  getTwelveMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let TwelveMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    TwelveMonthsAgo.setMonth(currentDate.getMonth() - 13);

    let lastTwelveMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= TwelveMonthsAgo && itemDate <= currentDate;
    });
    const targetDate = new Date(lastTwelveMonthsData[0].AllDates);
    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }

    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });

    for (let j = 0; j < lastRecords.length; j++) {
      lastTwelveMonthsData.unshift(lastRecords[j]);
    }

    if (lastRecords.length == count) {
      return { movingdata: lastTwelveMonthsData, recordsExist: true, existRecordCount: lastRecords.length }
    } else {
      return { movingdata: lastTwelveMonthsData, recordsExist: false, existRecordCount: lastRecords.length }
    }
  }

  // isBacklogRowSelected(event: any): void {
  //   if (event != undefined) {
  //     this.SelectBacklogRowData = event;
  //   }
  // }

  // getBacklogDetail(){
  //   if (this.SelectBacklogRowData != undefined) {
  //     let selectedRowData = this.SelectBacklogRowData.selectedRows[0].dataItem
  //     localStorage.setItem("BacklogData", JSON.stringify(selectedRowData));
  //     this.router.navigate(["/PlaceCall"]);
  //   }
  // }

  raiseServiceCall(data) {
    if (data != null && data != undefined) {
      let selectedRowData = data;
      localStorage.setItem("BacklogData", JSON.stringify(selectedRowData));
      this.router.navigate(["/PlaceCall"]);
    }
  }

  editItemAccountManager: any = null;
  editRatingAccountManager(dataItem: any) {
    this.editItemAccountManager = dataItem;
  }

  onInputChangeAccountManager(event: Event, dataItem: any): void {
    const inputElement = event.target as HTMLInputElement;
    let value = inputElement.value;

    // Check if the value has more than two decimal places
    if (value.includes('.')) {
      const [integerPart, decimalPart] = value.split('.');
      if (decimalPart.length > 2) {
        value = `${integerPart}.${decimalPart.substring(0, 2)}`;
        inputElement.value = value; // Update the input value to enforce two decimal places
      }
    }

    // Update the model value
    dataItem.AccountManagerRating = parseFloat(value);
  }

  enterRatingAccountManager(dataItem) {
    let IsBackProcessed = false;
    if (dataItem?.ServiceCallID == undefined || dataItem?.ServiceCallID == null || dataItem?.ServiceCallID == '') {
      IsBackProcessed = true;
    }
    if (dataItem.AccountManagerRating !== null &&
      dataItem.AccountManagerRating !== undefined &&
      !isNaN(dataItem.AccountManagerRating) &&
      dataItem.AccountManagerRating >= 0 &&
      dataItem.AccountManagerRating <= 100) {
      this.coreDataService.updateCustomerRating(IsBackProcessed == true ? dataItem.ID : dataItem.DEX_ID, dataItem.AccountManagerRating, "AccountManager", IsBackProcessed).subscribe(
        (response) => {
          if (response.StatusCode == StatusCodes.OK && response != null) {
            this.editItemAccountManager = null;
            this.shareData.showSuccess("Account Manager Rating Updated Successfully");
          } else {
            this.toastrService.error("Internal Server Error, Please Contact Support")
          }
        },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        }
      )
    } else {
      this.shareData.showWarning("Account manager rating must be a number between 0 and 100");
    }
  }

  getHighestDEXIDRecords(data: any[]): any[] {
    const grouped = data.reduce((acc, record) => {
      const key = `${record.Equipment}-${record.FTID}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(record);
      return acc;
    }, {});
    const result = Object.values(grouped).map((group: any) => {
      return group.sort((a, b) => b.DEX_ID - a.DEX_ID)[0];
    });

    return result;
  }

  editOutcomeItem: any = null;
  previousOutcomeValue: any = null;
  editOutcome(dataItem: any) {
    this.editOutcomeItem = dataItem;
    this.previousOutcomeValue = dataItem.WS_Response_String;
  }

  outcomeDropData = [
    {
      key: 'RESOLVED',
      value: 'RESOLVED'
    },
    {
      key: 'NO ACCESS',
      value: 'NO ACCESS'
    },
    {
      key: 'IMPROVED',
      value: 'IMPROVED'
    },
    {
      key: 'ACTIVE QUOTE OUTSTANDING',
      value: 'ACTIVE QUOTE OUTSTANDING'
    },
    {
      key: 'MECHANICAL REQUIRED',
      value: 'MECHANICAL REQUIRED'
    },
    {
      key: 'CONTROLS REQUIRED',
      value: 'CONTROLS REQUIRED'
    },
    {
      key: 'QUOTE REQUIRED',
      value: 'QUOTE REQUIRED'
    },
    {
      key: 'RESOLVED ON PREV SERVICE CALL',
      value: 'RESOLVED ON PREV SERVICE CALL'
    },
    {
      key: 'FALSE POSITIVE',
      value: 'FALSE POSITIVE'
    },
    {
      key: 'EQUIPMENT DE-COMMISSIONED',
      value: 'EQUIPMENT DE-COMMISSIONED'
    },
    {
      key: 'ACTIVE BUILDING WORKS',
      value: 'ACTIVE BUILDING WORKS'
    },
    {
      key: 'RESOLVED ON PREV IM TASK',
      value: 'RESOLVED ON PREV IM TASK'
    },
    {
      key: 'CAUSED BY PLANT CONFIG/DESIGN/USE',
      value: 'CAUSED BY PLANT CONFIG/DESIGN/USE'
    },
    {
      key: 'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE',
      value: 'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE'
    },
    {
      key: 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR',
      value: 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR'
    },
    {
      key: 'EQUIPMENT UNDER MAINTENANCE',
      value: 'EQUIPMENT UNDER MAINTENANCE'
    },
    {
      key: 'EQUIPMENT OPERATING IN FIRE MODE',
      value: 'EQUIPMENT OPERATING IN FIRE MODE'
    },

    // {
    //   key: 'ALREADY RESOLVED',
    //   value: 'ALREADY RESOLVED'
    // },
    // {
    //   key: 'FURTHER CONSIDERATION REQ.',
    //   value: 'FURTHER CONSIDERATION REQ.'
    // },
    // {
    //   key: 'AWAITING APPROVAL',
    //   value: 'AWAITING APPROVAL'
    // },
    // {
    //   key: 'DRAFT',
    //   value: 'DRAFT'
    // },
    // {
    //   key: 'QUOTE EXPIRED',
    //   value: 'EXPIRED'
    // },
    // {
    //   key: 'QUOTE NOT ACCEPTED',
    //   value: 'LOST'
    // },
    // {
    //   key: 'QUOTE ACCEPTED',
    //   value: 'WON'
    // },
  ]

  outcomeValueChange(value, dataItem) {
    let filterOutcomeKey = this.outcomeDropData.filter((obj) => {
      return obj.value.trim() === value.trim();
    })
    dataItem.WS_Response_String = filterOutcomeKey[0].key;
    this.editOutcomeItem = null;
    //  if(value != null && value != undefined && value != '' && value != ""){
    //   this.coreDataService.updateOutcome(dataItem.DEX_ID, value).subscribe(
    //     (response) => {
    //       if (response.StatusCode == StatusCodes.OK && response != null) {
    //           this.shareData.showSuccess("Outcome Updated Successfully");
    //           this.clearWidgets();
    //           this.getIMDataDrivenTCompletedList();
    //           this.getPerformanceData();
    //       }else{
    //         this.toastrService.error("Internal Server Error, Please Contact Support")
    //       }
    //     },
    //     error => {
    //       if (error) {
    //         this.shareData.ErrorHandler(error);
    //       }
    //     }
    //   )
    //  }else{
    //   this.shareData.showError("Please select the outcome");
    //  }

  }

  cancelEdit(dataItem) {
    dataItem.WS_Response_String = this.previousOutcomeValue;
    this.editOutcomeItem = null;
  }


  outcomeDropn: string = '';
  prevOutcomeSelected: string = '';
  outcomeSummary: string = '';
  inputSummary: string = '';

  initialOutcomeSummary: string = '';
  addedSummaries: string[] = [];

  openOutcomePopup(outcome: string, dataItem): void {
    if (outcome != null && outcome != undefined) {

      this.outcomeDropn = this.findOutcomeKey(outcome);
      this.prevOutcomeSelected = this.outcomeDropn
      this.initialOutcomeSummary = dataItem?.Outcome_Summary || '';
      this.outcomeSummary = this.initialOutcomeSummary;
      this.showOutcomePopup = true;
      if (dataItem?.ServiceCallID == undefined || dataItem?.ServiceCallID == null || dataItem?.ServiceCallID == '') {
        this.backlogRecordID = dataItem?.ID;
      } else {
        this.outDexID = dataItem?.OutDex_ID;
      }
    }
  }
  openBacklogOutcomePopup(dataItem): void {
    this.outcomeDropn = ''
    this.backlogRecordID = dataItem?.ID;
    this.prevOutcomeSelected = this.outcomeDropn
    console.log(this.prevOutcomeSelected)
    this.showBacklogOutcomePopup = true;
  }

  closeOutcomePopup(value): void {
    if (value == 'cancel') {
      this.showOutcomePopup = false;
      this.showBacklogOutcomePopup = false;
      this.outcomeDropn = '';
      this.backlogRecordID = 0;
      this.outDexID = 0;
      this.prevOutcomeSelected = '';
      this.outcomeSummary = '';
      this.inputSummary = '';
      this.initialOutcomeSummary = '';
      this.addedSummaries = [];
    }
  }

  splitText(text: string): { beforeDash: string, afterDash: string } {
    if (!text) {
      return { beforeDash: '', afterDash: '' };
    }
    const [beforeDash, afterDash] = text.split(' - ', 2);
    return {
      beforeDash: beforeDash ? beforeDash.trim() : '',
      afterDash: afterDash ? afterDash.trim() : ''
    };
  }

  findOutcomeKey(key: string): string {
    const outcome = this.outcomeDropData.find(item => item.key.trim() === key.trim());
    return outcome ? outcome.value : '';
  }

  saveOutcome(isBacklog?: boolean) {
    if (this.outcomeDropn != null && this.outcomeDropn != undefined && this.outcomeDropn != '' && this.outcomeDropn != "") {

      const currentBacklogState = { ...this.IMDataDrivenTBacklogListState };

      const data = {
        ID: isBacklog == true ? this.backlogRecordID : this.outDexID,
        Outcome: this.outcomeDropn,
        OutcomeSummary: this.outcomeSummary,
        IsBacklogData: isBacklog
      }
      if (isBacklog != true) {
        if (this.backlogRecordID > 0 && (this.outDexID == undefined || this.outDexID == null || this.outDexID == 0)) {
          data.IsBacklogData = true;
          data.ID = this.backlogRecordID;
        }
      }
      this.coreDataService.updateOutcome(data).subscribe(
        (response) => {
          if (response.StatusCode == StatusCodes.OK && response != null) {
            this.showOutcomePopup = false;
            this.showBacklogOutcomePopup = false;
            this.outcomeDropn = '';
            this.backlogRecordID = 0;
            this.outDexID = 0;
            this.prevOutcomeSelected = '';
            this.outcomeSummary = '';
            this.inputSummary = '';
            this.initialOutcomeSummary = '';
            this.addedSummaries = [];
            this.shareData.showSuccess("Outcome Updated Successfully");
            this.clearWidgets();
            if (isBacklog) {
              this.loadGetIMDataDrivenTBacklogList();
              this.loadGetIMDataDrivenTaskBacklogData();
            }
            this.loadGetIMDataDrivenTaskTypeData();
            this.loadGetIMDataDrivenTCompletedList();
            this.loadGetIMDataDrivenTTuningList();
            this.loadGetPerformanceData();
            this.headerComponent.clearEquipment();
            this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);
            this.allSelectedEquipments = [];
            this.getCompleteList = [];
            this.getAwaitingListCount = [];
            this.getAwaitingListCount = this.getIMDataDrivenTAwaitingApprovalList;
            this.getTaskTpeOnEquipments = [];
            this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, currentBacklogState);
            this.IMDataDrivenTBacklogListState = currentBacklogState;
          } else {
            this.toastrService.error("Internal Server Error, Please Contact Support")
          }
        },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        }
      )
    } else {
      this.shareData.showError("Please select the outcome");
    }
  }

  addSummary() {
    // if (this.inputSummary.trim()) {
    //   const formattedDate = this.datePipe.transform(new Date(), 'MMM d y h:mm a');
    //   this.outcomeSummary += `DevOne - ${formattedDate} - (Updated) \n${this.inputSummary}\n\n`;
    //   this.inputSummary = '';
    // }else{
    //    this.shareData.showWarning("Pleas enter the summary");
    // }

    if (this.inputSummary.trim()) {
      let out_come = this.outcomeDropData.find((res) => {
        if (res.value === this.outcomeDropn) {
          return res;
        }
      })

      let prev_out_come = this.outcomeDropData.find((res) => {
        if (res.value === this.prevOutcomeSelected) {
          return res;
        }
      })
      if (prev_out_come == undefined || prev_out_come == null) {
        prev_out_come = { key: 'None', value: 'None' }
      }
      const formattedDate = this.datePipe.transform(new Date(), 'MMM d y h:mm a');
      const newEntry = `${this.userRole?.Name} - ${formattedDate} - ( Updated From ${this.toCapitalize(prev_out_come?.key?.toString())} > ${this.toCapitalize(out_come?.key?.toString())}): \n${this.inputSummary}\n\n`;

      this.addedSummaries.push(newEntry);

      this.outcomeSummary = newEntry + this.outcomeSummary;
      this.prevOutcomeSelected = this.outcomeDropn;
      this.inputSummary = '';
    } else {
      this.shareData.showWarning("Please enter the summary");
    }
  }
  isPrioritization: boolean = false;

  openPrioritization() {
    this.isPrioritization = true;

    this.GetLocationPriority();
  }



  GetLocationPriority() {
    const allLocations = this.sessionStorage.retrieve('locations');
    const filteredLocations = allLocations.filter((res) =>
      this.locations.includes(parseInt(res.LocationID?.trim()))
    );

    if (filteredLocations.length == 1) {
      this.coreDataService.GetFTLocationPriority(filteredLocations[0].CustomerCode, filteredLocations[0].LocationCode).subscribe(response => {
        if (response != null && response != undefined) {
          if (response.StatusCode === 200) {
            this.priorityItems.map((res) => {
              localStorage.setItem('EquipmentLongevityIndex', response.response.EQReliablityPriority == 'High' ? '2' : response.response.EQReliablityPriority == 'Medium' ? '1' : response.response.EQReliablityPriority == 'Low' ? '0' : null);
              localStorage.setItem('ThermalComfortIndex', response.response.ThermalCountPriority == 'High' ? '2' : response.response.ThermalCountPriority == 'Medium' ? '1' : response.response.ThermalCountPriority == 'Low' ? '0' : null);
              localStorage.setItem('EnergyIndex', response.response.EnergyPriority == 'High' ? '2' : response.response.EnergyPriority == 'Medium' ? '1' : response.response.EnergyPriority == 'Low' ? '0' : null);
              setTimeout(() => {
                this.createPriorityDonutChart();
                this.createEnergyDonutChart();
                this.createThermalDonutChart();
              }, 500); // Delay for the next JavaScript execution cycle
            })
          }
        }
      }, catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
    }
  }
  public closePriority(value) {
    if (value === "cancel") {
      this.isPrioritization = false;
    }
  }
  onDragStart(event: DragEvent, index: number) {
    this.draggedItemIndex = index;
    event.dataTransfer?.setData('text/plain', index.toString()); // Set index as data
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    const draggedItemIndexStr = event.dataTransfer?.getData('text/plain');
    if (draggedItemIndexStr !== null) {
      const draggedItemIndex = parseInt(draggedItemIndexStr, 10);
      if (this.draggedItemIndex !== null && draggedItemIndex !== null) {
        const draggedItem = this.priorityItems[this.draggedItemIndex];
        const dropItem = this.priorityItems[dropIndex];
        const draggedPriorityIndex = this.priorityLevels.indexOf(draggedItem.priority);
        const dropPriorityIndex = this.priorityLevels.indexOf(dropItem.priority);
        const newPriority = this.priorityLevels[dropPriorityIndex];
        this.priorityItems[this.draggedItemIndex] = { ...draggedItem, priority: newPriority };
        this.priorityItems[dropIndex] = { ...dropItem, priority: this.priorityLevels[draggedPriorityIndex] };
        this.priorityItems = [...this.priorityItems];

        console.log('Updated priorityItems:', this.priorityItems);
        this.draggedItemIndex = null;
      }
    }
  }



  ApplyPriority() {

    const payload = [];
    const allLocations = this.sessionStorage.retrieve('locations');
    const filteredLocations = allLocations.filter((res) =>
      this.locations.includes(parseInt(res.LocationID?.trim()))
    );
    const data = { CUSTNMBR: '', ADRSCODE: '', EQReliablityPriority: '', ThermalCountPriority: '', EnergyPriority: '' }
    filteredLocations.forEach((res) => {
      data.CUSTNMBR = res.CustomerCode
      data.ADRSCODE = res.LocationCode
      data.EQReliablityPriority = this.ELSelectedIndex == 0 ? 'Low' : this.ELSelectedIndex == 1 ? 'Medium' : this.ELSelectedIndex == 2 ? 'High' : null;
      data.ThermalCountPriority = this.TCSelectedIndex == 0 ? 'Low' : this.TCSelectedIndex == 1 ? 'Medium' : this.TCSelectedIndex == 2 ? 'High' : null;
      data.EnergyPriority = this.EnergySelectedIndex == 0 ? 'Low' : this.EnergySelectedIndex == 1 ? 'Medium' : this.EnergySelectedIndex == 2 ? 'High' : null;
      payload.push(data)
    })

    this.coreDataService.UpdateFTLocationPriority(payload).subscribe(response => {
      if (response != null && response != undefined) {
        if (response.StatusCode === 200) {
          this.shareData.showSuccess("Priority updated successfully.")
          this.isPrioritization = false;
          localStorage.setItem('EquipmentLongevityIndex', this.ELSelectedIndex !== null ? this.ELSelectedIndex.toString() : null);
          localStorage.setItem('ThermalComfortIndex', this.TCSelectedIndex !== null ? this.TCSelectedIndex.toString() : null);
          localStorage.setItem('EnergyIndex', this.EnergySelectedIndex !== null ? this.EnergySelectedIndex.toString() : null);
        }
        else {
          this.shareData.showError("Error has occured please contact support.");
        }
      }
    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
  clearEquipment() {
    this.equipment.forEach(item => item.selected = false);
    this.selectAll = false;
    this.lastConfirmedSelection = [];
  }
  EquipmentFiltered: boolean = false
  confirmSelection(Equip?: string, fromSelected?: boolean) {
    let selectedItems = []
    if (Equip !== undefined && Equip !== null && Equip !== '') {
      selectedItems = this.equipment?.filter(item => item.name === Equip) || [];
    } else {
      selectedItems = this.getSelectedItems();
      if (selectedItems.length > 0) {
        this.EquipmentFiltered = true;
      } else {
        this.EquipmentFiltered = false;
      }
    }
    if (Equip === 'Deselected') {
      if (this.EquipmentFiltered == false) {
        selectedItems = this.equipment;
      }
      else {
        selectedItems = this.getSelectedItems();
      }
    }
    this.equipmentsData.emit(selectedItems);
    this.getDataOnEquipmentFilter(selectedItems, fromSelected)
    this.lastConfirmedSelection = JSON.parse(JSON.stringify(this.equipment));
    this.isEquipment = false;
    this.resetSearchAndFilter();
  }

  toggleSelectAll() {
    this.filteredEquipment.forEach(item => item.selected = this.selectAll);
  }
  cancelSelection() {
    this.isEquipment = false;
    this.revertToLastConfirmed();
    this.resetSearchAndFilter();
  }

  getSelectedItems() {
    return this.equipment.filter(item => item.selected);
  }
  openEquipments() {
    this.isEquipment = true;
    let isIMUPdatedValue = JSON.parse(localStorage.getItem('IsIMDataUpdated'));
    if (isIMUPdatedValue == true) {
      let completeData = JSON.parse(localStorage.getItem("CompleteList"))
      let awaitingData = JSON.parse(localStorage.getItem("AwaitingList"))
      let backlogData = JSON.parse(localStorage.getItem("BacklogList"))

      if (completeData != null && completeData != undefined && awaitingData != null && awaitingData != undefined && backlogData != null && backlogData != undefined) {
        let getEquipments = this.findCommonAndUniqueEquipments(completeData, awaitingData, backlogData);
        getEquipments.sort((a, b) => a.name.localeCompare(b.name));
        this.equipment = [...getEquipments];
        this.filteredEquipment = [...this.equipment];
      }

      localStorage.setItem("IsIMDataUpdated", JSON.stringify(false));
    }

  }
  findCommonAndUniqueEquipments(arr1: any[], arr2: any[], arr3: any[]): any[] {
    // Extracting the names of equipment
    const equipment1 = arr1.map(item => item.Equipment);
    const equipment2 = arr2.map(item => item.Equipment);
    const equipment3 = arr3.map(item => item.Equipment);

    // Finding common and unique equipment
    // const commonEquipments = equipment1.filter(e => equipment2.includes(e));
    const commonEquipments = equipment1.filter(e => equipment2.includes(e) && equipment3.includes(e));
    const uniqueEquipments = [
      // ...equipment1.filter(e => !equipment2.includes(e)),
      // ...equipment2.filter(e => !equipment1.includes(e)),

      ...equipment1.filter(e => !equipment2.includes(e) || !equipment3.includes(e)),
      ...equipment2.filter(e => !equipment1.includes(e) || !equipment3.includes(e)),
      ...equipment3.filter(e => !equipment1.includes(e) || !equipment2.includes(e)),
    ];

    const allEquipment = Array.from(new Set([...commonEquipments, ...uniqueEquipments]));

    // Return the final array of objects with 'name' instead of 'Equipment'
    return allEquipment.map(name => ({ name, selected: false }));
  }

  public closeEquipment(value) {
    if (value === "cancel") {
      this.isEquipment = false;
      this.revertToLastConfirmed();
      this.resetSearchAndFilter();
    }
  }
  revertToLastConfirmed() {
    if (this.lastConfirmedSelection.length) {
      this.equipment = JSON.parse(JSON.stringify(this.lastConfirmedSelection)); // Revert to the confirmed state
      this.filteredEquipment = [...this.equipment]; // Reset the filtered list too
      this.updateSelectAll();
    } else {
      this.equipment.forEach(item => item.selected = false); // Deselect all items
      this.filteredEquipment = [...this.equipment]; // Reset filtered list
      this.updateSelectAll();
    }
  }
  resetSearchAndFilter() {
    this.searchTerm = '';  // Clear the search input
    this.filteredEquipment = [...this.equipment];  // Reset to the full equipment list
    this.updateSelectAll();  // Update the select all checkbox state
  }

  filterEquipment() {
    if (this.searchTerm) {
      this.filteredEquipment = this.equipment.filter(item =>
        item.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    } else {
      this.filteredEquipment = [...this.equipment];
    }
    this.updateSelectAll();
  }
  updateSelectAll() {
    this.selectAll = this.filteredEquipment.every(item => item.selected);
  }

  toCapitalize(text: string): string {
    if (!text) return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }

  openPopupOutcomeSummary(outcomeSummary: string): void {
    this.popupOutcomeContent = outcomeSummary;
    this.showPopupOutcome = true;
  }

  closePopupOutcome(value): void {
    if (value == 'cancel') {
      this.showPopupOutcome = false;
    }
  }

  undoLastSummary() {
    if (this.addedSummaries.length > 0) {
      const lastAdded = this.addedSummaries.pop();
      this.outcomeSummary = this.outcomeSummary.replace(lastAdded, '');
    }
  }

  filterIMDataDrivenTaskCompleted(selectedWidgets) {
    let statusValues = selectedWidgets
      .filter(item => item.isSelected)
      .map(item => item.label);

    let equipmentFilters = [];
    if (this.allSelectedEquipments?.length > 0) {
      equipmentFilters = this.allSelectedEquipments?.map(equipment => ({
        field: 'Equipment',
        operator: 'eq',
        value: equipment.name
      }));
    }

    const filters = [];

    // Add WS_Response_String filters if there are any
    if (statusValues.length > 0) {
      // let val: string = '';
      // statusValues.forEach(value => {if (value === 'OPEN IM TASKS') { val = value }})
      // if (val === 'OPEN IM TASKS' ) {
      //   filters.push ({
      //     logic: 'or',
      //     fiters: statusValues.map(value => ({
      //       field: 'IsServiceCallOpen',
      //       operator: 'eq',
      //       value: value
      //     }))
      //   })
      // } else {
        filters.push({
          logic: 'or', // Logic for WS_Response_String filters
          filters: statusValues.map(value => ({
            field: 'WS_Response_String',
            operator: 'eq',
            value: value
          }))
        });
      // }
    }

    // Add Equipment filters if there are any
    if (equipmentFilters.length > 0) {
      filters.push({
        logic: 'or', // Logic for Equipment filters
        filters: equipmentFilters
      });
    }

    // Create the final filter combining both WS_Response_String and Equipment filters
    this.IMDataDrivenTCompleteListState.filter = {
      logic: 'and', // Ensure all groups must pass
      filters: [] // Initialize an empty filters array
    };

    // Check if we have added any filters
    const isCompositeFilter = filters.length > 0;

    if (isCompositeFilter) {
      // If there are filters, push them into the main filters array
      this.IMDataDrivenTCompleteListState.filter.filters.push(...filters);
    }

    // Select the appropriate data source based on selected equipment
    const dataSource = this.allSelectedEquipments?.length > 0 ? this.getCompleteList : this.getIMDataDrivenTaskCompleteList;

    // Apply the filters to get the filtered data
    const filteredData = this.customFilter(dataSource, this.IMDataDrivenTCompleteListState.filter.filters);

    // Set grid data with the filtered results
    this.gridDataForIMCompletedList = {
      data: filteredData, // Use the filtered data
      total: filteredData.length // Set total to the length of filtered results
    };
  }


  customFilter(data: any[], filters: any[]): any[] {
    return data.filter(item => {
      const fieldValue = item['WS_Response_String']?.trim(); // Trim the field value
      const equipmentValue = item['Equipment']?.trim(); // Trim the equipment field value

      // Function to evaluate a single filter condition
      const applyFilter = (filter) => {
        if (filter.field === 'WS_Response_String' && filter.operator === 'eq') {
          console.log(`Comparing WS_Response_String: '${fieldValue}' with '${filter.value}'`); // Log for debugging
          return fieldValue === filter.value;
        }
        if (filter.field === 'Equipment' && filter.operator === 'eq') {
          console.log(`Comparing Equipment: '${equipmentValue}' with '${filter.value}'`); // Log for debugging
          return equipmentValue === filter.value;
        }
        return false; // Add other operators if needed
      };

      // Iterate through each filter and handle logic ('or' / 'and')
      return filters.every(filterGroup => {
        if (filterGroup.filters) { // Handle composite filters
          if (filterGroup.logic === 'or') {
            return filterGroup.filters.some(subFilter => applyFilter(subFilter));
          } else if (filterGroup.logic === 'and') {
            return filterGroup.filters.every(subFilter => applyFilter(subFilter));
          }
        }
        return applyFilter(filterGroup); // Handle single-level filters
      });
    });
  }

  public dataStateChangeCompleted(state: DataStateChangeEvent): void {
    this.IMDataDrivenTCompleteListState = state;
    const filters = state.filter.filters;




    if (filters.length > 0) {
      let filteredData = this.getIMDataDrivenTaskCompleteList.slice(0);

      filteredData = filteredData.map(function (obj) {

        obj.Technician_Date = extractDate(obj.Technician_Summary);
        function extractDate(summary: string): string {
          const datePattern = /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{4}\b/;
          const match = summary?.match(datePattern);
          if (match) {
            const date = new Date(match[0]);
            return formatDate(date); // Format date to "YYYY-MM-DDTHH:mm:ss"
          }
          return ""; // Return empty string if no date found
        }

        function formatDate(date: Date): string {
          const year = date.getFullYear();
          const month = padZero(date.getMonth() + 1); // Month is zero-indexed
          const day = padZero(date.getDate());
          const hours = padZero(date.getHours());
          const minutes = padZero(date.getMinutes());
          const seconds = padZero(date.getSeconds());
          return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
        }

        function padZero(num: number): string {
          return num < 10 ? '0' + num : num.toString();
        }

        return obj;
      });

      this.IMDataDrivenTCompleteListState.sort = [{ dir: "desc", field: "Technician_Date" }];


      // Function to apply individual filters
      const applyFilter = (item: any, filter: FilterDescriptor): boolean => {
        const filterValue = filter.value.toString().toLowerCase();

        if (filter.field === "EnergyWasteCostAvoidanceKWH") {
          const kwhValue = item.EnergyWasteCostAvoidanceKWH !== null && item.EnergyWasteCostAvoidanceKWH !== undefined
            ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase()
            : '';
          const dollarValue = item.EnergyWasteCostAvoidance$ !== null && item.EnergyWasteCostAvoidance$ !== undefined
            ? item.EnergyWasteCostAvoidance$.toString().toLowerCase()
            : '';
          return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
        } else if (typeof filter.field === 'string') {
          const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined
            ? item[filter.field].toString().toLowerCase()
            : '';
          // return fieldValue.includes(filterValue);

          if (filter.field === 'Equipment') {
            return fieldValue === filterValue; // Exact match for Equipment
          } else {
            return fieldValue.includes(filterValue); // Partial match for other fields
          }
        }
        return true; // Default true for unsupported fields
      };

      // Recursive function to apply filters based on logic
      const applyFiltersRecursively = (item: any, filterGroup: CompositeFilterDescriptor | FilterDescriptor): boolean => {
        if ('filters' in filterGroup) { // It's a CompositeFilterDescriptor
          const group = filterGroup as CompositeFilterDescriptor;

          if (group.logic === 'and') {
            return group.filters.every(subFilter => applyFiltersRecursively(item, subFilter));
          } else if (group.logic === 'or') {
            return group.filters.some(subFilter => applyFiltersRecursively(item, subFilter));
          }
        } else {
          return applyFilter(item, filterGroup as FilterDescriptor);
        }
        return true; // Default true if logic is unsupported
      };

      // Apply all filters recursively
      filteredData = filteredData.filter(item => applyFiltersRecursively(item, { filters, logic: state.filter.logic } as CompositeFilterDescriptor));

      // Process the filtered data for the grid
      this.gridDataForIMCompletedList = process(filteredData, this.IMDataDrivenTCompleteListState);
    } else {
      // No filters applied, use the original data
      this.gridDataForIMCompletedList = process(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState);
    }
  }

  clearIMDataDrivenFilter() {
    if (this.allSelectedEquipments?.length > 0) {
      this.FilterIMDataDrivenTableOnEquipment(this.allSelectedEquipments);
    } else {
      this.IMDataDrivenTCompleteListState = {
        skip: 0,
        filter: {
          logic: 'and',
          filters: []
        },
        sort: [{ dir: "desc", field: "Technician_Date" }]
      };
      this.gridDataForIMCompletedList = process(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState);
    }

  }

  public dataStateChangeTuning(state: DataStateChangeEvent): void {
    this.IMDataDrivenTTuningListState = state;
    const filters = state.filter.filters;

    if (filters.length > 0) {
      let filteredData = this.getIMDataDrivenTaskTuningList.slice(0);

      filteredData = filteredData.map(function (obj) {

        obj.Technician_Date = extractDate(obj.Technician_Summary);
        function extractDate(summary: string): string {
          const datePattern = /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}\s+\d{4}\b/;
          const match = summary?.match(datePattern);
          if (match) {
            const date = new Date(match[0]);
            return formatDate(date);
          }
          return "";
        }

        function formatDate(date: Date): string {
          const year = date.getFullYear();
          const month = padZero(date.getMonth() + 1);
          const day = padZero(date.getDate());
          const hours = padZero(date.getHours());
          const minutes = padZero(date.getMinutes());
          const seconds = padZero(date.getSeconds());
          return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
        }

        function padZero(num: number): string {
          return num < 10 ? '0' + num : num.toString();
        }

        return obj;
      });

      this.IMDataDrivenTTuningListState.sort = [{ dir: "desc", field: "Technician_Date" }];

      const applyFilter = (item: any, filter: FilterDescriptor): boolean => {
        const filterValue = filter.value.toString().toLowerCase();

        if (filter.field === "EnergyWasteCostAvoidanceKWH") {
          const kwhValue = item.EnergyWasteCostAvoidanceKWH !== null && item.EnergyWasteCostAvoidanceKWH !== undefined
            ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase()
            : '';
          const dollarValue = item.EnergyWasteCostAvoidance$ !== null && item.EnergyWasteCostAvoidance$ !== undefined
            ? item.EnergyWasteCostAvoidance$.toString().toLowerCase()
            : '';
          return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
        } else if (typeof filter.field === 'string') {
          const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined
            ? item[filter.field].toString().toLowerCase()
            : '';

          if (filter.field === 'Equipment') {
            return fieldValue === filterValue;
          } else {
            return fieldValue.includes(filterValue);
          }
        }
        return true;
      };

      const applyFiltersRecursively = (item: any, filterGroup: CompositeFilterDescriptor | FilterDescriptor): boolean => {
        if ('filters' in filterGroup) {
          const group = filterGroup as CompositeFilterDescriptor;

          if (group.logic === 'and') {
            return group.filters.every(subFilter => applyFiltersRecursively(item, subFilter));
          } else if (group.logic === 'or') {
            return group.filters.some(subFilter => applyFiltersRecursively(item, subFilter));
          }
        } else {
          return applyFilter(item, filterGroup as FilterDescriptor);
        }
        return true;
      };

      filteredData = filteredData.filter(item => applyFiltersRecursively(item, { filters, logic: state.filter.logic } as CompositeFilterDescriptor));

      this.gridDataForIMTuningList = process(filteredData, this.IMDataDrivenTTuningListState);
    } else {
      this.gridDataForIMTuningList = process(this.getIMDataDrivenTaskTuningList, this.IMDataDrivenTTuningListState);
    }
  }

  clearIMDataDrivenTuningFilter() {
    if (this.allSelectedEquipments?.length > 0) {
      this.FilterIMDataDrivenTuningTableOnEquipment(this.allSelectedEquipments);
    } else {
      this.IMDataDrivenTTuningListState = {
        skip: 0,
        filter: {
          logic: 'and',
          filters: []
        },
        sort: [{ dir: "desc", field: "Technician_Date" }]
      };
      this.gridDataForIMTuningList = process(this.getIMDataDrivenTaskTuningList, this.IMDataDrivenTTuningListState);
    }

  }

  FilterIMDataDrivenTuningTableOnEquipment(equipment) {
    let equipments = equipment;

    this.IMDataDrivenTTuningListState.filter = {
      logic: 'and',
      filters: [
        {
          logic: 'or',
          filters: equipments.map(value => ({
            field: 'Equipment',
            operator: 'eq',
            value: value.name
          }))
        }
      ]
    };
    this.gridDataForIMTuningList = {
      data: this.customFilterOnEquipment(this.getIMDataDrivenTaskTuningList, this.IMDataDrivenTTuningListState.filter.filters),
      total: this.getIMDataDrivenTaskTuningList.length
    };
  }

  // createEquipmentRunningTooHotCold(){
  //   let data = [{
  //     name: 'Too Cold',
  //     data: [5, 3, 4, 7, 2, 1, 4],
  //     color: '#86c1ec',
  //     borderColor: '#0f9ed5',
  //     borderWidth: 2
  //   }, {
  //     name: 'Too Hot',
  //     data: [0, 2, 3, 2, 1, 3, 3],
  //     color: '#f3b19d',
  //     borderColor: '#e97132',
  //     borderWidth: 2
  //   }]
  //   let categories = ['South PAC-05', 'South AHU-26', 'North AHU-15', 'North AHU-20', 'North East AHU 3-10-A', 'South PAC-04', 'North AHU-17']
  //   const chart = Highcharts.chart('container3', {
  //     chart: {
  //       type: 'bar',
  //       marginLeft: 150,
  //       backgroundColor: '#F8F8F8',
  //       scrollablePlotArea: {
  //         minHeight: categories.length * 50,
  //         opacity: 1
  //       },
  //       height: 400
  //     },

  //     title: {
  //       text: ''
  //     },
  //     subtitle: {

  //     },
  //     xAxis: {
  //       type: 'category',
  //       categories: categories,
  //       title: {
  //         text: ''
  //       },
  //       tickLength: 0
  //     },
  //     yAxis: {
  //       title: false,
  //       allowDecimals: false,
  //     },
  //     plotOptions: {
  //       bar: {
  //         stacking: 'normal',
  //         groupPadding: 0.1,
  //         pointPadding: 0,
  //         dataLabels: {
  //           enabled: true,
  //           formatter: function () {
  //             if (this.y > 0) {
  //               return `${this.series.name}`;
  //             }
  //             return '';
  //           },
  //           style: {
  //               fontWeight: 'bold',
  //               color: 'black',
  //               font: '13px "Roboto" !important'
  //           },
  //           css: {
  //             'font': '13px "Roboto" !important',
  //             'color': 'black'
  //           }
  //        }
  //       }
  //     },
  //     legend: {
  //       enabled: false
  //     },
  //     credits: {
  //       enabled: false
  //     },
  //     series: data
  //   } as any);
  // }

  filterTaskTypeData(selectedWidgets) {
    // const selectedLabels = selectedWidgets
    //   .filter(label => label.isSelected)
    //   .map(label => label.label);

    //  let data = this.getIMDataDrivenTaskCompleteList;

    let data = (this.allSelectedEquipments?.length > 0) ? this.getTaskTpeOnEquipments : this.allTaskTypeData;
    const responseStrings = data
      .filter(item =>
        selectedWidgets.some(widget => widget.label.trim() === item.Ws_Response_string?.trim())
      )
      .map(item => item.Equipment.trim());


    const uniqueResponseStrings = Array.from(new Set(responseStrings));
    const formattedResponseStrings = uniqueResponseStrings.map(response => ({
      label: response,
      isSelected: true
    }));

    const selectedLabels = formattedResponseStrings
      .filter(label => label.isSelected)
      .map(label => label.label);
    const selectedLabelsSet = new Set(selectedLabels);
    const filteredData = data.filter(item => {
      const trimmedResponseString = item.Equipment?.trim();
      return selectedLabelsSet.has(trimmedResponseString);
    });

    const analyticRuleMap = filteredData.reduce((acc, item) => {
      if (!acc[item.AnalyticRule]) {
        acc[item.AnalyticRule] = [];
      }
      acc[item.AnalyticRule].push(item);
      return acc;
    }, {} as { [key: string]: typeof filteredData });

    const result = ([] as typeof filteredData).concat(...Object.values(analyticRuleMap));

    const grouped = result.reduce((acc, curr) => {
      const { AnalyticRule, AnalyticRuleCount } = curr;
      if (!acc[AnalyticRule]) {
        acc[AnalyticRule] = 0;
      }
      acc[AnalyticRule] += AnalyticRuleCount;
      return acc;
    }, {} as { [key: string]: number });

    let groupedData = Object.entries(grouped);

    this.createTaskTypeChart(groupedData);
  }

  clearTaskTypeFilter() {
    // let data = this.allTaskTypeData;
    let data = (this.allSelectedEquipments?.length > 0) ? this.getTaskTpeOnEquipments : this.allTaskTypeData;
    const grouped = data.reduce((acc, curr) => {
      const { AnalyticRule, AnalyticRuleCount } = curr;
      if (!acc[AnalyticRule]) {
        acc[AnalyticRule] = 0;
      }
      acc[AnalyticRule] += AnalyticRuleCount;
      return acc;
    }, {} as { [key: string]: number });

    let groupedData = Object.entries(grouped);
    if (groupedData.length > 0) {
      this.createTaskTypeChart(groupedData);
    }
  }


  AwaitingApprovalCount: any;
  allSelectedEquipments: any;
  getCompleteList: any;
  getDataOnEquipmentFilter(event, fromSelected?: boolean) {
    this.clearWidgets();
    let equipments = event;
    this.allSelectedEquipments = event;
    this.getCompleteList = [];
    this.getAwaitingListCount = [];

    let data = this.getIMDataDrivenTaskCompleteList;
    const filterDataOnEquipment = data.filter(item1 => equipments.some(item2 => item2.name === item1.Equipment.trim()));
    // const filterEquipmentThermalComfortEquipment = this.EquipmentThermalComfortdata.filter(item1 => equipments.some(item2 => item2.Equipment === item1.Equipment.trim()));
    let getHighestDexIDRecords;
    if (equipments.length > 0) {
      getHighestDexIDRecords = this.getHighestDEXIDRecords(filterDataOnEquipment);
    } else {
      getHighestDexIDRecords = this.getHighestDEXIDRecords(data);
    }
    this.getCompleteList = getHighestDexIDRecords;


    let awaitingApprovalData = this.getIMDataDrivenTAwaitingApprovalList;
    const awaitingApprvalfilterDataOnEquipment = awaitingApprovalData.filter(item1 => equipments.some(item2 => item2.name === item1.Equipment.trim()));
    let getAwaitingApprovalHighestDexIDRecords;
    if (equipments.length > 0) {
      getAwaitingApprovalHighestDexIDRecords = this.getHighestDEXIDRecords(awaitingApprvalfilterDataOnEquipment);
    } else {
      getAwaitingApprovalHighestDexIDRecords = this.getHighestDEXIDRecords(awaitingApprovalData);
    }

    let tuningData = this.getIMDataDrivenTaskTuningList;
    const tuningFilterDataOnEquipment = tuningData.filter(item1 => equipments.some(item2 => item2.name === item1.Equipment.trim()));
    let getTuningHighestDexIDRecords;
    if(equipments.length > 0) {
      getTuningHighestDexIDRecords = this.getHighestDEXIDRecords(tuningFilterDataOnEquipment);
    } else {
      getTuningHighestDexIDRecords = this.getHighestDEXIDRecords(tuningData);
    }


    // for avoided carbon emission
    const filterValuesForAvoidCarbon = ['RESOLVED', 'WON', 'RESOLVED ON PREV IM TASK'];
    const filteredDataForAvoidedCarbon = getHighestDexIDRecords.filter(item => {
      const responseString = item.WS_Response_String.trim();
      return filterValuesForAvoidCarbon.includes(responseString);
    });
    const totalEnergyWasteCostAvoidanceKWH = filteredDataForAvoidedCarbon.reduce((sum, item) => sum + item.EnergyWasteCostAvoidanceKWH, 0);

    if (this.Co2ReductionUnit == "kg CO2") {
      this.Co2Reduction = totalEnergyWasteCostAvoidanceKWH / 0.934;
    } else {
      let calculateUnitData = totalEnergyWasteCostAvoidanceKWH / 0.934
      this.Co2Reduction = calculateUnitData / 1000;
    }


    // calculate AATotalUnrealisedEnergyWaste
    const AATotalUnrealisedEnergyWaste = getAwaitingApprovalHighestDexIDRecords.reduce((sum, item) => sum + item.EnergyWasteCostAvoidance$, 0);
    console.log(AATotalUnrealisedEnergyWaste);
    this.AATotalUnrealisedEnergyWaste = AATotalUnrealisedEnergyWaste;


    // calculate AATotalUnrealisedEnergyWasteOutstanding_kWh
    const AATotalUnrealisedEnergyWasteOutstanding_kWh = getAwaitingApprovalHighestDexIDRecords.reduce((sum, item) => sum + item.EnergyWasteCostAvoidanceKWH, 0);
    console.log(AATotalUnrealisedEnergyWasteOutstanding_kWh);
    this.AATotalUnrealisedEnergyWasteAvoidanceKWH = AATotalUnrealisedEnergyWasteOutstanding_kWh;


    // calculate UnrealisedEnergyWasteCostAvoidance$
    let filterValueUnrealisedEnergyWasteCostAvoidance$ = ['FURTHER CONSIDERATION REQ.', 'QUOTE REQUIRED', 'NO ACCESS', 'AWAITING APPROVAL', 'EXPIRED', 'LOST', 'CAUSED BY PLANT CONFIG/DESIGN/USE', 'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE', 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR', 'EQUIPMENT DE-COMMISSIONED'];
    const filteredDataForCostAvoidance$ = getHighestDexIDRecords.filter(item => {
      const responseString = item.WS_Response_String.trim();
      return filterValueUnrealisedEnergyWasteCostAvoidance$.includes(responseString);
    });
    const UnrealisedEnergyWasteCostAvoidance$ = filteredDataForCostAvoidance$.reduce((sum, item) => sum + item.EnergyWasteCostAvoidance$, 0);


    // calculate UnrealisedEnergyWasteCostAvoidanceKWH
    let filterValueUnrealisedEnergyWasteCostAvoidanceKWH = ['FURTHER CONSIDERATION REQ.', 'QUOTE REQUIRED', 'NO ACCESS', 'AWAITING APPROVAL', 'EXPIRED', 'LOST', 'CAUSED BY PLANT CONFIG/DESIGN/USE', 'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE', 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR', 'EQUIPMENT DE-COMMISSIONED'];
    const filteredDataForCostAvoidanceKWH = getHighestDexIDRecords.filter(item => {
      const responseString = item.WS_Response_String.trim();
      return filterValueUnrealisedEnergyWasteCostAvoidanceKWH.includes(responseString);
    });
    const UnrealisedEnergyWasteCostAvoidanceKWH = filteredDataForCostAvoidanceKWH.reduce((sum, item) => sum + item.EnergyWasteCostAvoidanceKWH, 0);


    // calculate TotalUnrealisedEnergyWasteOutstanding_kWh
    const TotalUnrealisedEnergyWasteOutstanding_kWh = UnrealisedEnergyWasteCostAvoidanceKWH + AATotalUnrealisedEnergyWasteOutstanding_kWh;
    this.ThermalComfortCount = TotalUnrealisedEnergyWasteOutstanding_kWh;

    // calculate TotalUnrealisedEnergyWaste
    const TotalUnrealisedEnergyWaste = UnrealisedEnergyWasteCostAvoidance$ + AATotalUnrealisedEnergyWaste;
    this.TotalUnRealisedEnergyWaste = TotalUnrealisedEnergyWaste;


    // calculate TotalRealisedEnergyWaste
    const filterValuesForTotalRealisedEnergyWaste = ['RESOLVED', 'WON', 'RESOLVED ON PREV IM TASK'];
    const filteredDataForRealisedEnergyWaste = getHighestDexIDRecords.filter(item => {
      const responseString = item.WS_Response_String.trim();
      return filterValuesForTotalRealisedEnergyWaste.includes(responseString);
    });
    const TotalRealisedEnergyWaste = filteredDataForRealisedEnergyWaste.reduce((sum, item) => sum + item.EnergyWasteCostAvoidance$, 0);
    this.TotalRealisedEnergyWaste = TotalRealisedEnergyWaste;


    // calculate TotalRealisedEnergyWaste_KWH
    const filterValuesForTotalRealisedEnergyWaste_KWH = ['RESOLVED', 'WON', 'RESOLVED ON PREV IM TASK'];
    const filteredDataForRealisedEnergyWaste_KWH = getHighestDexIDRecords.filter(item => {
      const responseString = item.WS_Response_String.trim();
      return filterValuesForTotalRealisedEnergyWaste_KWH.includes(responseString);
    });
    const TotalRealisedEnergyWaste_KWH = filteredDataForRealisedEnergyWaste_KWH.reduce((sum, item) => sum + item.EnergyWasteCostAvoidanceKWH, 0);
    this.EquipmentReliabilityCount = TotalRealisedEnergyWaste_KWH;

    if (equipments.length > 0) {
      this.FilterIMDataDrivenTableOnEquipment(equipments);
      this.FilterIMDataDrivenAwaitingTableOnEquipment(equipments);
      this.FilterIMDataDrivenTuningTableOnEquipment(equipments);
    } else {
      this.clearIMDataDrivenFilter();
      this.clearIMDataDrivenAwaitingFilter();
      this.clearIMDataDrivenTuningFilter();
    }


    // this.clearTaskTypeFilter();

    if (equipments.length > 0) {
      this.filterTaskTypeDataOnEquipment(filterDataOnEquipment);
      // if (this.EquipmentFiltered && !fromSelected) {
      //   this.createEquipmentThermalGraph(filterEquipmentThermalComfortEquipment);
      // }

    } else {
      this.filterTaskTypeDataOnEquipment(data);
    }
    // if (this.isEquipment == true && this.EquipmentFiltered == false) {
    //   this.createEquipmentThermalGraph(this.EquipmentThermalComfortdata);
    // }

    // this.filterDataOnEquipmentForSummaryBar(getHighestDexIDRecords);
    this.filterDataOnEquipmentForTaskSummary(getHighestDexIDRecords);
    this.calculateDataForOutcomes(getHighestDexIDRecords, getAwaitingApprovalHighestDexIDRecords);

    this.AwaitingApprovalCount = 0;
    this.AwaitingApprovalCount = getAwaitingApprovalHighestDexIDRecords.length;

    if (equipments.length > 0) {
      this.getAwaitingListCount = awaitingApprvalfilterDataOnEquipment;
    } else {
      this.getAwaitingListCount = awaitingApprovalData;
    }

    if (equipments.length > 0) {
      this.FilterIMDataDrivenBacklogTableOnEquipment(equipments);
    } else {
      this.clearIMDataDrivenBacklogFilter();
    }

    let backlogData = this.getIMDataDrivenTaskBacklogList;
    const backlogfilterDataOnEquipment = backlogData.filter(item1 => equipments.some(item2 => item2.name === item1.Equipment.trim()));
    if (equipments.length > 0) {
      this.calculateBacklogValues(backlogfilterDataOnEquipment);
      this.calculateBacklogPieOnEquipment(backlogfilterDataOnEquipment);
    } else {
      this.calculateBacklogValues(backlogData);
      this.calculateBacklogPieOnEquipment(backlogData);
    }
    this.calculateProposedIMTaskTypeOnEquipment(getAwaitingApprovalHighestDexIDRecords);
  }

  FilterIMDataDrivenTableOnEquipment(equipment) {
    let equipments = equipment;

    this.IMDataDrivenTCompleteListState.filter = {
      logic: 'and',
      filters: [
        {
          logic: 'or',
          filters: equipments.map(value => ({
            field: 'Equipment',
            operator: 'eq',
            value: value.name
          }))
        }
      ]
    };
    this.gridDataForIMCompletedList = {
      data: this.customFilterOnEquipment(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState.filter.filters),
      total: this.getIMDataDrivenTaskCompleteList.length
    };
  }

  customFilterOnEquipment(data: any[], filters: any[]): any[] {
    return data.filter(item => {
      const fieldValue = item['Equipment']?.trim();
      const applyFilter = (filter) => {
        if (filter.field === 'Equipment' && filter.operator === 'eq') {
          return fieldValue === filter.value;
        }
        return false;
      };
      return filters.some(filterGroup => {
        if (filterGroup.filters) {
          if (filterGroup.logic === 'or') {
            return filterGroup.filters.some(subFilter => applyFilter(subFilter));
          } else if (filterGroup.logic === 'and') {
            return filterGroup.filters.every(subFilter => applyFilter(subFilter));
          }
        }
        return applyFilter(filterGroup);
      });
    });
  }

  @ViewChild(HeaderComponent) headerComponent!: HeaderComponent;

  ngOnDestroy(): void {
    this.headerComponent.clearEquipment();
    localStorage.removeItem("CompleteList");
    localStorage.removeItem("IsIMDataUpdated");
    localStorage.removeItem("AwaitingList");
    localStorage.removeItem("BacklogList");
  }


  FilterIMDataDrivenAwaitingTableOnEquipment(equipment) {
    let equipments = equipment;

    this.IMDataDrivenTAwaitingListState.filter = {
      logic: 'and',
      filters: [
        {
          logic: 'or',
          filters: equipments.map(value => ({
            field: 'Equipment',
            operator: 'eq',
            value: value.name
          }))
        }
      ]
    };
    this.gridDataForIMAwaitingList = {
      data: this.customFilterOnEquipment(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState.filter.filters),
      total: this.getIMDataDrivenTAwaitingApprovalList.length
    };
  }

  clearIMDataDrivenAwaitingFilter() {
    this.IMDataDrivenTAwaitingListState = {
      skip: 0,
      filter: {
        logic: 'and',
        filters: []
      },
      sort: [{ dir: "desc", field: "DATE" }]
    };
    this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);
  }


  public dataStateChangeAwaiting(state: DataStateChangeEvent): void {
    this.IMDataDrivenTAwaitingListState = state;
    const filters = state.filter.filters;

    if (filters.length > 0) {
      let filteredData = this.getIMDataDrivenTAwaitingApprovalList.slice(0);

      // Function to apply individual filters
      const applyFilter = (item: any, filter: FilterDescriptor): boolean => {
        const filterValue = filter.value.toString().toLowerCase();

        if (filter.field === "EnergyWasteCostAvoidanceKWH") {
          const kwhValue = item.EnergyWasteCostAvoidanceKWH !== null && item.EnergyWasteCostAvoidanceKWH !== undefined
            ? item.EnergyWasteCostAvoidanceKWH.toString().toLowerCase()
            : '';
          const dollarValue = item.EnergyWasteCostAvoidance$ !== null && item.EnergyWasteCostAvoidance$ !== undefined
            ? item.EnergyWasteCostAvoidance$.toString().toLowerCase()
            : '';
          return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
        } else if (typeof filter.field === 'string') {
          const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined
            ? item[filter.field].toString().toLowerCase()
            : '';
          // return fieldValue.includes(filterValue);

          if (filter.field === 'Equipment') {
            return fieldValue === filterValue; // Exact match for Equipment
          } else {
            return fieldValue.includes(filterValue); // Partial match for other fields
          }
        }
        return true; // Default true for unsupported fields
      };

      // Recursive function to apply filters based on logic
      const applyFiltersRecursively = (item: any, filterGroup: CompositeFilterDescriptor | FilterDescriptor): boolean => {
        if ('filters' in filterGroup) { // It's a CompositeFilterDescriptor
          const group = filterGroup as CompositeFilterDescriptor;

          if (group.logic === 'and') {
            return group.filters.every(subFilter => applyFiltersRecursively(item, subFilter));
          } else if (group.logic === 'or') {
            return group.filters.some(subFilter => applyFiltersRecursively(item, subFilter));
          }
        } else {
          return applyFilter(item, filterGroup as FilterDescriptor);
        }
        return true; // Default true if logic is unsupported
      };

      // Apply all filters recursively
      filteredData = filteredData.filter(item => applyFiltersRecursively(item, { filters, logic: state.filter.logic } as CompositeFilterDescriptor));

      // Process the filtered data for the grid
      this.gridDataForIMAwaitingList = process(filteredData, {});
    } else {
      // No filters applied, use the original data
      this.gridDataForIMAwaitingList = process(this.getIMDataDrivenTAwaitingApprovalList, this.IMDataDrivenTAwaitingListState);
    }
  }

  dataStateEquipmentComfort(state: DataStateChangeEvent) {
    this.EquipComfortState = state;
    this.equipComfortGrid = process(this.EquipmentThermalComfortdata, this.EquipComfortState);
  }
  filterDataOnEquipmentForSummaryBar() {
    // let data = completedData;

    // const thermalComfort = data.filter(item =>
    //   item.EnergySavingPerDay <= 10 && item.ThermalComfort >= item.EquipmentReliability
    // );
    // const equipmentReliablity = data.filter(item =>
    //   item.EnergySavingPerDay <= 10 && item.EquipmentReliability > item.ThermalComfort
    // );
    // const energy = data.filter(item =>
    //   item.EnergySavingPerDay >= 11
    // );

    // let DataForCompleteAction = [];
    // DataForCompleteAction = [{
    //   name: 'Equipment Reliablity', value: equipmentReliablity.length
    // }, {
    //   name: 'Thermal Comfort', value: thermalComfort.length
    // }, {
    //   name: 'Energy', value: energy.length
    // }];
    // this.TotalProblemAddressedData = DataForCompleteAction;

    let data = JSON.parse(localStorage.getItem('getOutcomeData'));
    if (data != null && data != undefined) {
      const totals = data.reduce((acc, curr) => {
        acc.totalEnergy += curr.Energy || 0;
        acc.totalThermal += curr.ThermalCount || 0;
        acc.totalReliability += curr.EquipmentReliability || 0;
        return acc;
      }, { totalEnergy: 0, totalThermal: 0, totalReliability: 0 });

      let DataForCompleteAction = [];
      DataForCompleteAction = [{
        name: 'Equipment Reliablity', value: totals.totalReliability
      }, {
        name: 'Thermal Comfort', value: totals.totalThermal
      }, {
        name: 'Energy', value: totals.totalEnergy
      }];
      this.TotalProblemAddressedData = DataForCompleteAction;
    }
  }

  filterDataOnEquipmentForTaskSummary(completedData) {
    this.resolvedOutcomeDataCount = 0;
    this.improvedOutcomeDataCount = 0;
    this.quoteRequiredOutcomeDataCount = 0;
    this.escalatedOutcomeDataCount = 0;
    this.NoAccessOutcomeDataCount = 0;
    this.ExpiredOutcomeDataCount = 0;
    this.LostOutcomeDataCount = 0;
    this.WonOutcomeDataCount = 0;
    this.AwaitingApprovalDataCount = 0;
    this.ImTaskQtyDataCount = 0;
    this.FT2_Total = 0;
    let filterRecordsOnDexID = completedData

    if (filterRecordsOnDexID.length > 0 && filterRecordsOnDexID != null && filterRecordsOnDexID != undefined) {
      let serviceCallCompleteDataForResolved = filterRecordsOnDexID;
      let resolvedFilterData = serviceCallCompleteDataForResolved.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "resolved"
      })

      let ImprovedFilterData = serviceCallCompleteDataForResolved.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "improved"
      })


      if (ImprovedFilterData.length > 0 && ImprovedFilterData != undefined && ImprovedFilterData != null) {
        this.improvedOutcomeDataCount = ImprovedFilterData.length;
      } else {
        this.improvedOutcomeDataCount = 0;
      }


      let ResolvedPreIMTaskFilterData = serviceCallCompleteDataForResolved.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "resolved on prev im task"
      })

      if (resolvedFilterData.length > 0 && resolvedFilterData != undefined && resolvedFilterData != null) {
        this.resolvedOutcomeDataCount = resolvedFilterData.length + ResolvedPreIMTaskFilterData.length;
      } else {
        this.resolvedOutcomeDataCount = 0;
      }


      let serviceCallCompleteDataForQuoteRequired = filterRecordsOnDexID;
      let quoteRequiredFilterData = serviceCallCompleteDataForQuoteRequired.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "quote required"
      })
      if (quoteRequiredFilterData.length > 0 && quoteRequiredFilterData != undefined && quoteRequiredFilterData != null) {
        this.quoteRequiredOutcomeDataCount = quoteRequiredFilterData.length;
      } else {
        this.quoteRequiredOutcomeDataCount = 0;
      }



      let serviceCallCompleteDataForEscalated = filterRecordsOnDexID;
      let escalatedFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "further consideration req."
      })

      let NoAccessFilterData = serviceCallCompleteDataForResolved.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "no access"
      })


      let causedbyPlantFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "caused by plant config/design/use"
      })

      let causedbyEquipmentFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "caused by equipment config/design/use"
      })

      let causedbyInstallationFilterData = serviceCallCompleteDataForEscalated.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "caused by installation or commissioning error"
      })
      let EQUIPMENT_DE_COMMISSIONED = serviceCallCompleteDataForEscalated.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "equipment de-commissioned"
      })

      if (escalatedFilterData.length > 0 && escalatedFilterData != undefined && escalatedFilterData != null) {
        this.escalatedOutcomeDataCount = escalatedFilterData.length + causedbyPlantFilterData.length + causedbyEquipmentFilterData.length + causedbyInstallationFilterData.length + EQUIPMENT_DE_COMMISSIONED.length;
      } else {
        this.escalatedOutcomeDataCount = 0 + causedbyPlantFilterData.length + causedbyEquipmentFilterData.length + causedbyInstallationFilterData.length + EQUIPMENT_DE_COMMISSIONED.length;
      }

      // this.ImTaskQtyDataCount = this.resolvedOutcomeDataCount + this.quoteRequiredOutcomeDataCount + this.escalatedOutcomeDataCount;


      // New Outcome Count No Access...
      let serviceCallCompleteDataForNoAccess = filterRecordsOnDexID;
      let NoAccessOutcomeData = serviceCallCompleteDataForNoAccess.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "no access"
      })
      if (NoAccessOutcomeData.length > 0 && NoAccessOutcomeData != undefined && NoAccessOutcomeData != null) {
        this.NoAccessOutcomeDataCount = NoAccessOutcomeData.length;
      } else {
        this.NoAccessOutcomeDataCount = 0;
      }

      // New Outcome Count Expired
      let serviceCallCompleteDataForExpired = filterRecordsOnDexID;
      let ExpiredOutcomeData = serviceCallCompleteDataForExpired.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "quote expired"
      })
      if (ExpiredOutcomeData.length > 0 && ExpiredOutcomeData != undefined && ExpiredOutcomeData != null) {
        this.ExpiredOutcomeDataCount = ExpiredOutcomeData.length;
      } else {
        this.ExpiredOutcomeDataCount = 0;
      }

      // New Outcome Count Lost
      let serviceCallCompleteDataForLost = filterRecordsOnDexID;
      let LostOutcomeData = serviceCallCompleteDataForLost.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "quote not accepted"
      })
      if (LostOutcomeData.length > 0 && LostOutcomeData != undefined && LostOutcomeData != null) {
        this.LostOutcomeDataCount = LostOutcomeData.length;
      } else {
        this.LostOutcomeDataCount = 0;
      }


      // New Outcome Count Won
      let serviceCallCompleteDataForWon = filterRecordsOnDexID;
      let WonOutcomeData = serviceCallCompleteDataForWon.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "quote accepted"
      })
      if (WonOutcomeData.length > 0 && WonOutcomeData != undefined && WonOutcomeData != null) {
        this.WonOutcomeDataCount = WonOutcomeData.length;
      } else {
        this.WonOutcomeDataCount = 0;
      }

      // New Outcome Count Awaiting Approval

      let serviceCallCompleteDataForAwaitingApproval = filterRecordsOnDexID;
      let AwaitingApprovalOutcomeData = serviceCallCompleteDataForAwaitingApproval.filter((res) => {
        return res.WS_Response_String.trim().toLowerCase() === "awaiting approval"
      })
      if (AwaitingApprovalOutcomeData.length > 0 && AwaitingApprovalOutcomeData != undefined && AwaitingApprovalOutcomeData != null) {
        this.AwaitingApprovalDataCount = AwaitingApprovalOutcomeData.length;
      } else {
        this.AwaitingApprovalDataCount = 0;
      }

      this.ImTaskQtyDataCount = this.resolvedOutcomeDataCount + this.improvedOutcomeDataCount + this.quoteRequiredOutcomeDataCount + this.escalatedOutcomeDataCount + this.NoAccessOutcomeDataCount + this.AwaitingApprovalDataCount + this.ExpiredOutcomeDataCount + this.WonOutcomeDataCount + this.LostOutcomeDataCount;

      let serviceCallCompleteDataForOpenTask = filterRecordsOnDexID;
      let openTaskFilterData = serviceCallCompleteDataForOpenTask.filter((res) => {
        return res.IsServiceCallOpen === 1;
      });

      if (openTaskFilterData.length > 0 && openTaskFilterData != undefined && openTaskFilterData != null) {
        this.FT2_Total = openTaskFilterData.length;
      } else {
        this.FT2_Total = 0;
      }
    }
  }

  calculateDataForOutcomes(completedData, awaitingData) {
    let selectedCompleteData = completedData;
    const result: { [key: string]: any } = {};
    let outcomeData = [];

    const realisedOutcomes = ['RESOLVED', 'WON', 'RESOLVED ON PREV IM TASK'];
    const unrealisedOutcomes = [
      'FURTHER CONSIDERATION REQ.', 'QUOTE REQUIRED', 'NO ACCESS', 'AWAITING APPROVAL',
      'EXPIRED', 'LOST', 'CAUSED BY PLANT CONFIG/DESIGN/USE',
      'CAUSED BY EQUIPMENT CONFIG/DESIGN/USE', 'CAUSED BY INSTALLATION OR COMMISSIONING ERROR', 'EQUIPMENT DE-COMMISSIONED'
    ];

    selectedCompleteData.forEach((item) => {
      const outcome = item.WS_Response_String.trim();
      if (!result[outcome]) {
        result[outcome] = {
          Outcome: outcome,
          Total_RealisedEnergyWasteCostAvoidanceKWH: 0,
          Total_RealisedEnergyWasteCostAvoidance: 0,
          Total_UnrealisedEnergyWasteCostAvoidanceKWH: 0,
          Total_UnrealisedEnergyWasteCostAvoidance: 0,
          ThermalCount: 0,
          EquipmentReliability: 0,
          Energy: 0
        };
      }
      if (realisedOutcomes.includes(outcome)) {
        result[outcome].Total_RealisedEnergyWasteCostAvoidanceKWH += item.EnergyWasteCostAvoidanceKWH || 0;
        result[outcome].Total_RealisedEnergyWasteCostAvoidance += item.EnergyWasteCostAvoidance$ || 0;
      }
      else if (unrealisedOutcomes.includes(outcome)) {
        result[outcome].Total_UnrealisedEnergyWasteCostAvoidanceKWH += item.EnergyWasteCostAvoidanceKWH || 0;
        result[outcome].Total_UnrealisedEnergyWasteCostAvoidance += item.EnergyWasteCostAvoidance$ || 0;
      }
      if (item.EnergySavingPerDay <= 10 && item.ThermalComfort >= item.EquipmentReliability) {
        result[outcome].ThermalCount += 1;
      }
      if (item.EnergySavingPerDay <= 10 && item.EquipmentReliability > item.ThermalComfort) {
        result[outcome].EquipmentReliability += 1;
      }
      if (item.EnergySavingPerDay >= 11) {
        result[outcome].Energy += 1;
      }
    });
    outcomeData = Object.values(result);
    let awaitingResult = this.calculateOutcomeForAwaitingApproval(awaitingData)
    outcomeData.push(awaitingResult);
    localStorage.setItem('getOutcomeData', JSON.stringify(outcomeData));
    this.filterDataOnEquipmentForSummaryBar();
  }

  calculateOutcomeForAwaitingApproval(dataArray: any): any {
    let Total_UnrealisedEnergyWasteCostAvoidanceKWH = 0;
    let Total_UnrealisedEnergyWasteCostAvoidance = 0;
    let ThermalCount = 0;
    let EquipmentReliabilityCount = 0;
    let EnergyCount = 0;

    dataArray.forEach((item) => {
      Total_UnrealisedEnergyWasteCostAvoidanceKWH += item.EnergyWasteCostAvoidanceKWH;
      Total_UnrealisedEnergyWasteCostAvoidance += item.EnergyWasteCostAvoidance$;

      const { EnergySavingPerDay, ThermalComfort, EquipmentReliability } = item;

      // Calculate thermal comfort
      if (EnergySavingPerDay <= 10 && ThermalComfort >= EquipmentReliability) {
        ThermalCount++;
      }

      // Calculate energy
      if (EnergySavingPerDay >= 11) {
        EnergyCount++;
      }

      // Calculate equipment reliability
      if (EnergySavingPerDay <= 10 && EquipmentReliability > ThermalComfort) {
        EquipmentReliabilityCount++;
      }

    });
    return {
      Outcome: 'QT_AWAITING APPROVAL',
      ThermalCount: ThermalCount,
      EquipmentReliability: EquipmentReliabilityCount,
      Energy: EnergyCount,
      Total_RealisedEnergyWasteCostAvoidanceKWH: 0,
      Total_RealisedEnergyWasteCostAvoidance: 0,
      Total_UnrealisedEnergyWasteCostAvoidanceKWH: Total_UnrealisedEnergyWasteCostAvoidanceKWH,
      Total_UnrealisedEnergyWasteCostAvoidance: Total_UnrealisedEnergyWasteCostAvoidance,
    };
  }

  filterTaskTypeDataOnEquipment(completedData) {
    let completeData = completedData;
    this.getTaskTpeOnEquipments = [];

    const responseStrings = completeData.map(item => item.Equipment.trim());
    const uniqueResponseStrings = Array.from(new Set(responseStrings));
    const formattedResponseStrings = uniqueResponseStrings.map(response => ({
      label: response,
      isSelected: true
    }));

    const selectedLabels = formattedResponseStrings
      .filter(label => label.isSelected)
      .map(label => label.label);

    let data = this.allTaskTypeData;
    const selectedLabelsSet = new Set(selectedLabels);
    const filteredData = data.filter(item => {
      const trimmedResponseString = item.Equipment?.trim();
      return selectedLabelsSet.has(trimmedResponseString);
    });

    this.getTaskTpeOnEquipments = filteredData;

    const analyticRuleMap = filteredData.reduce((acc, item) => {
      if (!acc[item.AnalyticRule]) {
        acc[item.AnalyticRule] = [];
      }
      acc[item.AnalyticRule].push(item);
      return acc;
    }, {} as { [key: string]: typeof filteredData });
    const result = ([] as typeof filteredData).concat(...Object.values(analyticRuleMap));

    const grouped = result.reduce((acc, curr) => {
      const { AnalyticRule, AnalyticRuleCount } = curr;
      if (!acc[AnalyticRule]) {
        acc[AnalyticRule] = 0;
      }
      acc[AnalyticRule] += AnalyticRuleCount;
      return acc;
    }, {} as { [key: string]: number });

    let groupedData = Object.entries(grouped);
    this.createTaskTypeChart(groupedData);
  }

  filterBacklogTaskTypeDataOnEquipment(completedData) {
    let completeData = completedData;
    // this.getTaskTpeOnEquipments = [];

    const responseStrings = completeData.map(item => item.Equipment.trim());
    const uniqueResponseStrings = Array.from(new Set(responseStrings));
    const formattedResponseStrings = uniqueResponseStrings.map(response => ({
      label: response,
      isSelected: true
    }));

    const selectedLabels = formattedResponseStrings
      .filter(label => label.isSelected)
      .map(label => label.label);

    let data = this.allBacklogTaskTypeData;
    const selectedLabelsSet = new Set(selectedLabels);
    const filteredData = data.filter(item => {
      const trimmedResponseString = item.Equipment?.trim();
      return selectedLabelsSet.has(trimmedResponseString);
    });

    //  this.getTaskTpeOnEquipments = filteredData;

    const analyticRuleMap = filteredData.reduce((acc, item) => {
      if (!acc[item.AnalyticRule]) {
        acc[item.AnalyticRule] = [];
      }
      acc[item.AnalyticRule].push(item);
      return acc;
    }, {} as { [key: string]: typeof filteredData });
    const result = ([] as typeof filteredData).concat(...Object.values(analyticRuleMap));

    const grouped = result.reduce((acc, curr) => {
      const { AnalyticRule, AnalyticRuleCount } = curr;
      if (!acc[AnalyticRule]) {
        acc[AnalyticRule] = 0;
      }
      acc[AnalyticRule] += AnalyticRuleCount;
      return acc;
    }, {} as { [key: string]: number });

    let groupedData = Object.entries(grouped);
    this.createTaskBacklogChart(groupedData);
  }

  FilterIMDataDrivenBacklogTableOnEquipment(equipment) {
    let equipments = equipment;

    this.IMDataDrivenTBacklogListState.filter = {
      logic: 'and',
      filters: [
        {
          logic: 'or',
          filters: equipments.map(value => ({
            field: 'Equipment',
            operator: 'eq',
            value: value.name
          }))
        }
      ]
    };
    this.gridDataForIMBacklogList = {
      data: this.customFilterOnEquipment(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState.filter.filters),
      total: this.getIMDataDrivenTaskBacklogList.length
    };

    this.filterBacklogTaskTypeDataOnEquipment(this.gridDataForIMBacklogList.data)
  }


  public dataStateChangeBacklog(state: DataStateChangeEvent): void {
    this.IMDataDrivenTBacklogListState = state;
    const filters = state.filter.filters;

    if (filters.length > 0) {
      let filteredData = this.getIMDataDrivenTaskBacklogList.slice(0);

      // Function to apply individual filters
      const applyFilter = (item: any, filter: FilterDescriptor): boolean => {
        const filterValue = filter.value.toString().toLowerCase();

        if (filter.field === "PotentialEnergyWasteCostAvoidanceKWH") {
          const kwhValue = item.PotentialEnergyWasteCostAvoidanceKWH !== null && item.PotentialEnergyWasteCostAvoidanceKWH !== undefined
            ? item.PotentialEnergyWasteCostAvoidanceKWH.toString().toLowerCase()
            : '';
          const dollarValue = item.PotentialEnergyWasteCostAvoidance$ !== null && item.PotentialEnergyWasteCostAvoidance$ !== undefined
            ? item.PotentialEnergyWasteCostAvoidance$.toString().toLowerCase()
            : '';
          return kwhValue.includes(filterValue) || dollarValue.includes(filterValue);
        } else if (typeof filter.field === 'string') {
          const fieldValue = item[filter.field] !== null && item[filter.field] !== undefined
            ? item[filter.field].toString().toLowerCase()
            : '';
          // return fieldValue.includes(filterValue);

          if (filter.field === 'Equipment') {
            return fieldValue === filterValue; // Exact match for Equipment
          } else {
            return fieldValue.includes(filterValue); // Partial match for other fields
          }
        }
        return true; // Default true for unsupported fields
      };

      // Recursive function to apply filters based on logic
      const applyFiltersRecursively = (item: any, filterGroup: CompositeFilterDescriptor | FilterDescriptor): boolean => {
        if ('filters' in filterGroup) { // It's a CompositeFilterDescriptor
          const group = filterGroup as CompositeFilterDescriptor;

          if (group.logic === 'and') {
            return group.filters.every(subFilter => applyFiltersRecursively(item, subFilter));
          } else if (group.logic === 'or') {
            return group.filters.some(subFilter => applyFiltersRecursively(item, subFilter));
          }
        } else {
          return applyFilter(item, filterGroup as FilterDescriptor);
        }
        return true; // Default true if logic is unsupported
      };

      // Apply all filters recursively
      filteredData = filteredData.filter(item => applyFiltersRecursively(item, { filters, logic: state.filter.logic } as CompositeFilterDescriptor));

      // Process the filtered data for the grid
      this.gridDataForIMBacklogList = process(filteredData, {});
    } else {
      // No filters applied, use the original data
      this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState);
    }
  }

  clearIMDataDrivenBacklogFilter() {
    this.IMDataDrivenTBacklogListState = {
      skip: 0,
      filter: {
        logic: 'and',
        filters: []
      },
    };
    this.gridDataForIMBacklogList = process(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState);
    this.filterBacklogTaskTypeDataOnEquipment(this.gridDataForIMBacklogList.data)
  }

  calculateBacklogValues(data) {
    const UnRealisedEnergyWasteAvoidanceBacklog = data.reduce((sum, item) => sum + item.PotentialEnergyWasteCostAvoidance$, 0);
    this.UnrealisedEnergyWasteCostAvoidanceBacklog = UnRealisedEnergyWasteAvoidanceBacklog;

    const UnRealisedEnergyWasteAvoidanceKWHBacklog = data.reduce((sum, item) => sum + item.PotentialEnergyWasteCostAvoidanceKWH, 0);
    this.UnrealisedEnergyWasteCostAvoidanceKWHBacklog = UnRealisedEnergyWasteAvoidanceKWHBacklog;
  }

  calculateProposedIMTaskTypeOnEquipment(data) {
    let thermalComfortCount: number = 0;
    let equipmentReliabilityCount: number = 0;
    let energyCount: number = 0;

    thermalComfortCount = data.filter(
      item => item.EnergySavingPerDay <= 10 && item.ThermalComfort >= item.EquipmentReliability
    ).length;

    equipmentReliabilityCount = data.filter(
      item => item.EnergySavingPerDay <= 10 && item.EquipmentReliability > item.ThermalComfort
    ).length;

    energyCount = data.filter(
      item => item.EnergySavingPerDay >= 11
    ).length;

    let DataForWaitingAction = [];
    DataForWaitingAction = [{
      name: 'Equipment Reliablity', value: equipmentReliabilityCount
    }, {
      name: 'Thermal Comfort', value: thermalComfortCount
    }, {
      name: 'Energy', value: energyCount
    }];
    this.TotalProblemIdentifiedData = DataForWaitingAction;
    this.SumOfTasksProposed = this.TotalProblemIdentifiedData.reduce((sum, item) => sum + item.value, 0);
  }

  calculateBacklogPieOnEquipment(data) {
    let thermalComfortCount: number = 0;
    let equipmentReliabilityCount: number = 0;
    let energyCount: number = 0;

    thermalComfortCount = data.filter(
      item => item.EnergySavingPerDay <= 9 && item.ThermalComfort >= item.EquipmentReliability
    ).length;

    equipmentReliabilityCount = data.filter(
      item => item.EnergySavingPerDay <= 9 && item.EquipmentReliability > item.ThermalComfort
    ).length;

    energyCount = data.filter(
      item => item.EnergySavingPerDay >= 10
    ).length;

    let DataForBacklogAction = [];
    DataForBacklogAction = [{
      name: 'Equipment Reliablity', value: equipmentReliabilityCount
    }, {
      name: 'Thermal Comfort', value: thermalComfortCount
    }, {
      name: 'Energy', value: energyCount
    }];

    this.TotalTaskBacklogData = DataForBacklogAction;
    this.SumOfTasksBacklog = this.TotalTaskBacklogData.reduce((sum, item) => sum + item.value, 0);
  }

  public data = [
    {
      kind: "Low",
      share: 60,
    },
    {
      kind: "Medium",
      share: 60,
    },
    {
      kind: "High",
      share: 60,
    },
    {
      kind: "Empty",
      share: 180,
      color: "transparent",
    },
  ];

  public selectedValue: string = '';
  public selectedAngle: number = 0;

  public labelContent2(e: any): string {
    return e.dataItem.kind !== "Empty" ? e.dataItem.kind : "";
  }

  public onSeriesClick(e: SeriesClickEvent): void {
    if (e.category !== "Empty") {
      this.selectedValue = e.category;

      // Find the index of the selected slice
      const index = this.data.findIndex((item) => item.kind === e.category);

      // Calculate the cumulative share up to the start of the selected slice
      const totalShare = this.data.reduce((sum, item) => sum + item.share, 0);
      const cumulativeShare = this.data
        .slice(0, index)
        .reduce((sum, item) => sum + item.share, 0);

      // Calculate the center angle of the selected slice
      const sliceShare = this.data[index].share;
      const sliceCenterAngle =
        (cumulativeShare + sliceShare / 2) * (360 / totalShare);

      // Adjust angle to account for the chart's startAngle (0 degrees is top center)
      const adjustedAngle = sliceCenterAngle - 90;

      // Set the adjusted angle for the arrow
      this.selectedAngle = adjustedAngle;
    }
  }

  GoToCompletedTable(dataItem: any) {

    const isSameRecord = this.lastTCompletedFilteredRecord?.Equipment === dataItem?.Equipment;

    if (isSameRecord) {

      this.IMDataDrivenTCompleteListState.filter = {
        logic: 'and',
        filters: []
      };
      this.gridDataForIMCompletedList = {
        data: this.getIMDataDrivenTaskCompleteList,
        total: this.getIMDataDrivenTaskCompleteList.length
      };
      this.lastTCompletedFilteredRecord = null;
    } else {
      this.IMDataDrivenTCompleteListState.filter = {
        logic: 'and',
        filters: [
          {
            logic: 'or',
            filters: [{
              field: 'Equipment',
              operator: 'eq',
              value: dataItem?.Equipment
            }]
          }
        ]
      };
      this.gridDataForIMCompletedList = {
        data: this.customFilterOnEquipment(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState.filter.filters),
        total: this.getIMDataDrivenTaskCompleteList.length
      };
      this.lastTCompletedFilteredRecord = dataItem;
    }

    const tableElement = document.getElementById('completedTable');
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  GoToBacklogTable(dataItem: any) {

    const isSameRecord = this.lastTInBacklogFilteredRecord?.Equipment === dataItem?.Equipment;

    if (isSameRecord) {

      this.IMDataDrivenTBacklogListState.filter = {
        logic: 'and',
        filters: []
      };
      this.gridDataForIMBacklogList = {
        data: this.getIMDataDrivenTaskBacklogList,
        total: this.getIMDataDrivenTaskBacklogList.length
      };
      this.lastTInBacklogFilteredRecord = null;
    } else {
      this.IMDataDrivenTBacklogListState.filter = {
        logic: 'and',
        filters: [
          {
            logic: 'or',
            filters: [{
              field: 'Equipment',
              operator: 'eq',
              value: dataItem?.Equipment
            }]
          }
        ]
      };
      this.gridDataForIMBacklogList = {
        data: this.customFilterOnEquipment(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState.filter.filters),
        total: this.getIMDataDrivenTaskBacklogList.length
      };
      this.lastTInBacklogFilteredRecord = dataItem;
    }
    const tableElement = document.getElementById('BacklogTable');
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  chartData = [
    {
      name: 'High',
      y: 33.33,
      angle: -80,  // Angle where the arrow should point for this slice
      targetX: 80, // X coordinate relative to the chart center for arrow position
      targetY: 50, // Y coordinate relative to the chart center for arrow position
    },
    {
      name: 'Medium',
      y: 33.33,
      angle: 0,    // Angle where the arrow should point for this slice
      targetX: 160,
      targetY: 40,
    },
    {
      name: 'Low',
      y: 33.34,
      angle: 80,   // Angle where the arrow should point for this slice
      targetX: 220,
      targetY: 50,
    },
  ];

  selectedPoint: Highcharts.Point | null = null;
  // createPriorityDonutChart() {
  //   const chart = Highcharts.chart('EQREVContainer', {
  //     chart: {
  //       type: 'pie',
  //       events: {
  //         load: function () {
  //           const chart = this;
  //           const centerX = chart.plotLeft + chart.plotWidth / 2;
  //           const centerY = chart.plotTop + chart.plotHeight / 2;

  //           // Add the center title with line break and some padding
  //           chart.renderer
  //             .text('Equipment Longevity', centerX, centerY - 20) // Add padding by shifting up
  //             .css({
  //               fontSize: '16px',
  //               fontWeight: 'bold',
  //               color: 'black',
  //               textAlign: 'center',
  //             })
  //             .attr({
  //               align: 'center',
  //             })
  //             .add();

  //           // Add a black filled circle in the center
  //           chart.renderer
  //             .circle(centerX, centerY, 5) // Circle radius adjusted
  //             .attr({
  //               fill: 'black',
  //             })
  //             .add();
  //         },
  //       },
  //     },
  //     title: {
  //       text: 'Half Donut Chart with Equal Values',
  //     },
  //     plotOptions: {
  //       pie: {
  //         innerSize: '50%', // Donut effect
  //         startAngle: -90,  // Start angle for half donut
  //         endAngle: 90,     // End angle for half donut
  //         dataLabels: {
  //           enabled: true,
  //           format: '{point.name}',
  //           style: {
  //             color: 'white',
  //             fontWeight: 'bold',
  //             textOutline: 'none',
  //           },
  //           distance: -30, // Position labels inside
  //         },
  //         point: {
  //           events: {
  //             click: function () {
  //               const component = (chart.options.chart as any)._angularComponent;

  //               // Reset previously selected point
  //               if (component.selectedPoint) {
  //                 component.selectedPoint.update({
  //                   borderWidth: 0,
  //                 });
  //                 if (component.selectedPoint.arrow) {
  //                   component.selectedPoint.arrow.destroy();
  //                 }
  //               }

  //               // Highlight the selected point
  //               // this.update({
  //               //   borderWidth: 2,
  //               //   borderColor: 'black',
  //               // });
  //               component.selectedPoint = this;

  //               // Extract bounding box from `graphic`
  //               if (this.graphic) {
  //                 const bbox = this.graphic.getBBox();
  //                 const chart = this.series.chart;

  //                 // Calculate center of the chart
  //                 const centerX = chart.plotLeft + chart.plotWidth / 2;
  //                 const centerY = chart.plotTop + chart.plotHeight / 2;

  //                 // Calculate angle and position for the arrow
  //                 const sliceCenterX = bbox.x + bbox.width / 2;
  //                 const sliceCenterY = bbox.y + bbox.height / 2;
  //                 const angle = Math.atan2(sliceCenterY - centerY, sliceCenterX - centerX); // Angle in radians
  //                 const radius = 50; // Shortened arrow distance from center

  //                 const arrowX = centerX + Math.cos(angle) * radius;
  //                 const arrowY = centerY + Math.sin(angle) * radius;

  //                 // Arrowhead dimensions
  //                 const arrowHeadLength = 8; // Shortened arrowhead length
  //                 const arrowHeadWidth = 5;  // Adjusted arrowhead width

  //                 // Calculate arrowhead points
  //                 const leftX = arrowX - Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const leftY = arrowY - Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightX = arrowX + Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightY = arrowY + Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const tipX = arrowX + Math.cos(angle) * arrowHeadLength;
  //                 const tipY = arrowY + Math.sin(angle) * arrowHeadLength;

  //                 // Draw the arrow
  //                 this.arrow = chart.renderer
  //                   .path([
  //                     'M', centerX, centerY, // Start at chart center
  //                     'L', arrowX, arrowY,   // Line to arrow base
  //                     'L', leftX, leftY,     // Left side of arrowhead
  //                     'L', tipX, tipY,       // Arrow tip
  //                     'L', rightX, rightY,   // Right side of arrowhead
  //                     'L', arrowX, arrowY,   // Back to arrow base
  //                   ])
  //                   .attr({
  //                     fill: 'black',   // Fill color for arrowhead
  //                     stroke: 'black', // Border color for arrow
  //                     'stroke-width': 1,
  //                   })
  //                   .add();


  //               }
  //             },
  //           },
  //         },
  //       },
  //     },
  //     series: [
  //       {
  //         type: 'pie',
  //         name: 'Data',
  //         data: [
  //           { name: 'High', y: 33.33 },
  //           { name: 'Medium', y: 33.33 },
  //           { name: 'Low', y: 33.34 },
  //         ],
  //       },
  //     ],
  //   });

  //   // Attach Angular component reference to the chart
  //   (chart.options.chart as any)._angularComponent = this;
  // }

  // createPriorityDonutChart() {
  //   const chart = Highcharts.chart('EQREVContainer', {
  //     chart: {
  //       type: 'pie',
  //       events: {
  //         load: function () {
  //           const chart = this;
  //           const centerX = chart.plotLeft + chart.plotWidth / 2;
  //           const centerY = chart.plotTop + chart.plotHeight / 2;
  //           const chartBottom = chart.plotTop + chart.plotHeight; // Bottom of the chart area

  //           // Add the center title with line break and some padding
  //           chart.renderer
  //             .text('Equipment Longevity', centerX, chartBottom + 3) // Placed below the chart
  //             .css({
  //               fontSize: '16px',
  //               fontWeight: 'bold',
  //               color: 'black',
  //               textAlign: 'center',
  //             })
  //             .attr({
  //               align: 'center',
  //             })
  //             .add();

  //           // Add a black filled circle in the center of the donut
  //           chart.renderer
  //             .circle(centerX, centerY, 5) // Circle radius adjusted
  //             .attr({
  //               fill: 'black',
  //             })
  //             .add();
  //         },
  //       },
  //     },
  //     title: {
  //       text: "Align our technician's focus with your objectives by prioritising maintenance outcomes.",
  //     },
  //     plotOptions: {
  //       pie: {
  //         innerSize: '50%', // Donut effect
  //         startAngle: -90,  // Start angle for half donut
  //         endAngle: 90,     // End angle for half donut
  //         dataLabels: {
  //           enabled: true,
  //           format: '{point.name}',
  //           style: {
  //             color: 'white',
  //             fontWeight: 'bold',
  //             textOutline: 'none',
  //           },
  //           distance: -30, // Position labels inside
  //         },
  //         point: {
  //           events: {
  //             click: function () {
  //               const component = (chart.options.chart as any)._angularComponent;

  //               // Reset previously selected point
  //               if (component.selectedPoint) {
  //                 component.selectedPoint.update({
  //                   borderWidth: 0,
  //                 });
  //                 if (component.selectedPoint.arrow) {
  //                   component.selectedPoint.arrow.destroy();
  //                 }
  //               }

  //               // Highlight the selected point
  //               component.selectedPoint = this;

  //               // Extract bounding box from `graphic`
  //               if (this.graphic) {
  //                 const bbox = this.graphic.getBBox();
  //                 const chart = this.series.chart;

  //                 // Calculate center of the chart
  //                 const centerX = chart.plotLeft + chart.plotWidth / 2;
  //                 const centerY = chart.plotTop + chart.plotHeight / 2;

  //                 // Calculate angle and position for the arrow
  //                 const sliceCenterX = bbox.x + bbox.width / 2;
  //                 const sliceCenterY = bbox.y + bbox.height / 2;
  //                 const angle = Math.atan2(sliceCenterY - centerY, sliceCenterX - centerX); // Angle in radians
  //                 const radius = 50; // Shortened arrow distance from center

  //                 const arrowX = centerX + Math.cos(angle) * radius;
  //                 const arrowY = centerY + Math.sin(angle) * radius;

  //                 // Arrowhead dimensions
  //                 const arrowHeadLength = 8; // Shortened arrowhead length
  //                 const arrowHeadWidth = 5;  // Adjusted arrowhead width

  //                 // Calculate arrowhead points
  //                 const leftX = arrowX - Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const leftY = arrowY - Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightX = arrowX + Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightY = arrowY + Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const tipX = arrowX + Math.cos(angle) * arrowHeadLength;
  //                 const tipY = arrowY + Math.sin(angle) * arrowHeadLength;

  //                 // Draw the arrow
  //                 this.arrow = chart.renderer
  //                   .path([
  //                     'M', centerX, centerY, // Start at chart center
  //                     'L', arrowX, arrowY,   // Line to arrow base
  //                     'L', leftX, leftY,     // Left side of arrowhead
  //                     'L', tipX, tipY,       // Arrow tip
  //                     'L', rightX, rightY,   // Right side of arrowhead
  //                     'L', arrowX, arrowY,   // Back to arrow base
  //                   ])
  //                   .attr({
  //                     fill: 'black',   // Fill color for arrowhead
  //                     stroke: 'black', // Border color for arrow
  //                     'stroke-width': 1,
  //                   })
  //                   .add();
  //               }
  //             },
  //           },
  //         },
  //       },
  //     },
  //     series: [
  //       {
  //         type: 'pie',
  //         name: 'Data',
  //         data: [
  //           { name: 'High', y: 33.33 },
  //           { name: 'Medium', y: 33.33 },
  //           { name: 'Low', y: 33.34 },
  //         ],
  //       },
  //     ],
  //   });

  //   // Attach Angular component reference to the chart
  //   (chart.options.chart as any)._angularComponent = this;
  // }

  // createPriorityDonutChart() {
  //   const chart = Highcharts.chart('EQREVContainer', {
  //     chart: {
  //       type: 'pie',
  //       events: {
  //         load: function () {
  //           const chart = this;
  //           const containerWidth = chart.chartWidth; // Get the width of the container
  //           const containerHeight = chart.chartHeight; // Get the height of the container

  //           // Dynamically calculate sizes based on the container size
  //           const centerX = chart.plotLeft + chart.plotWidth / 2;
  //           const centerY = chart.plotTop + chart.plotHeight / 2;
  //           const chartBottom = chart.plotTop + chart.plotHeight; // Bottom of the chart area

  //           // Declare size factors for arrow, dot, and font size based on container size
  //           const circleRadius = containerWidth * 0.01; // Dot size based on container width
  //           const arrowSizeFactor = containerWidth * 0.05; // Arrow size based on container width
  //           const fontSizeFactor = containerWidth * 0.04; // Font size factor based on container width

  //           // Add the center title with line break and some padding
  //           chart.renderer
  //             .text('Equipment Longevity', centerX, chartBottom - 40) // Placed below the chart
  //             .css({
  //               fontSize: `${fontSizeFactor}px`, // Dynamic font size
  //               fontWeight: 'bold',
  //               color: 'black',
  //               textAlign: 'center',
  //             })
  //             .attr({
  //               align: 'center',
  //             })
  //             .add();

  //           // Add a black filled circle in the center of the donut
  //           chart.renderer
  //             .circle(centerX, centerY, circleRadius) // Circle radius adjusted based on container size
  //             .attr({
  //               fill: 'black',
  //             })
  //             .add();
  //         },
  //       },
  //     },
  //     title: {
  //       text: 'Half Donut Chart with Equal Values',
  //     },
  //     plotOptions: {
  //       pie: {
  //         innerSize: '50%', // Donut effect
  //         startAngle: -90,  // Start angle for half donut
  //         endAngle: 90,     // End angle for half donut
  //         dataLabels: {
  //           enabled: true,
  //           format: '{point.name}',
  //           style: {
  //             color: 'white',
  //             fontWeight: 'bold',
  //             textOutline: 'none',
  //           },
  //           distance: -30, // Position labels inside
  //           formatter: function () {
  //             const containerWidth = this.series.chart.chartWidth;
  //             const fontSize = containerWidth * 0.03; // Dynamically adjust font size based on container size
  //             return `<span style="font-size: ${fontSize}px">${this.point.name}</span>`;
  //           },
  //         },
  //         point: {
  //           events: {
  //             click: function () {
  //               const component = (chart.options.chart as any)._angularComponent;
  //               const containerWidth = this.series.chart.chartWidth;
  //               // Reset previously selected point
  //               if (component.selectedPoint) {
  //                 component.selectedPoint.update({
  //                   borderWidth: 0,
  //                 });
  //                 if (component.selectedPoint.arrow) {
  //                   component.selectedPoint.arrow.destroy();
  //                 }
  //               }

  //               // Highlight the selected point
  //               component.selectedPoint = this;

  //               // Extract bounding box from `graphic`
  //               if (this.graphic) {
  //                 const bbox = this.graphic.getBBox();
  //                 const chart = this.series.chart;

  //                 // Calculate center of the chart
  //                 const centerX = chart.plotLeft + chart.plotWidth / 2;
  //                 const centerY = chart.plotTop + chart.plotHeight / 2;

  //                 // Calculate angle and position for the arrow
  //                 const sliceCenterX = bbox.x + bbox.width / 2;
  //                 const sliceCenterY = bbox.y + bbox.height / 2;
  //                 const angle = Math.atan2(sliceCenterY - centerY, sliceCenterX - centerX); // Angle in radians

  //                 // **Reduce the arrow height**: Decrease the radius to shorten the arrow
  //                 const radius = containerWidth * 0.10; // Reduced arrow distance from center, now only 15%

  //                 const arrowX = centerX + Math.cos(angle) * radius;
  //                 const arrowY = centerY + Math.sin(angle) * radius;

  //                 // Arrowhead dimensions adjusted for responsiveness
  //                 const arrowHeadLength = containerWidth * 0.03; // Adjusted arrowhead length
  //                 const arrowHeadWidth = containerWidth * 0.03 * 0.5;  // Adjusted arrowhead width

  //                 // Calculate arrowhead points
  //                 const leftX = arrowX - Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const leftY = arrowY - Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightX = arrowX + Math.cos(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const rightY = arrowY + Math.sin(angle + Math.PI / 2) * arrowHeadWidth;
  //                 const tipX = arrowX + Math.cos(angle) * arrowHeadLength;
  //                 const tipY = arrowY + Math.sin(angle) * arrowHeadLength;

  //                 // Draw the arrow
  //                 this.arrow = chart.renderer
  //                   .path([
  //                     'M', centerX, centerY, // Start at chart center
  //                     'L', arrowX, arrowY,   // Line to arrow base
  //                     'L', leftX, leftY,     // Left side of arrowhead
  //                     'L', tipX, tipY,       // Arrow tip
  //                     'L', rightX, rightY,   // Right side of arrowhead
  //                     'L', arrowX, arrowY,   // Back to arrow base
  //                   ])
  //                   .attr({
  //                     fill: 'black',   // Fill color for arrowhead
  //                     stroke: 'black', // Border color for arrow
  //                     'stroke-width': 1,
  //                   })
  //                   .add();
  //               }
  //             },
  //           },
  //         },
  //       },
  //     },
  //     series: [
  //       {
  //         type: 'pie',
  //         name: 'Data',
  //         data: [
  //           { name: 'High', y: 33.33 },
  //           { name: 'Medium', y: 33.33 },
  //           { name: 'Low', y: 33.34 },
  //         ],
  //       },
  //     ],
  //   });

  //   // Attach Angular component reference to the chart
  //   (chart.options.chart as any)._angularComponent = this;
  // }


  chart: Highcharts.Chart | null = null; // Track the chart reference here







  ELSelectedIndex: number | null = null;
  TCSelectedIndex: number | null = null;
  EnergySelectedIndex: number | null = null;

  // This worked
  createEnergyDonutChart() {
    const savedIndex = localStorage.getItem('EnergyIndex');
    this.EnergySelectedIndex = savedIndex !== null && !isNaN(parseInt(savedIndex, 10))
    ? parseInt(savedIndex, 10)
    : null;

    const component = this;

    const chart = Highcharts.chart('EnergyContainer', {
      chart: {
        type: 'pie',
        events: {
          load: function () {
            const centerX = this.chartWidth / 2;
            const centerY = this.chartHeight / 2 - 20;

            this.renderer
              .text('Avoid<br>Energy Waste', centerX, centerY)
              .css({
                fontSize: '0.9vw !important',
                fontFamily: 'Roboto !important',
                fontWeight: 'bold',
                color: 'black',
                textAlign: 'center',
              })
              .attr({
                zIndex: 5,
                align: 'center',
              })
              .add();
          },
        },
      },
      title: {
        // text: "Align our technician's focus with your objectives by prioritising maintenance outcomes.",
        text: ''
      },
      tooltip: {
        enabled: false, // Disable tooltips
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        pie: {
          innerSize: '50%',
          startAngle: -90,
          endAngle: 90,
          dataLabels: {
            enabled: true,
            formatter: function () {
              const labels = ['Low', 'Medium', 'High'];
              return labels[this.point.index];
            },
            style: {
              fontSize: '0.8vw !important',
              fontFamily: 'Roboto !important',
              fontWeight: 'bold',
              color: 'white',
              textOutline: 'none' // Remove any outline for clearer text
            },
            distance: -25, // Adjust distance to center
            alignTo: 'connector', // Align labels towards the slice
            verticalAlign: 'middle', // Vertically align labels
          },
          showInLegend: false,
          point: {
            events: {
              click: function () {
                if (component.EnergySelectedIndex !== null) {
                  const prevPoint = this.series.data[component.EnergySelectedIndex];
                  const prevDataLabel = (prevPoint as any).dataLabel;

                  if (prevDataLabel) {
                    prevDataLabel.css({
                      // fontWeight: 'bold',
                      textDecoration: 'none',
                      fontSize: '0.8vw !important',
                    });
                  }
                  prevPoint.update({ sliced: false });
                }

                if (component.EnergySelectedIndex === this.index) {
                  component.EnergySelectedIndex = null;
                  return;
                } else {
                  component.EnergySelectedIndex = this.index;
                }
                //    localStorage.setItem('EnergyIndex', this.index.toString());

                const currentDataLabel = (this as any).dataLabel;

                if (currentDataLabel) {
                  currentDataLabel.css({
                    fontWeight: 'bold !important',
                    textDecoration: 'underline',
                    fontSize: '0.9vw !important',
                  });
                }

                this.update({ sliced: true });
              },
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Data',
          data: [
            { name: 'Low', y: 33.33, color: '#A1E6FF' },
            { name: 'Medium', y: 33.33, color: '#3DCDFF' },
            { name: 'High', y: 33.34, color: '#33415B' },
          ],
        },
      ],
    });

    if (this.EnergySelectedIndex !== null) {
      const point = chart.series[0].data[this.EnergySelectedIndex];
      const pointDataLabel = (point as any).dataLabel;

      if (pointDataLabel) {
        pointDataLabel.css({
          fontWeight: 'bold !important',
          textDecoration: 'underline',
          fontSize: '0.9vw !important',
        });
      }

      point.update({ sliced: true });
    }
  }
  createThermalDonutChart() {
    const savedIndex = localStorage.getItem('ThermalComfortIndex');
    this.TCSelectedIndex = savedIndex !== null && !isNaN(parseInt(savedIndex, 10))
    ? parseInt(savedIndex, 10)
    : null;

    const component = this;

    const chart = Highcharts.chart('ThermalContainer', {
      chart: {
        type: 'pie',
        events: {
          load: function () {
            const centerX = this.chartWidth / 2;
            const centerY = this.chartHeight / 2 - 20;

            this.renderer
              .text('Thermal<br>Comfort', centerX, centerY)
              .css({
                fontSize: '0.9vw !important',
                fontFamily: 'Roboto !important',
                fontWeight: 'bold',
                color: 'black',
                textAlign: 'center',
              })
              .attr({
                zIndex: 5,
                align: 'center',
              })
              .add();
          },
        },
      },
      title: {
        // text: "Align our technician's focus with your objectives by prioritising maintenance outcomes.",
        text: ''
      },
      tooltip: {
        enabled: false, // Disable tooltips
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        pie: {
          innerSize: '50%',
          startAngle: -90,
          endAngle: 90,
          dataLabels: {
            enabled: true,
            formatter: function () {
              const labels = ['Low', 'Medium', 'High'];
              return labels[this.point.index];
            },
            style: {
              fontSize: '0.8vw !important',
              fontFamily: 'Roboto !important',
              fontWeight: 'bold',
              color: 'white',
              textOutline: 'none' // Remove any outline for clearer text
            },
            distance: -25, // Adjust distance to center
            alignTo: 'connector', // Align labels towards the slice
            verticalAlign: 'middle', // Vertically align labels
          },
          showInLegend: false,
          point: {
            events: {
              click: function () {
                if (component.TCSelectedIndex !== null) {
                  const prevPoint = this.series.data[component.TCSelectedIndex];
                  const prevDataLabel = (prevPoint as any).dataLabel;

                  if (prevDataLabel) {
                    prevDataLabel.css({
                      // fontWeight: 'bold',
                      textDecoration: 'none',
                      fontSize: '0.8vw !important',
                    });
                  }
                  prevPoint.update({ sliced: false });
                }

                if (component.TCSelectedIndex === this.index) {
                  component.TCSelectedIndex = null;
                  return;
                } else {
                  component.TCSelectedIndex = this.index;
                }
                //  localStorage.setItem('ThermalComfortIndex', this.index.toString());

                const currentDataLabel = (this as any).dataLabel;

                if (currentDataLabel) {
                  currentDataLabel.css({
                    fontWeight: 'bold !important',
                    textDecoration: 'underline',
                    fontSize: '0.9vw !important',
                  });
                }

                this.update({ sliced: true });
              },
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Data',
          data: [
            { name: 'Low', y: 33.33, color: '#A1E6FF' },
            { name: 'Medium', y: 33.33, color: '#3DCDFF' },
            { name: 'High', y: 33.34, color: '#33415B' },
          ],
        },
      ],
    });

    if (this.TCSelectedIndex !== null) {
      const point = chart.series[0].data[this.TCSelectedIndex];
      const pointDataLabel = (point as any).dataLabel;

      if (pointDataLabel) {
        pointDataLabel.css({
          fontWeight: 'bold !important',
          textDecoration: 'underline',
          fontSize: '0.9vw !important',
        });
      }

      point.update({ sliced: true });
    }
  }
  createPriorityDonutChart() {
    const savedIndex = localStorage.getItem('EquipmentLongevityIndex');
    this.ELSelectedIndex = savedIndex !== null && !isNaN(parseInt(savedIndex, 10))
  ? parseInt(savedIndex, 10)
  : null;


    const component = this;

    const chart = Highcharts.chart('EQREVContainer', {
      chart: {
        type: 'pie',
        events: {
          load: function () {
            const centerX = this.chartWidth / 2;
            const centerY = this.chartHeight / 2 - 20;

            this.renderer
              .text('Equipment<br>Longevity', centerX, centerY)
              .css({
                fontSize: '0.9vw !important',
                fontFamily: 'Roboto !important',
                fontWeight: 'bold',
                color: 'black',
                textAlign: 'center',
              })
              .attr({
                zIndex: 5,
                align: 'center',
              })
              .add();
          },
        },
      },
      title: {
        // text: "Align our technician's focus with your objectives by prioritising maintenance outcomes.",
        text: ''
      },
      tooltip: {
        enabled: false, // Disable tooltips
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        pie: {
          innerSize: '50%',
          startAngle: -90,
          endAngle: 90,
          dataLabels: {
            enabled: true,
            formatter: function () {
              const labels = ['Low', 'Medium', 'High'];
              return labels[this.point.index];
            },
            style: {
              fontSize: '0.8vw !important',
              fontFamily: 'Roboto !important',
              fontWeight: 'bold',
              color: 'white',
              textOutline: 'none' // Remove any outline for clearer text
            },
            distance: -25, // Adjust distance to center
            alignTo: 'connector', // Align labels towards the slice
            verticalAlign: 'middle', // Vertically align labels
          },
          showInLegend: false,
          point: {
            events: {
              click: function () {
                if (component.ELSelectedIndex !== null) {
                  const prevPoint = this.series.data[component.ELSelectedIndex];
                  const prevDataLabel = (prevPoint as any).dataLabel;

                  if (prevDataLabel) {
                    prevDataLabel.css({
                      // fontWeight: 'bold',
                      textDecoration: "none",
                      fontSize: "0.8vw !important",
                    });
                  }
                  prevPoint.update({ sliced: false });
                }

                if (component.ELSelectedIndex === this.index) {
                  component.ELSelectedIndex = null;
                  return;
                } else {
                  component.ELSelectedIndex = this.index;
                }

                //  localStorage.setItem('EquipmentLongevityIndex', this.index.toString());

                const currentDataLabel = (this as any).dataLabel;

                if (currentDataLabel) {
                  currentDataLabel.css({
                    fontWeight: 'bold !important',
                    textDecoration: 'underline',
                    fontSize: '0.9vw !important',
                  });
                }

                this.update({ sliced: true });
              },
            },
          },
        },
      },
      series: [
        {
          type: 'pie',
          name: 'Data',
          data: [
            { name: 'Low', y: 33.33, color: '#A1E6FF' },
            { name: 'Medium', y: 33.33, color: '#3DCDFF' },
            { name: 'High', y: 33.34, color: '#33415B' },
          ],
        },
      ],
    });

    if (this.ELSelectedIndex !== null) {
      const point = chart.series[0].data[this.ELSelectedIndex];
      const pointDataLabel = (point as any).dataLabel;

      if (pointDataLabel) {
        pointDataLabel.css({
          fontWeight: 'bold !important',
          textDecoration: 'underline',
          fontSize: '0.9vw !important',
        });
      }

      point.update({ sliced: true });
    }
  }

}

