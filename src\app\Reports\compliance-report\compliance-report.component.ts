import { Component, OnInit, <PERSON>Child, ElementRef } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { Title } from "@angular/platform-browser";
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { SpinnerVisibilityService } from "ng-http-loader";
import { StatusCodes } from "./../../Common/shared";
import { ComplianceReportFoldersData } from "./compliance-report.model";
import { ContextMenuComponent } from "@progress/kendo-angular-menu";
import { first } from "rxjs/operators";
import { saveAs } from 'file-saver';


// const is = (fileName: string, ext: string) =>
//   new RegExp(`.${ext}\$`).test(fileName);
@Component({
  selector: "app-compliance-report",
  templateUrl: "./compliance-report.component.html",
  styleUrls: ["./compliance-report.component.scss"]
})
export class ComplianceReportComponent implements OnInit {
  imageData: string="";
  imageUrl: string="";
  showFile: boolean = false;
  constructor(
    private spinner: SpinnerVisibilityService,
    private coreDataService: CoreDataService,
    private sharedDataService: SharedDataService,
    private titleService: Title,
    private route: ActivatedRoute
  ) {
    let pageTite = this.route.snapshot.data["title"];
    this.titleService.setTitle(pageTite);
    this.sharedDataService.removeBacklogData();
    this.getCompianceReportData();
  }

  ngOnInit() {}
  @ViewChild("treemenu") public gridContextMenu: ContextMenuComponent;
  complianceFoldersData: Array<ComplianceReportFoldersData> = [];
  getCompianceReportData() {
    this.coreDataService
      .getComplianceReportData()
      .pipe(first())
      .subscribe(
        res => {
          if (res != null && res != undefined) {
            if (
              res.StatusCode === StatusCodes.OK ||
              res.StatusCode === StatusCodes.NoContent
            ) {
              this.complianceFoldersData.push(res.response);
            }
          }
        },
        error => {
          if (error.status === StatusCodes.BadRequest) {
            this.sharedDataService.showError("Invalid Request!");
          } else if (error.status === StatusCodes.NotFound) {
            this.sharedDataService.showError("No Data found!");
          } else {
            this.sharedDataService.showError(
              "An error occured, contact support!"
            );
          }
        }
      );
  }

  downloadComplianceDocument(docPath: string) {
    this.spinner.show();
    this.coreDataService
      .downloadComplianceDocument(docPath)
      .pipe(first())
      .subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === StatusCodes.OK) {
              let result = res["_body"];
               this.sharedDataService.showSuccess(
                "Document downloaded successfully."
              );
              this.spinner.hide();
              var blob = new Blob([result], {
                type: res.headers.get("content-type") + ";" + "charset=utf - 8"
              });
              //let reportName = 'Compliacne-Document'+"."+fileType;
              let reportName = this.fileName;
              saveAs(blob, reportName);
            }
             else if(res.status===StatusCodes.NoContent)
            {
              this.sharedDataService.showError("No Data Found");
              this.spinner.hide();
            }
          }
        },
        error => {
          this.sharedDataService.showError(
            "Couldn't download document, Contact support !"
          );
          this.spinner.hide();
        }
      );
  }

  public keys: string[] = [];
  public isExpanded = (dataItem: any, index: string) => {
    return this.keys.indexOf(index) > -1;
  };
  public handleExpand(node) {
    this.keys = this.keys.concat(node.index);
  }
  public handleCollapse(node) {
    this.keys = this.keys.filter(k => k !== node.index);
  }

  public itemSelected: any[] = [
    {
      text: "Download",
      icon: "download",
      cssStyle: { color: "#0291d5",height:"25px" },
      cssClass: "treeViewUI contextMenuUI"
    }
  ];

  private contextItem: any;
  private fileName: any;
  public fileClick(e: any): void {
    if (e.dataItem.isFolder === false) {
      if (e.dataItem.path != null && e.dataItem.path != undefined) {
        this.previewFile(e.dataItem.path);
      }
    }
  }
  public onNodeClick(e: any): void {
    if (e.type === "contextmenu" && e.item.dataItem.isFolder == false) {
      const originalEvent = e.originalEvent;

      originalEvent.preventDefault();

      this.contextItem = e.item.dataItem;
      this.fileName = e.item.dataItem.title;
      this.gridContextMenu.show({
        left: originalEvent.pageX,
        top: originalEvent.pageY
      });
    }
  }
  // RG: Download the Compliance file on double click
  public downloadOnDoubleClick(e): void {
    if (!e.item.dataItem.isFolder) {
      if (e.item.dataItem != null && e.item.dataItem != undefined) {
        this.fileName = e.item.dataItem.title;
        this.downloadComplianceDocument(e.item.dataItem.path);
      } else {
        this.sharedDataService.showWarning("No document available!");
      }
    }
  }

  public onSelect( item ): void {
     if (item.item.text === "Download") {
      if (this.contextItem != null && this.contextItem != undefined) {
        this.downloadComplianceDocument(this.contextItem.path); //this.contextItem, this.data
      } else {
        this.sharedDataService.showWarning("No document available!");
      }
    }
  }
  previewFile(filePath) {
    if (filePath != null && filePath != undefined) {
      this.spinner.show();
      var that=this;
      this.coreDataService
        .downloadComplianceDocument(filePath)
        .pipe(first())
        .subscribe(
          res => {
            if (res != null && res != undefined) {
              if (res.status === StatusCodes.OK) {
                let result = res["_body"];
                var blob = new Blob([result], {
                  type:res.headers.get("content-type") //+ ";" + "charset=utf - 8"
                });

                // var fileType=blob.type.split('/',2)[1];
                // var fileURL = URL.createObjectURL(blob);
                // //window.open(fileURL+'.'+fileType);
                // that.imageUrl=fileURL+'.'+fileType;
                // that.showFile = true;

              //MS: Blob to Base64 --start
                var reader = new FileReader();
                reader.onload = function() {
                  var b64 = reader.result.toString().replace(/^data:.+;base64,/, '');
                   var imageData = b64;

                  that.imageUrl ="data:image/jpg;base64," + imageData;
                  that.showFile = true;
 
                };
                reader.readAsDataURL(blob);
              //MS: Blob to Base64 --end


                this.spinner.hide();
              }
            }
          },
          error => {
            this.sharedDataService.showError(
              "Couldn't preview document, Contact support !"
            );
            this.spinner.hide();
          }
        ); //this.contextItem, this.data
    } else {
      this.sharedDataService.showWarning("No document available!");
    }
  }
  public iconClass(isFolder, extension): any {
    return {
      "k-i-photo": extension == "tif",
      "k-i-image": extension == "jpg",
      "k-i-file-pdf": extension == "pdf",
      "k-i-folder": isFolder == true,
      "k-icon": true,
      "k-i-document-manager":
        !isFolder &&
        extension != "tif" &&
        extension != "jpg" &&
        extension != "pdf"
    };
  }
  
}
