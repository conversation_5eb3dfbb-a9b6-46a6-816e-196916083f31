import { Injectable } from "@angular/core";
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from "@angular/router";
import { SharedDataService } from "../Services/shared-data.service";
import { AuthenticationService } from "../Services/authentication.service";
import { Observable } from "rxjs-compat";
import { TokenModel } from "../Common/shared";
import { NavigationLoggerService } from "./navigation-logger.service";

export interface CanComponentDeactivate {
  canDeactivate: () => Observable<boolean> | Promise<boolean> | boolean;
}
@Injectable()
export class AuthGuardService implements CanActivate {
  Userinfo: any;
  currentComponent: string;
  tokenData: TokenModel = new TokenModel();
  constructor(
    private authService: AuthenticationService,
    private SharedDataService: SharedDataService,
    private router: Router,
    private navigationLoggerService: NavigationLoggerService
  ) {
    this.authService.userInfo$.subscribe((token) => {
      if (token != undefined) this.Userinfo = token.UserInfo;
    });
  }
  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (this.authService.authenticated) {
      this.tokenData = this.authService.getTokenData();
      if (this.tokenData.IsExternalUser === "1") {
        if (
          state.url.includes("MoM") ||
          state.url === "/SubContractor" ||
          state.url.includes("followUpMeeting") ||
          state.url.includes("MonthlyReport")
        ) {
          return false;
        }
      } else if (this.tokenData.Role != "6") {
        if (state.url.includes("clientPerceptionReport")) {
          return false;
        }
      }
      // followUpMeeting
    }
    const isUnprotectedRoute = !!~["/", "/Login"].indexOf(state.url);
    if (this.authService.authenticated && !isUnprotectedRoute) {
      if (!this.navigationLoggerService.isInitialized()) {
        this.navigationLoggerService.initialize();
      }
      return true;
    } else if (this.authService.authenticated && isUnprotectedRoute) {
      this.router.navigate(["/Dashboard"]);
      return false;
    } else if (isUnprotectedRoute || !!~state.url.indexOf("/?returnUrl")) {
      return true;
    } else {
      // this.router.navigate(['/'], { queryParams: { returnUrl: state.url } });

      this.SharedDataService.broadcastLoginStatus(false);
      this.authService.setLoggedIn(false);
      this.router.navigateByUrl("");
      this.SharedDataService.showWarning(
        "Your Session has expired. Please re-Login"
      );
      return false;
    }
  }
  canDeactivate(component: CanComponentDeactivate) {
    if (this.authService.authenticated) {
      return component.canDeactivate ? component.canDeactivate() : true;
    }
    return true;
  }
}
@Injectable()
export class LoginAuthGuardService implements CanActivate {
  Userinfo: any;
  currentComponent: string;
  tokenData: TokenModel = new TokenModel();
  constructor(
    private authService: AuthenticationService,
    private SharedDataService: SharedDataService,
    private router: Router
  ) {
    this.authService.userInfo$.subscribe((token) => {
      if (token != undefined) this.Userinfo = token.UserInfo;
    });
  }
  canActivate(
    activatedRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ) {
    if (this.authService.authenticated) {
      this.tokenData = this.authService.getTokenData();
      this.router.navigate(["/Dashboard"]);
      return false;
    } else {
      if (
        activatedRoute.paramMap.get("flowT") != undefined &&
        activatedRoute.paramMap.get("flowT") != null
      ) {
        return true;
      } else {
        this.router.navigate(["/Login"]);
        return false;
      }
    }
  }
}
@Injectable()
export class MFAGuardService implements CanActivate {
  constructor(
    private SharedDataService: SharedDataService,
    private router: Router
  ) {}
  canActivate() {
    if (
     
      this.SharedDataService.loginUsername != undefined 
    ) {
      return true;
    } else {
      this.router.navigate(["/Login"]);
      return false;
    }
  }
}
