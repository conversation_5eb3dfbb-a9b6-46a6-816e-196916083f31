import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import * as moment from 'moment';
import { process, State, } from '@progress/kendo-data-query';
import { DataStateChangeEvent } from '@progress/kendo-angular-grid';
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { JwtHelper } from 'angular2-jwt';
import { Router, ActivatedRoute } from "@angular/router";
import { SharedDataService } from "../../Services/shared-data.service";
import { CustomerLocationByName, MOMEntity, CodeData, EditMoMListEntity, CustomersEntities } from "./mom.model";
import { AuthenticationService } from "../../Services/authentication.service";
import { parseNumber } from '@telerik/kendo-intl';
import { TokenDecodedEntities } from '../../Common/shared';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { SessionStorageService } from 'ngx-webstorage';
import { first } from 'rxjs/operators';
const createFormGroup = dataItem => new FormGroup({
  'ITEM': new FormControl(dataItem.ITEM),
  'DESCRIPTION': new FormControl(dataItem.DESCRIPTION, Validators.required),
  'ACTION': new FormControl(dataItem.ACTION, Validators.required),
  'NOTES': new FormControl(dataItem.NOTES),
  'statusId': new FormControl(dataItem.statusId, Validators.required),
  'DATE': new FormControl(dataItem.DATE, Validators.required),
  "TaskID": new FormControl(dataItem.TaskID)

});

export let isBrowserRefreshed: boolean = false;

@Component({
  selector: 'app-mom',
  templateUrl: './mom.component.html',
  styleUrls: ['./mom.component.scss']
})
export class MOMComponent implements OnInit, DoCheck, OnDestroy {
  StatedefaultValue: any;
  opened: boolean = false;
  hasAllValueSelected: boolean = false;
  EditMoMList: EditMoMListEntity = new EditMoMListEntity();
  StateList: CodeData[] = [];
  tokenData: TokenDecodedEntities = new TokenDecodedEntities();

  stateName: string;
  defaultLocation = [];

  defaultDropdown = "Select Locations";

  LoggedInUser: string;
  codeData: CodeData[] = [];
  selectedMoMFields: MOMEntity = new MOMEntity();
  MoMStatus = [
    {
      'statusId': 1,
      'Name': 'Open',
    },
    {
      'statusId': 2,
      'Name': 'CarriedFW',
    },
    {
      'statusId': 3,
      'Name': 'OnGoing',

    },
    {
      'statusId': 4,
      'Name': 'Complete',
    },
    {
      'statusId': 5,
      'Name': 'Comment'
    },
    {
      'statusId': 6,
      'Name': 'Parked'
    }];

  public onClose(event: any) {
    event.preventDefault();
  }

  public category(id: number): any {
    return this.MoMStatus.find(x => x.statusId === id);
  }
  @ViewChild('momRef') formSubmit: ElementRef;

  locationList: CustomersEntities[] = [];
  public names: any[];
  defaultLocationAsString: string;
  totalSelectedLocations: string;

  routeParams: string;
  private editedRowIndex: number;
  public data: Array<CustomerLocationByName> = [];
  value: Date = new Date();
  listItems: CustomersEntities[] = [];
  userId: number;
  gridData: any = [];
  totalCount: number;
  // sad
  subscrption: Subscription;
  followUpMeeting: string;
  jwtHelper: JwtHelper = new JwtHelper();
  originalDate = new Date();
  public formGroup: FormGroup;
  public valueDefault: Array<any> = [];

  products = [];
  public state: State = {
    skip: 0,
    take: 10,
    // Initial filter descriptor
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public cancelHandler({ sender, rowIndex }) {
    this.closeEditor(sender, rowIndex);
  }
  public filterChange(filter: any): void {
    // this.data = this.listItems.filter((s) => s.Name.includes(filter.toUpperCase()));
    this.data = this.listItems.filter((s) => {
      const filterUpperCase = filter.toUpperCase();
      return (
          s.Name.includes(filterUpperCase) ||
          s.CustName.includes(filterUpperCase) ||
          s.CustomerCode.includes(filterUpperCase) ||
          s.LocationCode.includes(filterUpperCase)
      );
    });

  }
  ngDoCheck() {
    let locations = [];
    let defaultLocation = [];
    this.valueDefault.map((element, index) => {
      if ((index + 1) <= 2) {
        this.totalCount = index + 1;
        defaultLocation.push(element.Name);
        this.defaultLocation = defaultLocation;
      }
      this.defaultLocationAsString = this.defaultLocation.toString();
      locations.push(element.Name);
    });
    this.totalSelectedLocations = locations.toString();
  }
  public open(dataItem) {
    this.opened = true;
    // this.locationsData = dataItem;
  }
  removeTag(index) {
    let data = this.valueDefault.slice();
    data.splice(index, 1);
    this.valueDefault = data;
    this.checkIsListSelectedAll();
  }
  public selectedValue: number = 2;
  constructor(private session: SessionStorageService, private titleService: Title, private route: ActivatedRoute, private router: Router, private coredata: CoreDataService, private authservice: AuthenticationService, private shareData: SharedDataService) {
    let Token = this.authservice.getTokenData();
    this.tokenData = Token;
    this.shareData.removeBacklogData();
    this.routeParams = this.route.snapshot.paramMap.get('id');
    this.followUpMeeting = this.route.snapshot.paramMap.get('value');
    let pageTite: string;
    if (this.routeParams === null) {
      pageTite = "Create MoM";
    }
    else {
      pageTite = "Edit MoM";
    }
    this.titleService.setTitle(pageTite);
    this.LoggedInUser = Token.Name;
    this.userId = parseInt(Token.UserID);
    this.selectedMoMFields.Prepared = this.LoggedInUser;
    this.StatedefaultValue = { CodeID: null, CodeTypeID: null, CodeName: " ", Code: " ", Description: "Select State" };

    this.selectedMoMFields.Date = new Date();
  }

  ngOnInit() {
    this.loadCodeData();
    isBrowserRefreshed = !this.router.navigated;
    if (isBrowserRefreshed === false) {

      this.loadCustomerLocationDropdown(this.tokenData.UserID, undefined);
    }


  }
  public addHandler({ sender }) {
    this.closeEditor(sender);
    let totalItem = this.gridData.length + 1;
    this.formGroup = createFormGroup({
      'ITEM': totalItem,
      'DESCRIPTION': '',
      'ACTION': '',
      'NOTES': '',
      'statusId': 1,
      'DATE': new Date()
    });
    sender.addRow(this.formGroup);
  }

  loadCustomerLocationDropdown(id, locations) {
    if (locations === undefined) {
      let sessionLocation = this.session.retrieve('locations');
      if (sessionLocation === null) {
        this.coredata.getCustomerLocation(id).pipe(first()).subscribe(res => {
          if (res.StatusCode === 200) {
            this.locationList = res.response;
            this.listItems = this.locationList;
            this.data = JSON.parse(JSON.stringify(this.listItems));
            if (this.routeParams != undefined && this.routeParams != null) {
              this.loadMeetingByID(this.routeParams);
            }
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
              // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
            }
          });
      }
      else if (sessionLocation != null) {
        this.locationList = sessionLocation;
        this.listItems = this.locationList;
        this.data = JSON.parse(JSON.stringify(this.listItems));
        if (this.routeParams != undefined && this.routeParams != null) {
          this.loadMeetingByID(this.routeParams);
        }
      }
    }
    else if (locations != undefined) {
      this.locationList = locations;
      this.listItems = this.locationList;
      this.data = JSON.parse(JSON.stringify(this.listItems));
      if (this.routeParams != undefined && this.routeParams != null) {
        this.loadMeetingByID(this.routeParams);
      }
    }




  }
  locationchange(value) {
    this.stateName = value[0]["StateCode"];
    let defaultvalue = this.StateList.find(el => el.Code === value[0]["StateCode"]);
    this.StatedefaultValue = defaultvalue;
    this.checkIsListSelectedAll();
  }
  loadMeetingByID(MeetingID) {
    this.coredata.getMoMById(MeetingID).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          let taskList = [];
          let MoMTaskList = [];
          this.EditMoMList = res.response;
          this.selectedMoMFields.Apologies = this.EditMoMList.Apologies;
          this.selectedMoMFields.Date = new Date(this.EditMoMList.MeetingDate);
          this.selectedMoMFields.HeldAt = this.EditMoMList.HeldAt;
          this.selectedMoMFields.NextMeeting = this.EditMoMList.NextMeeting;
          if (this.EditMoMList.OwnerID === this.userId) {
            this.selectedMoMFields.Prepared = this.LoggedInUser;
          }
          this.selectedMoMFields.Present = this.EditMoMList.Attendees;
          this.selectedMoMFields.meeting = this.EditMoMList.MeetingTitle;
          this.StatedefaultValue = this.StateList.find(element => element.Code === this.EditMoMList.State);
          this.stateName = this.EditMoMList.State;
          if (!!this.EditMoMList.MeetingLocations) {
            let valueDefault = [];
            this.EditMoMList.MeetingLocations.split(",").map(obj => {

              let value = this.data.find(el => el.LocationID == parseNumber(obj));
              if (value != undefined) {
                valueDefault.push(value);
                this.valueDefault = valueDefault;
              }
            })
          }
          if (this.followUpMeeting != null) {
            MoMTaskList = this.EditMoMList.MoMTasks.filter(ele => ele["Status"] === 1);
          }
          else {
            MoMTaskList = this.EditMoMList.MoMTasks;
          }
          MoMTaskList.map((obj, index) => {

            taskList.push({
              ITEM: index + 1,
              DESCRIPTION: obj["Description"],
              ACTION: obj["Action"],
              NOTES: obj["Notes"],
              statusId: obj["Status"],
              DATE: this.GetFormattedDate(obj["DueDate"]),
              TaskID: obj["TaskID"]
            })
          });
          this.products = taskList;
          this.gridData = this.products;

        }

      }


    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }
  stateChangeValue(value) {
    this.stateName = value.Code;
    // this.loadCustomerLocationDropdown(this.userId, value.Code, "stateChange");
  }

  loadCodeData() {
    this.coredata.getCodeData().pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        this.codeData = res.response;
        this.StateList = this.codeData.filter(data => data.CodeName === "STATE");
      }

    }, catchError => {
      if (catchError) {

      }
    });
  }
  public editHandler({ sender, rowIndex, dataItem }) {
    this.closeEditor(sender);
    // this.getNames(dataItem.ITEM);
    this.formGroup = createFormGroup(dataItem);
    this.editedRowIndex = rowIndex;
    sender.editRow(rowIndex, this.formGroup);
  }

  private closeEditor(grid, rowIndex = this.editedRowIndex) {
    grid.closeRow(rowIndex);
    this.editedRowIndex = undefined;
    this.formGroup = undefined;
  }
  handleFilter(value) {
    this.data = this.listItems.filter((s) => s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1);
  }

  public dataStateChange(state: DataStateChangeEvent) {
    this.state = state;
    this.gridData = process(this.products, this.state);

  }
  public saveHandler({ sender, rowIndex, formGroup, isNew }) {

    const product = formGroup.value;
    if (isNew) {
      this.products.push(product)
      this.gridData = this.products;
    }
    else {
      this.products.splice(rowIndex, 1, product)
      this.gridData = this.products;
    }
    sender.closeRow(rowIndex);
  }



  public tagMapper(tags: any[]): any[] {
    return tags.length < 3 ? tags : [tags];
  }
  saveMeeting() {
    if(this.formSubmit['form'].status === 'INVALID'){
      this.shareData.showError('Please fill all mandatory fields');
      return;
    }
    let code = [];
    this.valueDefault.map(element => {
      code.push(element.LocationID);
    })
    let payloadMoM = {
      MeetingTitle: this.selectedMoMFields.meeting,
      MeetingDate: this.selectedMoMFields.Date,
      HeldAt: this.selectedMoMFields.HeldAt,
      Attendees: this.selectedMoMFields.Present,
      OwnerID: this.userId,
      Apologies: this.selectedMoMFields.Apologies,
      NextMeeting: this.selectedMoMFields.NextMeeting,
      state: this.stateName,
      MeetingLocations: code.toString(),
    }
    if (this.routeParams != null && this.routeParams != undefined && this.followUpMeeting === null) {
      payloadMoM["MeetingID"] = parseInt(this.routeParams);
    }

    this.coredata.createMoM(payloadMoM).subscribe(res => {
      if (res.StatusCode === 200 && typeof res.response === "number") {
        this.saveMoMTaskDetail(res.response);
        if (this.routeParams != null && this.routeParams != undefined && this.followUpMeeting === null) {
          this.shareData.showSuccess('MoM saved successfully');

        }
        else {
          this.shareData.showSuccess('MoM created successfully');

        }
        this.router.navigate(["/MoM"]);
      }
    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
  clearAll() {
    this.valueDefault = [];
    this.opened = false;
    this.hasAllValueSelected = false;
  }

  saveMoMTaskDetail(meetingID) {
    let MoMTaskPayload = {};
    this.gridData.map(element => {
      MoMTaskPayload = {
        MeetingID: meetingID,
        Description: element.DESCRIPTION,
        Action: element.ACTION,
        Notes: element.NOTES,
        Status: element.statusId,
      }
      if (this.routeParams != null && this.followUpMeeting === null) {
        MoMTaskPayload["TaskID"] = element.TaskID;
      }

      if (MoMTaskPayload["TaskID"] === undefined || MoMTaskPayload["TaskID"] === null) {
        delete MoMTaskPayload["TaskID"];
      }

      this.coredata.createMoMTask(MoMTaskPayload).subscribe(response => {

        if (response != null && response != undefined) {

          if (response.StatusCode === 200) {
          }
        }
      }, catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
    }
    );
  }
  public close(value) {
    if (value === "cancel") {
      this.opened = false;
      if (!!this.EditMoMList.MeetingLocations) {
        let valueDefault = [];
        this.EditMoMList.MeetingLocations.split(",").map(obj => {

          let value = this.data.find(el => el.LocationID == parseNumber(obj));
          if (value != undefined) {
            valueDefault.push(value);
            this.valueDefault = valueDefault;
          }
        })
      }
    }
    else if (value === "yes") {
      this.opened = false;

    }
  }
  OnSelectAllChange() {
    this.hasAllValueSelected = true;
    this.valueDefault = this.data;
  }
  OnDeSelectAllChange() {
    this.hasAllValueSelected = false;
    this.valueDefault = [];
  }

  checkIsListSelectedAll() {

    if (this.valueDefault.length === this.data.length) {
      this.hasAllValueSelected = true;
    }
    else if (this.valueDefault.length < this.data.length) {
      this.hasAllValueSelected = false;
    }
  }
  ngOnDestroy() {
    // this.subscrption.unsubscribe();
  }
  locationChangeOnRefresh(event) {
    this.loadCustomerLocationDropdown(this.tokenData.UserID, event);
  }
}
