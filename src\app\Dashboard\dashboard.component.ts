import { Component, OnInit } from "@angular/core";
import { CoreDataService } from "../Services/core-data.service";
import { StatusCodes } from "../Common/shared";
import { Title } from "@angular/platform-browser";
import { ActivatedRoute } from "@angular/router";
import { Router } from "@angular/router";
import { SharedDataService } from "../Services/shared-data.service";
import {
  pieChartModel,
  chartSeriesModel,
  OpenDebtGraphEntity,
  TMMonthGraphEntity,
  TMMonthGraphByStatusCallEntity,
  OpenDebtChartVALUES,
  piechanrtInnerDataModel,
  locationDateFilterEntity,
  CustomerLocationByName,
  CustomerSummary,
  LocationDropdownListModel,
  TMMonthGraphByStatusCallChartValues,
  MostExpensiveEquipment,
  MostExpensiveEquipmentChartData,
} from "./dashboard.model";
import { AuthenticationService } from "../Services/authentication.service";
import { first } from "rxjs/operators";
@Component({
  selector: "admin-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
})
export class DashboardComponent implements OnInit {
  PieChartStatuses = "9,184";
  originalDate = new Date();
  startDate: string;
  endDate: string;
  currency: string;
  Ammount: string = "Amount in";
  countOpenDebt: boolean = false;
  defaultLocation = [];
  defaultLocationAsString: string;
  public MonthList: Array<any> = [];
  TotalCalls = [];
  AHTotalCalls = [];
  TotalAhCalls = [];
  TotalTMCalls = [];
  locations = [];
  defaultCurrency: string;
  TotalStatus: number = 0;
  getTMMonthGraphList: TMMonthGraphEntity[] = [];
  twoYearsTotalGraphList: TMMonthGraphEntity[] = [];
  //FK: declaration for service calls by status
  getTMMonthGraphListByStatus: TMMonthGraphByStatusCallEntity[] = [];
  twoYearsTotalGraphListByStatus: TMMonthGraphByStatusCallEntity[] = [];
  MostExpensiveEquipmentData: Array<MostExpensiveEquipment> = [];
  MostExpensiveEquipmentChartData = [];
  MostExpensiveEquipCharCategories = [];
  //public MonthListByStatusCalls: Array<any> = [];
  TotalCallsByStatus = [];
  OPENCallsByStatus = [];
  COMPLETECallsByStatus = [];
  INVOICEDCallsByStatus = [];
  categoriesServiceCallsChartByStatusCalls = [];
  ServiceCallsCountByStatus: boolean = false;
  preOpenCalls = [];
  preCompleteCalls = [];
  preInvoicedCalls = [];
  // end FK
  categoriesServiceCallsChart = [];
  TotalValue: number = 0;
  TotalValueQuoteType: number = 0;
  OpenDebtcategories = [];
  QuoteSeriesData: chartSeriesModel = new chartSeriesModel();
  public pieData: any = [];
  pieDataQuoteType: any = [];
  public model: any[] = piechanrtInnerDataModel;
  listQuoteType: Array<pieChartModel> = [];
  list: Array<pieChartModel> = [];
  ServiceCallsCount: boolean = false;
  listQuoteStatus: Array<pieChartModel> = [];
  Total: number = 0;
  priority = "PRIORITY";
  quotetype = "QUOTETYPE";
  openDebtGraphList: OpenDebtGraphEntity[] = [];
  barType = "QUOTESTATUS";
  public openDebtData = [];
  public ApproveData = [];
  Quotestatus: boolean = false;
  public AwaitingApprove: any = [""];
  fromDateFilter: any;
  categories = [];
  locationDateFilterData: locationDateFilterEntity =
    new locationDateFilterEntity();
  Category = [];
  public range: any;
  public autofit = true;
  constructor(
    private titleService: Title,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private coredata: CoreDataService,
    private authservice: AuthenticationService,
    private shareData: SharedDataService
  ) {
    let pageTite = this.activatedRoute.snapshot.data["title"];
    this.titleService.setTitle(pageTite);
    this.shareData.removeBacklogData();
    this.fromDateFilter = this.shareData.getStartDateByUser();
    this.locationDateFilterData = JSON.parse(localStorage.getItem("location"));
    if (this.locationDateFilterData === null) {
      if (
        this.fromDateFilter == "null" ||
        this.fromDateFilter == undefined ||
        this.fromDateFilter == ""
      ) {
        this.range = {
          start: new Date(
            this.originalDate.getFullYear() - 3,
            this.originalDate.getMonth(),
            this.originalDate.getDate() + 1
          ),
          end: new Date(
            this.originalDate.getFullYear(),
            this.originalDate.getMonth(),
            this.originalDate.getDate()
          ),
        };
      } else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(
            StartingDate.getFullYear(),
            StartingDate.getMonth(),
            StartingDate.getDate()
          ),
          end: new Date(
            this.originalDate.getFullYear(),
            this.originalDate.getMonth(),
            this.originalDate.getDate()
          ),
        };
      }
    } else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations
          .split(",")
          .map((el) => parseInt(el));
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate),
      };
    }
  }
  ngOnInit() {
    this.shareData.defaultasObservable.subscribe((data) => {});
    this.defaultCurrency = "";
    // this.loadPriority();
    // this.loadQuoteTYPEData();
    // this.loadQuoteStatus();
    //FK: comment html code of old service graph
    //this.loadServiceCallsChart();
    // MS : Call Most Expensive Equipment API Here to load on Graph.
    this.GetMostExpensiveEquipmentsChartData();
    this.loadServiceCallsChartByStatus();
    this.loadOpenDebtChart();
    this.loadQuotesSummary();
  }

  onSeriesOpenDebt(event) {
    this.router.navigate(["/OpenDebt", OpenDebtChartVALUES[event.category]]);
  }
  //MS: Opening Service Call by TM Month Graph Bars by Call Type
  onServiceCallByStatus(event) {
    if (event != null && event != undefined) {
      let value =
        event.series.name.split(" ", 1)[0].toString() + " 13-" + event.category;

      this.router.navigate(["/ServiceCalls", value]);
    }
  }
  loadServiceCallsChart() {
    let originalDate = new Date();
    let range = {
      Date: new Date(
        originalDate.getFullYear(),
        originalDate.getMonth(),
        originalDate.getDate()
      ),
    };
    this.coredata.GetTMMonthGraph(this.locations, range.Date).subscribe(
      (response) => {
        if (response[0].StatusCode === 200) {
          this.ServiceCallsCount = true;
          this.MonthList = [];
          this.categoriesServiceCallsChart = [];
          this.TotalCalls = [];
          this.AHTotalCalls = [];
          this.TotalAhCalls = [];
          this.TotalTMCalls = [];
          this.getTMMonthGraphList = response[0].response;
          this.twoYearsTotalGraphList = response[1].response;
          for (let key in this.getTMMonthGraphList) {
            this.MonthList.push(this.getTMMonthGraphList[key].TMTotalCalls);
            this.categoriesServiceCallsChart.push(
              this.getTMMonthGraphList[key].Months
            );
            this.TotalCalls.push(this.getTMMonthGraphList[key].TotalCalls);
            this.AHTotalCalls.push(this.getTMMonthGraphList[key].AHTotalCalls);
          }
          for (let key in this.twoYearsTotalGraphList) {
            this.TotalAhCalls.push(
              this.twoYearsTotalGraphList[key].AHTotalCalls
            );
            this.TotalTMCalls.push(
              this.twoYearsTotalGraphList[key].TMTotalCalls
            );
          }
        }
      },
      (error) => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      }
    );
  }

  loadServiceCallsChartByStatus() {
    this.coredata.GetTMMonthGraphByStatusCall(this.locations).subscribe(
      (response) => {

        
        if (response.StatusCode === 200) {
          this.ServiceCallsCountByStatus = true;
          this.categoriesServiceCallsChartByStatusCalls = [];
          this.TotalCallsByStatus = [];
          this.OPENCallsByStatus = [];
          this.COMPLETECallsByStatus = [];
          this.INVOICEDCallsByStatus = [];
          this.preOpenCalls = [];
          this.preCompleteCalls = [];
          this.preInvoicedCalls = [];
          this.getTMMonthGraphListByStatus = response.response.RecentThirteenMonths;
          this.twoYearsTotalGraphListByStatus = response.response.PreviousThirteenMonths;
          for (let key in this.getTMMonthGraphListByStatus) {
            this.OPENCallsByStatus.push(
              this.getTMMonthGraphListByStatus[key].OPENCalls
            );
            this.COMPLETECallsByStatus.push(
              this.getTMMonthGraphListByStatus[key].COMPLETECalls
            );
            this.INVOICEDCallsByStatus.push(
              this.getTMMonthGraphListByStatus[key].INVOICEDCalls
            );
            this.categoriesServiceCallsChartByStatusCalls.push(
              this.getTMMonthGraphListByStatus[key].Months
            );
            this.TotalCallsByStatus.push(
              this.getTMMonthGraphListByStatus[key].TotalCalls
            );
          }
          for (let key in this.twoYearsTotalGraphListByStatus) {
            this.preOpenCalls.push(
              this.twoYearsTotalGraphListByStatus[key].OPENCalls
            );
            this.preCompleteCalls.push(
              this.twoYearsTotalGraphListByStatus[key].COMPLETECalls
            );
            this.preInvoicedCalls.push(
              this.twoYearsTotalGraphListByStatus[key].INVOICEDCalls
            );
          }
        }
      },
      (error) => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      }
    );
  }

  OpenDebtCurrencyToString(value: any) {
    if (value == 0) {
      return 0;
    } else {
      // hundreds
      if (value <= 999) {
        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat(
            this.openDebtGraphList[key].TotalAmount.toString()
          )
            .toFixed(2)
            .toString();
          this.openDebtData.push(data);
          this.OpenDebtcategories.push(this.openDebtGraphList[key].Aged);
        }
      }
      // thousands
      else if (value >= 1000 && value <= 999999) {
        this.currency = "k";
        this.Ammount = "Amount in " + this.currency;
        for (let key in this.openDebtGraphList) {
          0;
          let data = Number.parseFloat(
            (this.openDebtGraphList[key].TotalAmount / 1000).toString()
          )
            .toFixed(2)
            .toString();
          this.openDebtData.push(data);
          this.OpenDebtcategories.push(this.openDebtGraphList[key].Aged);
        }
      }
      // millions
      else if (value >= 1000000 && value <= 999999999) {
        this.currency = "M";
        this.Ammount = "Amount in " + this.currency;

        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat(
            (this.openDebtGraphList[key].TotalAmount / 1000000).toString()
          )
            .toFixed(2)
            .toString();
          this.openDebtData.push(data);
          // this.openDebtData=[1,2,4];
          this.OpenDebtcategories.push(this.openDebtGraphList[key].Aged);
        }
      }
      // billions
      else if (value >= 1000000000 && value <= 999999999999) {
        this.currency = "B";
        this.Ammount = "Amount in " + this.currency;

        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat(
            (this.openDebtGraphList[key].TotalAmount / 1000000000).toString()
          )
            .toFixed(2)
            .toString();
          this.openDebtData.push(data);
          this.OpenDebtcategories.push(this.openDebtGraphList[key].Aged);
        }
        // return Number.parseFloat((value / 1000000000).toString()).toFixed(2).toString() + 'B';
      } else {
        return value;
      }
    }
  }
  GetMostExpensiveEquipmentsChartData() {
    let dateFilter: Date = new Date(this.range["start"]);
    this.coredata
      .getMostExpensiveEquipment(dateFilter, this.locations.toString())
      .subscribe((response) => {
        if (response.StatusCode === 200) {
          this.MostExpensiveEquipmentData = [];
          this.MostExpensiveEquipCharCategories = [];
          this.MostExpensiveEquipmentChartData = [];

          this.MostExpensiveEquipmentData = response.response;
          this.MostExpensiveEquipmentData.forEach((element) => {
            this.MostExpensiveEquipmentChartData.push(element.Spend_ExGST);
            this.MostExpensiveEquipCharCategories.push(element.EquipmentID);
          });
        } else {
          this.shareData.showError(
            "An exception occur, please contact support."
          );
        }
      });
  }
  loadOpenDebtChart() {
    this.coredata
      .getOpenDebtGraph(this.locations)
      .pipe(first())
      .subscribe(
        (response) => {
          if (response.StatusCode === 200) {
            this.countOpenDebt = true;
            this.openDebtData = [];
            this.OpenDebtcategories = [];
            this.openDebtGraphList = response.response;
            let maxAmount = 0;
            for (let key in this.openDebtGraphList) {
              let totalAmount = this.openDebtGraphList[key].TotalAmount;
              if (totalAmount > maxAmount) {
                maxAmount = totalAmount;
              }
            }
            this.OpenDebtCurrencyToString(maxAmount);
            this.CurrencyToString(maxAmount);
          }
        },
        (error) => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        }
      );
  }
  onMostExpensiveEquipChartClick(event) {
    if (event != null && event != undefined) {
      let value = "MostExpensiveEquipment";
      this.shareData.MostExpEqpBar = this.MostExpensiveEquipmentData.find(
        (obj) => {
          return (
            obj.EquipmentID == event.category &&
            obj.Spend_ExGST == event.dataItem
          );
        }
      );
      this.router.navigate(["/OpenDebt", value]);
    }
  }
  public onSeries(e) {
    this.QuoteSeriesData = e.dataItem;
    this.router.navigate([
      "/Quotes",
      this.priority,
      this.QuoteSeriesData.category,
    ]);
  }
  public onSeriesQuoteType(e) {
    this.QuoteSeriesData = e.dataItem;
    this.router.navigate([
      "/Quotes",
      this.quotetype,
      this.QuoteSeriesData.category,
    ]);
  }

  public onSeriesBarChart(e) {
    this.QuoteSeriesData = e;
    this.router.navigate([
      "/Quotes",
      this.barType,
      this.QuoteSeriesData.category,
    ]);
  }

  // loadQuoteTYPEData() {
  //   this.quotetype = "QUOTETYPE";
  //   this.coredata.getPiechartData(this.quotetype, this.locations, new Date(this.range["start"]), new Date(this.range["end"]), this.PieChartStatuses
  //   ).subscribe(response => {
  //     if (response != null && response != undefined) {
  //       if (response.response) {
  //         this.listQuoteType = [];
  //         this.pieDataQuoteType = [];
  //         this.listQuoteType = response.response;
  //         this.TotalValueQuoteType = this.list.length;

  //         for (let key in this.listQuoteType) {
  //           if (this.quotetype === 'QUOTETYPE') {
  //             if (this.listQuoteType[key].Type === "CONTROLS QUOTES") {
  //               this.listQuoteType[key].Type = "CONTROLS"

  //             }
  //             else if (this.listQuoteType[key].Type === "MAINTENANCE QUOTES") {
  //               this.listQuoteType[key].Type = "MAINTENANCE"
  //             }
  //             else if (this.listQuoteType[key].Type === "PROJECTS QUOTES") {
  //               this.listQuoteType[key].Type = "PROJECTS"
  //             }
  //             else if (this.listQuoteType[key].Type === "SERVICE QUOTES") {
  //               this.listQuoteType[key].Type = "SERVICE";

  //             }

  //           }
  //           this.pieDataQuoteType.push({ category: this.listQuoteType[key].Type, value: this.listQuoteType[key].Total })
  //           this.TotalValueQuoteType = this.TotalValueQuoteType + this.listQuoteType[key].Total;

  //         }
  //       }

  //     }
  //   },
  //     error => {
  //       if (error) {
  //         this.shareData.ErrorHandler(error);
  //         // this.toastrService.error("response error !");
  //       }
  //     });
  // }
  loadQuotesSummary() {
    this.coredata
      .getQuotesSummary(
        this.locations,
        new Date(this.range["start"]),
        new Date(this.range["end"]),
        this.PieChartStatuses
      )
      .subscribe(
        (response) => {
          // console.log(response.response, "RR");
        this.categories = [];
        this.ApproveData = [];
        this.AwaitingApprove = [""];
        this.Quotestatus = true;
          if (response != null && response != undefined && response.StatusCode !== 204) {
            if (response.response.PRIORITY) {
              this.list = [];
              this.pieData = [];
              this.list = response.response.PRIORITY;
              this.Total = this.list.length;
              for (let key in this.list) {
                this.pieData.push({
                  category: this.list[key].Type,
                  value: this.list[key].Total,
                });
                this.TotalValue = this.TotalValue + this.list[key].Total;
              }
            }
            if (response.response.QUOTETYPE) {
              this.listQuoteType = [];
              this.pieDataQuoteType = [];
              this.listQuoteType = response.response.QUOTETYPE;
              this.TotalValueQuoteType = this.list.length;

              for (let key in this.listQuoteType) {
                if (this.quotetype === "QUOTETYPE") {
                  if (this.listQuoteType[key].Type === "CONTROLS QUOTES") {
                    this.listQuoteType[key].Type = "CONTROLS";
                  } else if (
                    this.listQuoteType[key].Type === "MAINTENANCE QUOTES"
                  ) {
                    this.listQuoteType[key].Type = "MAINTENANCE";
                  } else if (
                    this.listQuoteType[key].Type === "PROJECTS QUOTES"
                  ) {
                    this.listQuoteType[key].Type = "PROJECTS";
                  } else if (
                    this.listQuoteType[key].Type === "SERVICE QUOTES"
                  ) {
                    this.listQuoteType[key].Type = "SERVICE";
                  }
                }
                this.pieDataQuoteType.push({
                  category: this.listQuoteType[key].Type,
                  value: this.listQuoteType[key].Total,
                });
                this.TotalValueQuoteType =
                  this.TotalValueQuoteType + this.listQuoteType[key].Total;
              }
            }
            if (response.response.QUOTESTATUS) {
              this.listQuoteStatus = response.response.QUOTESTATUS;
              if (!!this.listQuoteStatus.length === true) {
                if (this.listQuoteStatus.length === 1) {
                  this.categories = ["APPROVED", "AWAITING APPROVAL"];
                  for (let key in this.listQuoteStatus) {
                    if (this.listQuoteStatus[key].Type === "APPROVED") {
                      this.ApproveData = [this.listQuoteStatus[key].Total];
                      this.AwaitingApprove = ["", 0];
                    } else if (
                      this.listQuoteStatus[key].Type === "AWAITING APPROVAL"
                    ) {
                      this.ApproveData = [0];
                      this.AwaitingApprove = [
                        "",
                        this.listQuoteStatus[key].Total,
                      ];
                    }
                    this.TotalStatus = this.listQuoteStatus[key].Total;
                  }
                } else if (this.listQuoteStatus.length === 2) {
                  this.categories = ["APPROVED", "AWAITING APPROVAL"];
                  for (let key in this.listQuoteStatus) {
                    if (this.listQuoteStatus[key].Type === "APPROVED") {
                      //  this.ApproveData.push(this.listQuoteStatus[key].Total);
                      this.ApproveData = [this.listQuoteStatus[key].Total];
                    } else if (
                      this.listQuoteStatus[key].Type === "AWAITING APPROVAL"
                    ) {
                      this.AwaitingApprove = [
                        "",
                        this.listQuoteStatus[key].Total,
                      ];
                    }
                    this.TotalStatus =
                      this.listQuoteStatus[key].Total + this.TotalStatus;
                  }
                }
              }
              if (!!this.listQuoteStatus.length === false) {
                this.categories = ["APPROVED", "AWAITING APPROVAL"];
                this.ApproveData.push(0);
                this.AwaitingApprove = ["", 0];
                this.TotalStatus = 0;
              } //case when there is no data neither in awaiting approval nor in awaiting
            } 
          }
          
        },
        (error) => {
          if (error) {
            this.shareData.ErrorHandler(error);
            // this.toastrService.error("response error !");
          }
        }
      );
  }

  // loadPriority() {
  //   this.coredata.getPiechartData(this.priority, this.locations, new Date(this.range["start"]), new Date(this.range["end"]), this.PieChartStatuses
  //   ).subscribe(response => {
  //     if (response != null && response != undefined) {
  //       if (response.response) {
  //         this.list = [];
  //         this.pieData = [];
  //         this.list = response.response;
  //         this.Total = this.list.length;
  //         for (let key in this.list) {
  //           this.pieData.push({ category: this.list[key].Type, value: this.list[key].Total })
  //           this.TotalValue = this.TotalValue + this.list[key].Total;
  //         }
  //       }
  //     }
  //   },
  //     error => {
  //       if (error) {
  //         this.shareData.ErrorHandler(error);
  //         // this.toastrService.error("response error !");
  //       }
  //     });
  // }

  // loadQuoteStatus() {
  //   this.coredata.getBarchartData(this.barType, this.locations, new Date(this.range["start"]), new Date(this.range["end"]), this.PieChartStatuses)
  //     .subscribe(response => {
  //       this.categories = [];
  //       this.ApproveData = [];
  //       this.AwaitingApprove = [""];
  //       this.Quotestatus = true;
  //       if (response.StatusCode == StatusCodes.OK && response != null) {
  //         this.listQuoteStatus = response.response;
  //         if (!!this.listQuoteStatus.length === true) {
  //           if (this.listQuoteStatus.length === 1) {
  //             this.categories = ['APPROVED', 'AWAITING APPROVAL'];
  //             for (let key in this.listQuoteStatus) {
  //               if (this.listQuoteStatus[key].Type === 'APPROVED') {
  //                 this.ApproveData = [this.listQuoteStatus[key].Total];
  //                 this.AwaitingApprove = ["", 0];
  //               }
  //               else if (this.listQuoteStatus[key].Type === 'AWAITING APPROVAL') {
  //                 this.ApproveData = [0];
  //                 this.AwaitingApprove = ["", this.listQuoteStatus[key].Total];
  //               }
  //               this.TotalStatus = this.listQuoteStatus[key].Total;
  //             }
  //           }
  //           else if (this.listQuoteStatus.length === 2) {
  //             this.categories = ['APPROVED', 'AWAITING APPROVAL'];
  //             for (let key in this.listQuoteStatus) {
  //               if (this.listQuoteStatus[key].Type === 'APPROVED') {
  //                 //  this.ApproveData.push(this.listQuoteStatus[key].Total);
  //                 this.ApproveData = [this.listQuoteStatus[key].Total];
  //               }
  //               else if (this.listQuoteStatus[key].Type === 'AWAITING APPROVAL') {
  //                 this.AwaitingApprove = ["", this.listQuoteStatus[key].Total];
  //               }
  //               this.TotalStatus = this.listQuoteStatus[key].Total + this.TotalStatus;
  //             }
  //           }
  //         }
  //         if (!!this.listQuoteStatus.length === false) {
  //           this.categories = ['APPROVED', 'AWAITING APPROVAL'];
  //           this.ApproveData.push(0);
  //           this.AwaitingApprove = ["", 0];
  //           this.TotalStatus = 0;
  //         }    //case when there is no data neither in awaiting approval nor in awaiting

  //       }
  //       else {
  //         // this.SharedDataService.ErrorHandler(response);
  //       }
  //     },
  //       error => {
  //         if (error) {
  //           this.shareData.ErrorHandler(error);
  //           // this.toastrService.error("response error !");
  //         }
  //       });
  // }

  labelContent(e: any) {
    return `${e.value}(${Math.round(e.percentage * 10000) / 100}%) `;
  }

  setDataInLocalStorage() {
    let start = this.range["start"];
    let end = this.range["end"];
    let locations = this.locations.toString();
    let dataToString = {
      start: start,
      end: end,
      locations: locations,
    };
    localStorage.setItem("location", JSON.stringify(dataToString));
  }

  CurrencyToString(value: any) {
    if (value == 0) {
      this.defaultCurrency = "";
      return 0;
    } else {
      // hundreds
      if (value <= 999) {
        this.defaultCurrency = " In Hundreds ";
        return Number.parseFloat(value.toString()).toFixed(2);
      }
      // thousands
      else if (value >= 1000 && value <= 999999) {
        this.defaultCurrency = " In Thousands ";
        return (
          Number.parseFloat((value / 1000).toString())
            .toFixed(2)
            .toString() + "K"
        );
      }
      // millions
      else if (value >= 1000000 && value <= 999999999) {
        this.defaultCurrency = " In Millions ";

        return (
          Number.parseFloat((value / 1000000).toString())
            .toFixed(2)
            .toString() + "M"
        );
      }
      // billions
      else if (value >= 1000000000 && value <= 999999999999) {
        this.defaultCurrency = " In Billions ";
        return (
          Number.parseFloat((value / 1000000000).toString())
            .toFixed(2)
            .toString() + "B"
        );
      } else return value;
    }
  }

  locationChange(event) {
    if (event != undefined) {
      this.locationDateFilterData = JSON.parse(event);
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations
          .split(",")
          .map((el) => parseInt(el));
      } else {
        this.locations = [];
      }
      this.range.start = this.locationDateFilterData.start;
      this.range.end = this.locationDateFilterData.end;
      // this.loadPriority();
      // this.loadQuoteTYPEData();
      this.loadOpenDebtChart();
      this.GetMostExpensiveEquipmentsChartData();
      //this.loadServiceCallsChart();
      this.loadServiceCallsChartByStatus();
      // this.loadQuoteStatus();
      this.loadQuotesSummary();
    }
  }
}
