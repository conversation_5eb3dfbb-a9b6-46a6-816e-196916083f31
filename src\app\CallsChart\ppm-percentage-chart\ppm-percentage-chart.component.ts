import { Component, OnInit, Input } from '@angular/core';
 import { SharedDataService } from "../../Services/shared-data.service";
import { Router } from '@angular/router';
@Component({
  selector: 'app-ppm-percentage-chart',
  templateUrl: './ppm-percentage-chart.component.html',
  styleUrls: ['./ppm-percentage-chart.component.scss']
})
export class PPMPercentageChartComponent implements OnInit {
  count: boolean = true;
  Locations: Array<number> = [];
  constructor( private shareData: SharedDataService,private router:Router) {

    this.shareData.removeBacklogData();
    this.shareData.customeridsasObservables.subscribe(data => {
      this.Locations = data;
      this.ApproveData = [];
      this.loadCount();
    });

  }
  @Input() startDate:any;
  @Input() endDate:any;
  public ApproveData = [];

  totalPPMPercentage: number;

  ngOnInit() {
    // this.loadCount();
  }

  onSeries(event)
  {
  let value="MCC";
   this.router.navigate(["/PPMCompletion",value]);
  }
  loadCount() {
    this.shareData.customerSummaryAsObservable.subscribe(response => {
      this.ApproveData = [];
      this.count = true;
      this.totalPPMPercentage = response.TotalMCCServices;
      this.ApproveData.push(this.totalPPMPercentage)
    });

  }
}
