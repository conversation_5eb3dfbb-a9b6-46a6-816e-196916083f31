import { Component, OnInit, Input } from '@angular/core';
 import { SharedDataService } from "../../Services/shared-data.service";
import { Router } from '@angular/router';
import { piechanrtInnerDataModel } from "./open-jc-chart.model";


@Component({
  selector: 'app-open-jc-chart',
  templateUrl: './open-jc-chart.component.html',
  styleUrls: ['./open-jc-chart.component.scss']
})
export class OpenJcChartComponent implements OnInit {
  public pieData: any = [];
  @Input() startDate: any;
  @Input() endDate: any;
  public model: any[] = piechanrtInnerDataModel;

  TotalControlsJCServices: number;
  TotalInstallJCServices: number;
  TotalServicesJCCallServices: number;
  TotalMaintenanceJCCallsServices: number;
  totalCount: number = 0;
  count: boolean = false;
  Locations: Array<number> = [];
  public ApproveData = [];
  constructor(private shareData: SharedDataService, private router: Router) {
    // this.shareData.Token$.subscribe()
    this.shareData.removeBacklogData();
    this.shareData.customeridsasObservables.subscribe(data => {
      this.Locations = data;
      this.ApproveData = [];
      this.loadCount();
    });

  }

  ngOnInit() {
  }
  onSeries(event) {
    this.router.navigate(["/QuotesInProgress", event.category]);

  }

  loadCount() {
    this.shareData.customerSummaryAsObservable.subscribe(response => {
      this.ApproveData = [];
      this.pieData = [];
      this.TotalControlsJCServices = response.TotalControlsJCServices;
      this.TotalInstallJCServices = response.TotalInstallJCServices;
      this.TotalServicesJCCallServices = response.TotalServicesJCCallServices;
      this.TotalMaintenanceJCCallsServices = response.TotalMaintenanceJCCallsServices;
      this.totalCount = this.TotalControlsJCServices + this.TotalInstallJCServices + this.TotalServicesJCCallServices;
      let KEYS = {
        Controls: this.TotalControlsJCServices,
        Install: this.TotalInstallJCServices,
        Service: this.TotalServicesJCCallServices,
      }
      let objectKeysArray = Object.keys(KEYS);
      if (KEYS.Controls != 0) {
        this.pieData.push({ category: objectKeysArray[0], value: KEYS.Controls })

      }
      if (KEYS.Install != 0) {
        this.pieData.push({ category: objectKeysArray[1], value: KEYS.Install });

      }
      if (KEYS.Service != 0) {
        this.pieData.push({ category: objectKeysArray[2], value: KEYS.Service });

      }
    this.count = true;

    });
  }
}
