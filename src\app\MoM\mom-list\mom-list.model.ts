export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}

export class MoMListEntities
{
Apologies: string;
Attendees: string;
CreatedBy: number;
CreationDate: Date;
HeldAt: string;
MeetingDate: Date;
MeetingID: number;
MeetingLocations: string;
MeetingTitle: string;
NextMeeting: string;
OwnerID: number;
State: string;
UpdatedBy: number;
UpdationDate: Date;
}