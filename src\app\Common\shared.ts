﻿//generic class to handle list values for drop down throughout application's typescript\

export class CustomerSummary {
  TotalControlsJCServices: number;
  TotalInstallJCServices: number;
  TotalMCCServices: number;
  TotalMaintenanceJCCallsServices: number;
  TotalQuotes: number;
  TotalServicesJCCallServices: number;
  TotalTMCompletionServices: number;
  TotalTMOpenServices: number;
  TotalTMServiceCalls: number;
  TotalJCServiceCalls: number;
  TotalOpenDebt: number;
  TotalSubContractor: number;
}

export enum StatusCodes {
  Continue = 100,
  SwitchingProtocols = 101,
  OK = 200,
  Created = 201,
  Accepted = 202,
  NonAuthoritativeInformation = 203,
  NoContent = 204,
  ResetContent = 205,
  PartialContent = 206,
  MultipleChoices = 300,
  Ambiguous = 300,
  MovedPermanently = 301,
  Moved = 301,
  Found = 302,
  Redirect = 302,
  SeeOther = 303,
  RedirectMethod = 303,
  NotModified = 304,
  UseProxy = 305,
  Unused = 306,
  TemporaryRedirect = 307,
  RedirectKeepVerb = 307,
  BadRequest = 400,
  Unauthorized = 401,
  PaymentRequired = 402,
  Forbidden = 403,
  NotFound = 404,
  MethodNotAllowed = 405,
  NotAcceptable = 406,
  ProxyAuthenticationRequired = 407,
  RequestTimeout = 408,
  Conflict = 409,
  Gone = 410,
  LengthRequired = 411,
  PreconditionFailed = 412,
  RequestEntityTooLarge = 413,
  RequestUriTooLong = 414,
  UnsupportedMediaType = 415,
  RequestedRangeNotSatisfiable = 416,
  ExpectationFailed = 417,
  UpgradeRequired = 426,
  InternalServerError = 500,
  NotImplemented = 501,
  BadGateway = 502,
  ServiceUnavailable = 503,
  GatewayTimeout = 504,
  HttpVersionNotSupported = 505,
}

export class CodeData {
  public CodeID: number;
  public CodeTypeID: number;
  public CodeName: string;
  public Code: string;
  public Description: string;
}

export enum MomStatus {
  Open = 1,
  CarriedFW = 2,
  OnGoing = 3,
  Complete = 4,
  Comment = 5,
}

export class TokenDecodedEntities {
  EmailID: string;
  EmployeeID: string;
  Name: string;
  OrgName: string;
  Role: number;
  RoleName: string;
  StateCd: number;
  UserID: string;
  aud: string;
  exp: number;
  iss: string;
  nbf: number;
}

export class TokenModel {
  UserID: string;
  Name: string;
  Role: string;
  EmailID: string;
  StateCd: number;
  RoleName: string;
  EmployeeID: string;
  OrgName: string;
  IsExternalUser: string;
}

export class MFAModel {
    loginUsername: string;
    password: string;
    OTP: string="";
}

export class otpToken{
  public RecaptchaToken: any;
  public userName:string;
  public requestType="Web";
  public IsAeUser: boolean =false;
}