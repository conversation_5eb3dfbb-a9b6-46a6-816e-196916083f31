export class QuotePriorityClass {
    constructor() { }
    QuotePriorityData = [
        { PriorityID: 20, PriorityLevel: "LOW" },
        { PriorityID: 21, PriorityLevel: "MEDIUM" },
        { PriorityID: 22, PriorityLevel: "HIGH" },
        { PriorityID: 23, PriorityLevel: "COMPLIANCE" }
    ];
}
export class QuoteDetailsListModel {
    AccountManager: string;
    ApprovedDate: string;
    Customer: string;
    Description: string;
    Exclusion: null
    Introduction: null
    LocationName: string;
    PriceTotal: string;
    PriorityCd: 21
    QuoteTypeCd: number;
    QuoteReference: string;
    QuoteStatus: string;
    SOW: string;
    Subject: string;
    WorkOrderNo: string;
    Revision: number;
    CustNmbr: string;
    HeaderFooter: string;
    sectionDetails: sectionDetailsModel[];
    AccountManagerID: number;
    IsRestricted: boolean;
}
export enum QuoteTypes {
    ControlsQuote = 41,
    MaintainenceQuote = 42,
    ProjectsQuote = 43,
    ServiceQuotes = 44,
    CMPS = 45,
    BuenoMinors = 46,
}
export class sectionDetailsModel {
    SectionID: number;
    Equipment: string;
    Preferred: boolean;
    PriceTotal: string;
    Title: string;
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}

export class ReasonEntity {
    CodeID: number;
    CodeTypeID: number;
    CodeName: string;
    Code: string;
    Description: string;
}

export class QuoteDeclineEntity {
    QuoteID: number;
    StatusReasonCd: number;
    ClosureNotes: string;
}