export class OpenCallsListModel {
    CallStatus: string;
    DATE: Date;
    Location: string;
    PO: string;
    Resolution: string;
    ServiceCallID: string;
    ServiceDesciption: string;
    CallType:string;
    LocationName:string;
}
export enum ServiceCallsChartKeys {
    "Open" = "Open Calls",
    "Complete" = "Complete Calls",
    "Invoiced" ="Invoiced Calls"
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}