<div class="page login-page">
  <div class="container">
    <div class="form-outer text-center align-items-center justify-content-center">
      <div class="form-inner">
        <div class="logo">
          <img src="../../../assets/images/logo.png" class="logoHeight" alt="" />
        </div>
        <div class="row">
          <div class="col-md-12 img-center">
            <form #form="ngForm" (ngSubmit)="login(form.value)" [formGroup]="LoginForm" class="text-left form-validate">
              <div class="form-group-material">
                <label for="email">User Name:</label>

                <input id="login-username" type="text" name="loginUsername" class="input-material padding-left"
                  formControlName="loginUsername" [ngClass]="{
                    'is-invalid':
                      submitted && LoginControlValidator.loginUsername.errors
                  }" />
              </div>
              <span *ngIf="submitted && LoginControlValidator.loginUsername.errors" class="help-block">
                <div *ngIf="LoginControlValidator.loginUsername.errors.required">
                  Username is required
                </div>
              </span>
              <div *ngIf='!isPasswordChange' class="form-group-material">
                <label for="pwd">Password:</label>
                <!-- <input id="login-password" type="password" minlength="4" name="loginPassword"
                  class="input-material padding-left" ngModel required /> -->
                <input type="password" name="password" formControlName="password" class="input-material padding-left"
                  [ngClass]="{
                    'is-invalid':
                      submitted && LoginControlValidator.password.errors
                  }" id="password" />
              </div>
              <span *ngIf="submitted && LoginControlValidator.password.errors" class="help-block">
                <div *ngIf="LoginControlValidator.password.errors.required && !isPasswordChange">
                  Password is required
                </div>
                <div *ngIf="LoginControlValidator.password.errors.minlength && !isPasswordChange">
                  Password must be at least 10 characters
                </div>
              </span>
              <div class="form-group-material" *ngIf="isWrongCredential === true">
                <re-captcha #captchaRef="reCaptcha" (resolved)="resolved($event)" name="re-captcha"
                  siteKey="6LdflGUaAAAAAG4bbLv5tg7u_yzvc-zQEyngvtyF"></re-captcha>
              </div>

              <div *ngIf='!isPasswordChange' class="form-group text-center">
                <button type="submit" class="btn btn-unisaf">Log in »</button>
              </div>

              <div *ngIf='!isPasswordChange' style="font-size: 17px;
              text-align: center;
              margin-top: 19px;
              margin-bottom: -10px;">
                Airmaster employees please<a (click)="redirectToAE()" style="color: #2d62e2;
                  cursor: pointer;
                  margin-left: 5px;">login here</a>
              </div>

              <div *ngIf='isPasswordChange' class="form-group text-center">
                <button type="submit" class="btn btn-unisaf">submit »</button>
              </div>


              <!-- <div class="form-group text-center">
                <button type="button" (click)="Verification()"  class="btn btn-unisaf">Verfication</button>
              </div>
              <div class="form-group text-center">
                <button type="button" (click)="ChangePassword()"  class="btn btn-unisaf">Change Password</button>
              </div> -->
              <div *ngIf='!isPasswordChange' class="form__buttons">

                <a id="resCode" (click)="ChangePassword()">
                  Forgot Password</a>

              </div>
              <div *ngIf='isPasswordChange' class="form__buttons">

                <a id="resCode" (click)="Back()">
                  Back</a>

              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>