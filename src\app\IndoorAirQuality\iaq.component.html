<admin-header (valueChange)='locationChange($event)'></admin-header>
<app-side-nav></app-side-nav>
<section class="dashboard-header section-padding section-top-dashboard"
        [style]="collapsedSections.Temperature && collapsedSections.Humidity && collapsedSections.CO2 ?
        'background-color: #F5F3EF;' : 'height: auto; background-color: #F5F3EF;'">
    <div class="container-fluid" style="padding: 0 1rem;">
        <!-- <div *ngIf="!temperatureDataAvailable && !humidityDataAvailable && !co2DataAvailable"
            class="d-flex justify-content-center">
            <span style="font-size: 1.2vw !important; font-family: 'Roboto'; color: #000 !important; margin-top: 26vh;">
                No IAQ Data Available
            </span>
        </div> -->
        <ng-container *ngFor="let panel of panels">
            <div class="panel-container">
                <button kendoButton look="outline" class="panel-btn"
                    [style]="collapsedSections[panel.key] ? 'border-radius: 6px' : 'border-bottom: 0'"
                    (click)="toggleCollapse(panel.key)">
                    <span>{{ panel.title }}</span>
                    <label class="switch">
                      <input type="checkbox" [checked]="!collapsedSections[panel.key]" (change)="toggleCollapse(panel.key)"/>
                      <span class="slider"></span>
                    </label>
                </button>

                <div [@expandCollapse]="collapsedSections[panel.key] ? 'collapsed' : 'expanded'" class="collapse-panel">
                  <ng-container *ngIf="panel.key === 'Temperature'">
                    <div class="graphContainer">
                      <div class="row align-items-md-stretch section_3">
                        <div class="col-md-12 problem_proactively " style="border-radius: 0; height: 430px;">
                          <div class="card bar-chart-example screenScroll "
                            style="height: 100%; padding: 0;">

                            <div style="padding: 2px;">
                            <h3 style="font-weight: bold;
                              color: black;
                              font-family: 'Roboto';
                              font-size: 1.2vw !important;
                              margin-left: 3px">Site Comfort Performance Trend</h3>
                            <h3 *ngIf="getSiteComfortData?.length == 0" style="position: absolute;
                              z-index: 1;
                              top: 40%;
                              left: 40%;
                              font-size: 30px;
                              color: #888;">Not Available</h3>

                            <div class="tabs" *ngIf="getSiteComfortData?.length > 0"
                              style="width: 190px; font-family: Calibri Light !important;">
                              <p style="margin-right: 5px;">Period:</p>
                              <p (click)="changeMonthTabSiteComfort('1M')"
                                [ngStyle]="{'border-bottom': IsOneMonthSiteComfort ? '2px solid #888' : 'none' }">
                                1M</p>
                              <p>|</p>
                              <p (click)="changeMonthTabSiteComfort('3M')"
                                [ngStyle]="{'border-bottom': IsThreeMonthSiteComfort ? '2px solid #888' : 'none' }">
                                3M</p>
                              <p>|</p>
                              <p (click)="changeMonthTabSiteComfort('6M')"
                                [ngStyle]="{'border-bottom': IsSixMonthSiteComfort ? '2px solid #888' : 'none' }">
                                6M</p>
                              <p>|</p>
                              <p (click)="changeMonthTabSiteComfort('13M')"
                                [ngStyle]="{'border-bottom': IsTwelveMonthComfort ? '2px solid #888' : 'none' }">
                                13M</p>
                            </div>

                            <div style="position: absolute; top: 25px; left: 39%; font-family: Calibri Light !important;"
                              *ngIf="getSiteComfortData?.length > 0">
                                            <div class="tabs" style="justify-content: flex-start; width: 240px;">
                                <p style="margin-right: 5px;">Moving Trendline:</p>
                                <p (click)="changeMovingAverageTrending('3D')" style="margin-right: 2px;"
                                  [ngStyle]="{'border-bottom': IsThreeDayMovingAverage ? '2px solid #888' : 'none' }">3D</p>
                                <p>|</p>
                                <p (click)="changeMovingAverageTrending('1W')" style="margin-left: 2px; margin-right: 2px;"
                                  [ngStyle]="{'border-bottom': IsOneWeekMovingAverage ? '2px solid #888' : 'none' }">1W</p>
                                <p *ngIf="!IsOneMonthSiteComfort">|</p>
                                <p *ngIf="!IsOneMonthSiteComfort" (click)="changeMovingAverageTrending('1M')" style="margin-left: 2px;"
                                  [ngStyle]="{'border-bottom': IsOneMonthMovingAverage ? '2px solid #888' : 'none' }">1M</p>
                                <p *ngIf="IsTwelveMonthComfort">|</p>
                                <p *ngIf="IsTwelveMonthComfort" (click)="changeMovingAverageTrending('3M')" style="margin-left: 2px;"
                                  [ngStyle]="{'border-bottom': IsThreeMonthMovingAverage ? '2px solid #888' : 'none' }">3M</p>

                            </div>
                            </div>

                            <div *ngIf="getSiteComfortData?.length > 0" style="position: absolute; top: 26px; right: 12px; font-weight: bold;
                                font-size: 0.98vw;">
                              <span style="font-family: Roboto; color: rgb(13, 192, 255);">
                                {{ getTwelveMonthChangePercentage >= 0 ? '+' : '' }}{{ getTwelveMonthChangePercentage | number:'1.0-2' }}
                                %</span><span style="margin-left: 10px; font-family: Roboto;">{{movingAverageCount}} Month Change</span>
                            </div>

                            <kendo-chart [seriesColors]="['#5a5a5a']" style="height: 310px" *ngIf="getSiteComfortData?.length == 0"
                              class="site_comfort_chart">
                              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
                              <kendo-chart-value-axis>
                                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                                  [minorTicks]="{ visible: false }">
                                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                                  </kendo-chart-value-axis-item-labels>
                                </kendo-chart-value-axis-item>
                              </kendo-chart-value-axis>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item>
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                                    [rotation]="-30">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-legend>
                              </kendo-chart-legend>
                              <kendo-chart-series>
                                <kendo-chart-series-item type="column" [data]="OneMonthSiteData" field="TotalPctComfort"
                                  [missingValues]="'interpolate'" categoryField="AllDates">
                                </kendo-chart-series-item>
                              </kendo-chart-series>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item type="category" [labels]="{format: 'EEEEE', font: '5' , skip: '0'}"
                                  [majorGridLines]="{ visible: false }">
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-tooltip>
                                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                                  {{ category | date }}: {{ value }}
                                </ng-template>
                              </kendo-chart-tooltip>
                            </kendo-chart>

                            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 310px"
                              *ngIf="IsOneMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
                              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
                              <kendo-chart-value-axis>
                                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                                  [minorTicks]="{ visible: false }" [labels]="{font: '13px Roboto !important'}">
                                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                                  </kendo-chart-value-axis-item-labels>
                                </kendo-chart-value-axis-item>
                              </kendo-chart-value-axis>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item>
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                                    [rotation]="-30">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-legend>
                              </kendo-chart-legend>
                              <kendo-chart-series>
                                <kendo-chart-series-item type="column" [data]="OneMonthSiteData" field="TotalPctComfort"
                                  categoryField="AllDates">
                                </kendo-chart-series-item>

                                <kendo-chart-series-item type="line" [data]="getOneMonthMovingAverage" field="TotalPctComfort"
                                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                                  <kendo-chart-series-item-tooltip>
                                    <ng-template let-value="value" let-category="category">
                                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                                    </ng-template>
                                  </kendo-chart-series-item-tooltip>
                                </kendo-chart-series-item>

                              </kendo-chart-series>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item type="category" [labels]="{format: 'EEEEE', font: '5' , skip: '0'}"
                                  [majorGridLines]="{ visible: false }">
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important"
                                    [content]="labelContentOfOneMonth"></kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-tooltip>
                                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                                  {{ category | date }}: {{ value }}
                                </ng-template>
                              </kendo-chart-tooltip>
                            </kendo-chart>

                            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 310px"
                              *ngIf="IsThreeMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
                              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
                              <kendo-chart-value-axis>
                                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                                  [minorTicks]="{ visible: false }">
                                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                                  </kendo-chart-value-axis-item-labels>
                                </kendo-chart-value-axis-item>
                              </kendo-chart-value-axis>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item>
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                                    [rotation]="-30">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-legend>
                              </kendo-chart-legend>
                              <kendo-chart-series>
                                <kendo-chart-series-item type="column" [data]="ThreeMonthSiteData" field="TotalPctComfort"
                                  [missingValues]="'interpolate'" categoryField="AllDates">
                                </kendo-chart-series-item>

                                <kendo-chart-series-item type="line" [data]="getThreeMonthMovingAverage" field="TotalPctComfort"
                                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                                  <kendo-chart-series-item-tooltip>
                                    <ng-template let-value="value" let-category="category">
                                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                                    </ng-template>
                                  </kendo-chart-series-item-tooltip>
                                </kendo-chart-series-item>

                              </kendo-chart-series>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                                type="category" [majorGridLines]="{ visible: false }">
                                <kendo-chart-category-axis-item-labels [content]="customDivisionsForThreeMonths"></kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>

                              <kendo-chart-tooltip>
                                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                                  {{ category | date }}: {{ value }}
                                </ng-template>
                              </kendo-chart-tooltip>
                            </kendo-chart>

                            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 310px"
                              *ngIf="IsSixMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
                              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
                              <kendo-chart-value-axis>
                                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                                  [minorTicks]="{ visible: false }">
                                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                                  </kendo-chart-value-axis-item-labels>
                                </kendo-chart-value-axis-item>
                              </kendo-chart-value-axis>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item>
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                                    [rotation]="-30">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-legend>
                              </kendo-chart-legend>
                              <kendo-chart-series>
                                <kendo-chart-series-item type="column" [data]="SixMonthSiteData" field="TotalPctComfort"
                                  [missingValues]="'interpolate'" categoryField="AllDates">
                                </kendo-chart-series-item>

                                <kendo-chart-series-item type="line" [data]="getSixMonthMovingAverage" field="TotalPctComfort"
                                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                                  <kendo-chart-series-item-tooltip>
                                    <ng-template let-value="value" let-category="category">
                                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                                    </ng-template>
                                  </kendo-chart-series-item-tooltip>
                                </kendo-chart-series-item>

                              </kendo-chart-series>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                                  type="category" [majorGridLines]="{ visible: false }">
                                  <kendo-chart-category-axis-item-labels [content]="customDivisionsForSixMonths"></kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>

                              <kendo-chart-tooltip>
                                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                                  {{ category | date }}: {{ value }}
                                </ng-template>
                              </kendo-chart-tooltip>
                            </kendo-chart>

                            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 310px"
                              *ngIf="IsTwelveMonthComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
                              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
                              <kendo-chart-value-axis>
                                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                                  [minorTicks]="{ visible: false }">
                                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                                  </kendo-chart-value-axis-item-labels>
                                </kendo-chart-value-axis-item>
                              </kendo-chart-value-axis>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item>
                                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                                    [rotation]="-30">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>
                              <kendo-chart-legend>
                              </kendo-chart-legend>
                              <kendo-chart-series>
                                <kendo-chart-series-item type="column" [data]="TwelveMonthSiteData" field="TotalPctComfort"
                                  [missingValues]="'interpolate'" categoryField="AllDates">
                                </kendo-chart-series-item>

                                <kendo-chart-series-item type="line" [data]="getTwelveMonthMovingAverage" field="TotalPctComfort"
                                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                                  <kendo-chart-series-item-tooltip>
                                    <ng-template let-value="value" let-category="category">
                                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                                    </ng-template>
                                  </kendo-chart-series-item-tooltip>
                                </kendo-chart-series-item>

                              </kendo-chart-series>
                              <kendo-chart-category-axis>
                                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                                  type="category" [majorGridLines]="{ visible: false }">
                                  <kendo-chart-category-axis-item-labels [content]="customDivisionsForTwelveMonths">
                                  </kendo-chart-category-axis-item-labels>
                                </kendo-chart-category-axis-item>
                              </kendo-chart-category-axis>

                              <kendo-chart-tooltip>
                                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                                  {{ category | date }}: {{ value }}
                                </ng-template>
                              </kendo-chart-tooltip>
                            </kendo-chart>

                            <ul *ngIf="getSiteComfortData?.length > 0" class="legend" style="margin-top: 15px;">
                              <li class="legend-item" (click)="toggleSeries()"
                                style="font-size: 15px; color: #888888; font-family: Calibri Light;">
                                <span class="legend-marker"></span>
                                Daily building comfort during occupancy (zones within 2°c of set point, average across all zones)
                              </li>
                            </ul>
                          </div>
                        </div>

                      </div>
                      </div>
                    </div>

                    <div class="graphContainer">
                      <span>
                          Zone Temperature Distribution
                      </span>
                      <div class="tabs">
                          <p>Period: </p>
                          <!-- <p [class.active-tab]="selectedPeriods[panel.key]['BoxWhisker'] === 'Default'" (click)="updatePeriod(panel.key, 'BoxWhisker','Default')">Default</p>
                          <p> | </p> -->
                          <p [class.active-tab]="selectedPeriods[panel.key]['BoxWhisker'] === '1M'" (click)="updatePeriod(panel.key, 'BoxWhisker','1M')">1M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key]['BoxWhisker'] === '3M'" (click)="updatePeriod(panel.key, 'BoxWhisker','3M')">3M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key]['BoxWhisker'] === '6M'" (click)="updatePeriod(panel.key, 'BoxWhisker','6M')">6M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key]['BoxWhisker'] === '12M'" (click)="updatePeriod(panel.key, 'BoxWhisker','12M')">12M</p>
                        </div>
                        <div id="TemperatureBoxWhiskerContainer"></div>
                    </div>

                    <div class="graphContainer">
                        <span>
                          Individual Zone Performance
                        </span>
                        <div id="TemperatureDumbbellContainer"></div>

                        <div style="height: 472px; margin-top: 10px;">
                        <kendo-grid style="height: 100%;margin-top: -1vh" [data]="equipComfortGrid" [selectable]="true"
                          class="gridFontStyle awaiting-grid scroll-container" (selectionChange)="isRowSelected($event)"
                          [sortable]="{ allowUnsort: true, mode: 'multiple' }" [sortable]="{allowUnsort: true, mode:'multiple'}"
                          (dblclick)="getDetail()" [skip]="EquipComfortState.skip" [sort]="EquipComfortState.sort"
                          [filter]="EquipComfortState.filter" [resizable]="true" filterable="menu"
                          (dataStateChange)="dataStateEquipmentComfort($event)" [navigable]="true">

                          <kendo-grid-column field="Building" title="Building" width="160" [minResizableWidth]="160">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.Building}}
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="Equipment" title="Equipment" width="145" [minResizableWidth]="120">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.Equipment }}
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="PerOfOccupancy" title="% of Occupancy" width="145" [minResizableWidth]="120">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.PerOfOccupancy }}<span>&nbsp;%</span>
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="TotalHours" title="Total (Hrs)" width="115" [minResizableWidth]="110">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.TotalHours | number: '1.0-0'}}
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="TooHotHours" title="Too Hot (Hrs)" width="123" [minResizableWidth]="110">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.TooHotHours | number: '1.0-0'}}
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="TooColdHours" title="Too Cold (Hrs)" width="128" [minResizableWidth]="110">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <div style="font-family: Calibri Light !important;">
                                {{dataItem.TooColdHours | number: '1.0-0'}}
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="TasksCompleted" title="Tasks Completed" width="220" [minResizableWidth]="120">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <ng-container *ngIf="dataItem?.TasksCompleted !== null; else noTasks">
                                <a class="taskCompleted"
                                  (click)="GoToCompletedTable(dataItem)">{{'View Tasks Completed' + ' &ndash; ' + dataItem?.TasksCompleted }}</a>
                              </ng-container>
                              <ng-template #noTasks>--</ng-template>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column field="BacklogTasks" title="Tasks In Backlog" width="235" [minResizableWidth]="120">
                            <ng-template kendoGridCellTemplate let-dataItem>
                              <ng-container *ngIf="dataItem?.BacklogTasks !== null; else noTasks">
                                <a class="taskBacklogged"
                                  (click)="GoToBacklogTable(dataItem)">
                                  {{'View Tasks In The Backlog' + ' &ndash; ' + dataItem.BacklogTasks }}
                                </a>
                              </ng-container>
                              <ng-template #noTasks>--</ng-template>
                            </ng-template>
                          </kendo-grid-column>

                        </kendo-grid>
                      </div>
                    </div>

                  </ng-container>
                  <ng-container *ngFor="let graph of panel.graphs">
                      <div class="graphContainer" >
                        <span>
                          {{ graph.graphTitle }}
                        </span>
                        <div class="tabs">
                          <p>Period: </p>
                          <!-- <p [class.active-tab]="selectedPeriods[panel.key][graph.graphType] === 'Default'" (click)="updatePeriod(panel.key, graph.graphType,'Default')">Default</p>
                          <p> | </p> -->
                          <p [class.active-tab]="selectedPeriods[panel.key][graph.graphType] === '1M'" (click)="updatePeriod(panel.key, graph.graphType,'1M')">1M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key][graph.graphType] === '3M'" (click)="updatePeriod(panel.key, graph.graphType,'3M')">3M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key][graph.graphType] === '6M'" (click)="updatePeriod(panel.key, graph.graphType,'6M')">6M</p>
                          <p> | </p>
                          <p [class.active-tab]="selectedPeriods[panel.key][graph.graphType] === '12M'" (click)="updatePeriod(panel.key, graph.graphType,'12M')">12M</p>
                        </div>
                        <div id="{{ panel.key }}{{graph.graphType}}Container"></div>
                      </div>
                  </ng-container>
                </div>
            </div>
        </ng-container>
    </div>




</section>
<admin-footer class="dashboardFooterCss">
</admin-footer>

<!-- <kendo-dialog *ngIf="showDialog" (close)="closeDialog()">
  <kendo-dialog-titlebar class="dialog-title">
    <div>
      {{ zoneTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="dialog-content">
    <p>Open Tasks</p>

    <div class="task-grid header">
        <div>Task Type</div>
        <div>Mechanical / Controls</div>
        <div>Thermal Comfort</div>
        <div>Equipment Reliability</div>
        <div>Place Call</div>
        <div>Prioritisation</div>
    </div>

    <div class="task-grid row">
        <a href="#" class="task-link">AHU cooling coil starved</a>
        <span class="task-type">Mechanical</span>
        <span class="badge severe">Severe</span>
        <span class="badge minor">Minor</span>
        <span class="badge place">Place</span>
        <kendo-switch [(ngModel)]="switchState"></kendo-switch>
    </div>

    <button kendoButton look="outline" class="history-btn"
        [style]="historySectionCollapsed ? 'border-radius: 6px' : 'border-bottom: 0'"
        (click)="toggleHistoryCollapse()">
        <span>History</span>
        <kendo-icon [name]="historySectionCollapsed ? 'plus' : 'minus'" class="float-right"></kendo-icon>
    </button>
    <div [@expandCollapse]="historySectionCollapsed ? 'collapsed' : 'expanded'" class="collapse-panel">
        <div class="history-section">
            <div *ngFor="let history of historyData" class="history-item">
                <div class="history-left">
                    <span class="job-link">{{ history.jobNumber }}</span>
                    <div class="issue-link">
                        <a href="#">{{ history.issue }}</a>
                    </div>
                </div>

                <div class="history-right">
                    <div class="technician-header">
                        Technician Summary
                    </div>
                    <div class="technician-summary">
                        {{ history.summary }}
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</kendo-dialog> -->

<!-- iaq.component.html -->

<div
  class="modal fade"
  id="boxWhiskerModal"
  tabindex="-1"
  aria-labelledby="zoneModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg modal-dialog-centered">
    <div class="modal-content zone-modal-content">

      <!-- Header -->
      <div class="modal-header zone-modal-header">
        <h5 class="modal-title" id="zoneModalLabel">{{ zoneTitle }}</h5>
        <button
          type="button"
          class="btn-close zone-modal-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <hr style="border-color: #575757; margin: 0 1rem;">
      <!-- Body -->
      <div class="modal-body zone-modal-body">
        <section class="tasks-section">
          <h6 class="section-title">Tasks in Workflow</h6>
          <ul class="list-group workflow-list mb-4">
            <li
              *ngFor="let t of historyData"
              class="list-group-item workflow-item">
              <div class="item-left">
                <span class="job-number">{{ t.jobNumber }}</span>
                <span class="description">{{ t.issue }}</span>
              </div>
              <span class="badge status-badge">{{ t.summary }}</span>
            </li>
          </ul>
        </section>

        <section class="tasks-section">
          <h6 class="section-title">Tasks in Backlog</h6>
          <ul class="list-group backlog-list">
            <!-- <li
              *ngFor="let b of tasksInBacklog; let i = index"
              class="list-group-item backlog-item d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center gap-2">
                  <span class="description">{{ b.description }}</span>
                  <span class="info-icon" ><i class="bi bi-info-circle-fill"></i></span>
                </div>

              <div class="d-flex align-items-center gap-2">
                <button class="btn view-data-btn" (click)="viewData(b)">View Data</button>
                <button class="btn place-call-btn" (click)="placeCall(b)">Place Call</button>
                <div class="form-check form-switch prioritise-switch">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    [(ngModel)]="b.prioritised"
                    id="prio-{{ i }}"
                  />
                  <label class="form-check-label" for="prio-{{ i }}">Prioritise</label>
                </div>

              </div>
            </li> -->
          </ul>
        </section>
      </div>
    </div>
  </div>
</div>




