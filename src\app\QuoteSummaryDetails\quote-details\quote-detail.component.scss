.button-top-margin {
  float: right;
  margin-top: 19px;
  position: relative;
  right: 7px;
}
.header-section
{
  margin-bottom: 0% !important;
}
section form {
  padding: 0px;
}
.quoteDetailFooter {
  position: absolute;
  left: 0;
  right: 0;
}
.button-btm-margin {
  float: right;
  position: relative;
  margin-top: -40px;
  right: 20px;
}
.doc-dialog-content {
  margin-left: 45px !important;
  vertical-align: middle;
}
.k-textbox {
  width: 59% !important;
  position: relative;
  bottom: 13px;
}

// FK: start decline popup
.buttonFooter {
  background-color: #4ab4de !important;
  color: #ffffff !important;
}

.buttonFooter:hover {
  background-color: #ffffff !important;
  color: #000000 !important;
  cursor: pointer;
}

.buttonFooterCancel {
  background-color: #d9534f !important;
  color: #ffffff !important;
}

.buttonFooterCancel:hover {
  background-color: #ffffff !important;
  color: #000000 !important;
  cursor: pointer;
}
// end decline popup

@media only screen and (min-width: 767px) {
  .font-default {
    width: 163px;
  }
}

@media only screen and (max-width: 767px) {
  .btn-sm-position {
    margin-bottom: 33px;
  }
 
}
 
@media (max-width: 991px)
{
  p {
    font-size: 11px !important;
  }
}

.k-textarea {
  width: 100%;
}

.introduction {
  font-family: calibri light , "Roboto";
}

.font-default {
  font-family: calibri light , "Roboto";
  font-weight: 700;
}
.p-3 {
  overflow-y: scroll;
  padding: 2%;
  /* overflow: scroll; */
  height: 187px;
  border: 1px solid #80808030;
  background: white;
}
.p-0 {
  padding: 0px;
}
.margin-auto {
  margin-bottom: 65px;
}

@media only screen and (max-width: 767px) {
  .button-top-margin {
    margin-top: 3%;
  }
}

.font-default-size {
  font-family: calibri light , "Roboto";
}
.margin-top-section
{
  margin-top: 180px;
}

@media only screen and (max-width: 770px)
{
  .k-form .k-form-field > input {
    width: 100% !important;
    font-size: 11px !important;
}
label {
  font-size: 11px !important;
}
.k-textbox {
  width: 100% !important;
  font-size: 11px !important;
 
}
.div-center {
  position: relative !important;
  left: 0% !important;
}
kendo-dropdownlist
{
  width:100% !important;
  font-size: 11px !important;
}
.md-hide
{
  display: block !important;
  position: relative;
  bottom: 7px;
}
.md-lg
{
  display: none !important;
}
}
@media only screen and (max-width: 1130px)
{
  .margin-top-section
  {
    margin-top: 0px !important;
  }

}
.md-hide
{
  display: inline;
}
@media only screen and (min-width: 770px)
{
  .md-hide
{
  display: none !important;
 
}
.md-lg
{
  display: block !important;
}
.div-center {
  position: relative;
  left: 24%;
}

}

//FK: decline reason dropdown
kendo-dropdownlist.k-widget.k-dropdown.k-header.declineReason-dropdown {
  width: 270px !important;
  border: 1px solid rgba(0,0,0,0.08) !important;
  border-radius: 3px !important;
  background-color: #f6f6f6 !important;
  background-image: linear-gradient(#f6f6f6,#f1f1f1);
  border-width:1px;
  height: 34px;
}

kendo-dropdownlist.k-widget.k-dropdown.k-header.declineReason-dropdown:hover {
  background-color: #10101017 !important;
  background-image: linear-gradient(#10101017,#f1f1f1);
}