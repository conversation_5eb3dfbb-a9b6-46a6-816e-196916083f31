import { Component, OnInit, HostListener } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import * as moment from 'moment';
import { QuoteSummaryListModel, locationDateFilterEntity } from "./outstanding-quotes.model";
import { CoreDataService } from "../../Services/core-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import { GridDataResult, DataStateChangeEvent, FilterService } from '@progress/kendo-angular-grid';
import { SharedDataService } from '../../Services/shared-data.service';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'admin-outstanding-quotes',
  templateUrl: './outstanding-quotes.component.html',
  styleUrls: ['./outstanding-quotes.component.scss']
})

export class OutstandingQuotes implements OnInit {
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  locationsIds: string;
  PieChartStatuses = "9,184,315";
  originalDate = new Date();
  startDate: string;
  endDate: string;
  start: Date;
  end: Date;
  quoteList: QuoteSummaryListModel[] = [];
  public range: any;
  QuoteSummaryList: QuoteSummaryListModel[] = [];
  SelectedRowData: any;
  count = 0;
  value: string;
  gridData: GridDataResult;
  key;
  public state: State = {
    skip: 0,
    //take: 15,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public DataList: any = [];
  public filterGrid: any[];
  pagesize: number = 20;
  private dropdownFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  public quoteTypes: any[];
  public quoteStatuses: any[];
  public priorities: any[];
  fromDateFilter:any;
  public division: any[];
  constructor(private coredataservice: CoreDataService,
    private shareData: SharedDataService,
    private titleService: Title,
    private route: ActivatedRoute, private router: Router) {
      this.shareData.removeBacklogData();
      let hostNameUrl = window.location.hostname.toString();
      if(hostNameUrl == 'flow.controlco.nz'){
        this.PieChartStatuses = "9,184,315";
      }else if(hostNameUrl == 'flow.optimumair.co.nz'){
        this.PieChartStatuses = "9,184,315";
      }else if(hostNameUrl == 'flow.airmasterfire.com.au'){
        this.PieChartStatuses = "9,184,304";
      }else if(hostNameUrl == 'flow.airmaster.com.au'){
        this.PieChartStatuses = "9,184,371";
      }else {
         this.PieChartStatuses = "9,184,315";
      }

    this.allData = this.allData.bind(this);
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.locationDateFilterData === null) {
      if(this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == ""){
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else{
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locationsIds = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
  }

  isRowSelected(event: any): void {
    if (event != undefined) {
      this.SelectedRowData = event;
    }
  }

  getDetail() {
    if (this.SelectedRowData != undefined) {
      this.coredataservice.getQuoteDetail(this.SelectedRowData.selectedRows[0].dataItem.QuoteID).subscribe(response => {
        response.QuoteID=this.SelectedRowData.selectedRows[0].dataItem.QuoteID
        this.shareData.quoteDetail.next(response);
      },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        });
      this.router.navigate(["/QuoteDetail"]);
    }
  }

  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;
    }
    this.key = this.route.snapshot.paramMap.get('id');
    this.value = this.route.snapshot.paramMap.get('value');
    this.start = new Date(this.startDate);
    this.end = new Date(this.endDate);
    if (this.key) {
      this.loadData(this.key, this.value, this.locationsIds, this.range);
    }
  }

  setDataInLocalStorage() {
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locationsIds
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }

  loadData(key, value, locationsIds, range) {
    let id: string;
    if (locationsIds === undefined) {
      id = "";
    }
    else {
      id = locationsIds;
    }
    this.coredataservice.getOutstandingQuotes(id, new Date(range.start), new Date(range.end), this.PieChartStatuses).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        this.dataGrid = res.response;
        this.dataGrid.map(element => {
          element.CreationDate = this.GetFormattedDate(element.CreationDate);
          element.PriceTotal = element.PriceTotal === null ? "0.00" : parseFloat(element.PriceTotal.toString()).toFixed(2);
        });
        this.division = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Division === x.Division) === idx);
        this.division = this.division.filter(function (el) {
          return el.Division != "";
        });
        this.quoteStatuses = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.QuoteStatus === x.QuoteStatus) === idx);
        this.quoteStatuses = this.quoteStatuses.filter(function (el) {
          return el.QuoteStatus != "";
        });
        this.priorities = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Priority === x.Priority) === idx);
        this.priorities = this.priorities.filter(function (el) {
          return el.Priority != "";
        });
       
        this.DataList = this.dataGrid;
        this.filterGrid = [];
        this.QuoteSummaryList = [];
        if (key === "PRIORITY") {
          if (value === "All") {
            this.filterGrid = this.dataGrid;
            this.state.filter.filters = [];
            this.loadGridData();
          }
          else {
            this.state.filter.filters = [{ field: 'Priority', operator: 'contains', value: value }];
            this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            this.loadGridData();
          }
        }
        if (key === "DIVISION") {
          this.state.filter.filters = [{ field: 'Division', operator: 'contains', value: value }];
          this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
          this.loadGridData();
        }
        if (key == "QUOTESTATUS") {
          this.state.filter.filters = [{ field: 'QuoteStatus', operator: 'contains', value: value }];
          this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
          this.loadGridData();
        }
        this.count++;
      }
    },
      error => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      });

  }

// FK: Auto Load Scrolling
loadGridData(){
  this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "CreationDate" }]);
  if(this.filterGrid.length <= this.pagesize){
    this.isShow = false;
  }
  else{
    this.isShow = true;
  }
  const next = this.QuoteSummaryList.length;
  this.QuoteSummaryList = [
    ...this.QuoteSummaryList,
    ...this.filterGrid.slice(next, next + this.pagesize)
  ];
  this.state.sort = [{ dir: "desc", field: "CreationDate" }];
  this.gridData = process(this.QuoteSummaryList, this.state);
}

  loadMore(): void {
    if(this.QuoteSummaryList.length >= this.filterGrid.length - this.pagesize){
      setTimeout(()=>{
      this.isShow = false;
    }, 1500);
    }
    else{
      this.isShow = true;
    }
    if(this.QuoteSummaryList.length == this.filterGrid.length){
      this.loading = false;
    } else{
      this.loading = true;
      const next = this.QuoteSummaryList.length;
      this.QuoteSummaryList = [
        ...this.QuoteSummaryList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      setTimeout(()=>{
        this.loading = false;
        this.state.sort = [{ dir: "desc", field: "CreationDate" }];
        this.gridData = process(this.QuoteSummaryList, this.state);
      }, 1500);
    }
  }

  public dataStateChange(state: DataStateChangeEvent) {
    this.state = state;
    this.gridData = process(this.QuoteSummaryList, this.state);
  }

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }

  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locationsIds = this.locationDateFilterData.locations;
    }
    else {
      this.locationsIds = "";
    }
    this.range["start"] = this.locationDateFilterData.start;
    this.range["end"] = this.locationDateFilterData.end;
    this.loadData(this.key, this.value, this.locationsIds, this.range);
    this.QuoteSummaryList = [];
    this.setDataInLocalStorage();
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;
    const result: ExcelExportData = {
      data: process(this.DataList, state).data
    };
    return result;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    //FK: page height
    this.pageHeight = event.target.innerHeight - 233;
  }
//FK: all filter
public filterChange(filter: CompositeFilterDescriptor): void {
  if(filter.filters.length >= 1){
    this.filterGrid = [];
    this.filterGrid = filterBy(this.DataList, filter);
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.QuoteSummaryList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "CreationDate" }]);
    const next = this.QuoteSummaryList.length;
      this.QuoteSummaryList = [
        ...this.QuoteSummaryList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
    this.state.sort = [{ dir: "desc", field: "CreationDate" }];
    this.gridData = process(this.QuoteSummaryList, this.state);
  }
  else {
    this.DataList = [];
    this.filterGrid = [];
    this.DataList = this.dataGrid;
    this.filterGrid = this.dataGrid;
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.QuoteSummaryList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "CreationDate" }]);
    const next = this.QuoteSummaryList.length;
      this.QuoteSummaryList = [
        ...this.QuoteSummaryList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
    this.state.sort = [{ dir: "desc", field: "CreationDate" }];
    this.gridData = process(this.QuoteSummaryList, this.state);
  }
}
  //FK: filter drop down
  public priorityChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'Priority',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }

  public quoteStatusChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'QuoteStatus',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }

  public quoteTypeChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'Division',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }

  public dropdownFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
    this.dropdownFilter.splice(
      0, this.dropdownFilter.length,
      ...flatten(filter).map(({ value }) => value)
    );
    return this.dropdownFilter;
  }

}
