import {
    Component,
    Input,
    Output,
    EventEmitter,
    AfterViewInit
} from '@angular/core';

import { FilterService } from '@progress/kendo-angular-grid';
import { FilterDescriptor } from '@progress/kendo-data-query';

@Component({
    selector: 'dropdownlist-filter',
    template: `
      <kendo-dropdownlist
        [value]="value"
        [valuePrimitive]="isPrimitive"
        [data]="data"
        [textField]="textField"
        [valueField]="valueField"
        [defaultItem]="{ value: 'Choose data', code: null }"
        (valueChange)="onValueChange($event)"
        style="width: 270px !important;"
      >
      </kendo-dropdownlist>
    `
})
export class CustomDropDownListFilterComponent implements AfterViewInit {
    @Input() public isPrimitive: boolean;
    @Input() public currentFilter: any;
    @Input() public data;
    @Input() public textField;
    @Input() public valueField;
    @Input() public filterService: FilterService;
    @Input() public field: string;
    @Output() public valueChange = new EventEmitter<number[]>();

    public currentData: any;
    public showFilter = true;
    public value: number;

    public onValueChange(value: number): void {
        this.filterService.filter({
            filters: [{ field: this.field, operator: 'eq', value: value }],
            logic: 'and'
        });
    }

    public ngAfterViewInit(): void {
        this.currentData = this.data;
        const currentColumnFilter: FilterDescriptor =
            this.currentFilter.filters.find(
                (filter: FilterDescriptor) => filter.field === this.field
            );
        if (currentColumnFilter) {
            this.value = currentColumnFilter.value;
        }
    }
}
