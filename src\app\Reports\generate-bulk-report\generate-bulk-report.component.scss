.col-md-12 button {
  background: #0093d7;
  float: right;
  margin-top: -9px;
  bottom: 4px;
  margin-right: 6px;
  position: relative;
  text-transform: capitalize;
}
.panel-heading .td {
  width: 5% !important;
  background: none !important;
  color: #000000bf !important;
  font-size: 13px !important;
  font-weight: 700 !important;
  border-radius: 0px !important;
}
.col-md-9 kendo-grid {
  position: relative;
  right: 29px;
  width: 103%;
  height: 385px;
}
.panel-body .td {
  padding: 0px 0px !important;
}
select {
  height: 354px !important;
}
.panelContentPadding-auto {
  padding: 19px;
}
.width-auto {
  width: 98%;
}
.panel-bar-width {
  width: 100%;
}
.panel .td {
  padding: 2px 9px !important;
}

.panel-table {
  display: table;
}
.panel-table > .panel-heading {
  display: table-header-group;
  background: transparent;
}
.panel-table > .panel-body {
  display: table-row-group;
}
p {
  position: relative;
  bottom: 103px;
}
.panel-table > .panel-body:before,
.panel-table > .panel-body:after {
  content: none;
}
.panel-table > .panel-footer {
  display: table-footer-group;
  background: transparent;
}
.panel-table > div > .tr {
  display: table-row;
}
.panel-table > div:last-child > .tr:last-child > .td {
  border-bottom: none;
}
.panel-table .td {
  display: table-cell;
  padding: 15px;
  border: 1px solid #ddd;
  border-top: none;
  border-left: none;
}
.dashboardFooterCss{
  position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
}
.panel-table .td:last-child {
  border-right: none;
}
.panel-table > .panel-heading > .tr > .td,
.panel-table > .panel-footer > .tr > .td {
  background-color: #f5f5f5;
}
.panel-table > .panel-heading > .tr > .td:first-child {
  border-radius: 4px 0 0 0;
}
.panel-table > .panel-heading > .tr > .td:last-child {
  border-radius: 0 4px 0 0;
}
.panel-table > .panel-footer > .tr > .td:first-child {
  border-radius: 0 0 0 4px;
}
.panel-table > .panel-footer > .tr > .td:last-child {
  border-radius: 0 0 4px 0;
}

.header-font-size {
  padding: 7px 13px;
  font-size: 72%;
  font-family: calibri light , "Roboto";
  text-decoration: none;
}
.imgHight {
  width: 68%;
  height: 41px;
}
.eValuateButton {
  margin-right: 23px;
}
.eValuateButton:hover {
  background: none !important;
}
.navbar-nav {
  margin-bottom: 1px !important;
}

.fa-caret-down {
  position: relative;
  left: 9px;
  top: 1px;
}
.submenu li {
  padding: 0px 5px;
  border-bottom: 1px solid #80808047;
}
.report-builder:hover .submenu {
  color: black;
  background: #17b0e9;
  display: block;
  top: 95%;
  width: 168px;
  right: 328px;
  border-radius: 0px;
  margin-top: 0px;
  padding: 0;
}
.logOutFormCss {
  display: none;
  width: 168px;
  position: absolute;
  background: #17b0e9;
  top: 100%;
  right: 46px;
}
.nav-item:hover {
  background: #17b0e9;
}
.nav-item {
  padding: 10px 0px !important;
}
.submenu a {
  padding: 8px;
  color: white;
}
.submenu li a {
  padding: 8px;
  text-align: left;
}
.submenu li:hover {
  background: #80808038;
}
ul li p {
  margin-bottom: -20px;
  position: absolute;
  right: 66px;
  color: rgba(212, 212, 212, 1);
  font-size: 79%;
  font-weight: 500;
}
@media only screen and (max-width: 991px) {
  ul li p {
    text-align: center;
    left: 0px;
    position: relative;
  }
}

ul li {
  padding: 0px;
}

.fa-icon {
  font-size: 14px;
}
.fa-sign-out {
  position: relative;
  left: 4px;
}

.mutiselectTextPosition {
  text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px !important;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover {

  transition: 0.5s !important;
  background: black !important;
}

@media only screen and (min-width: 991px) {
  .bar-chart-position {
    margin-top: 0;
    margin-bottom: 30px;
  }
}
.ul-container {
  padding: 0 12px;
}
.ul-row {
  height: 101px;
  overflow-y: scroll;
}

.k-select {
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  margin-left: 0.5em;
  margin-right: -0.2em;
}
.k-i-close {
  font-size: 12px;
  padding: 2px;
  box-sizing: content-box;
  cursor: pointer;
}
.span-content {
  background: rgba(128, 128, 128, 0.25882352941176473);  margin-right: 8px;
  padding: 4px;
  font-family: calibri light , "Roboto";
  margin-bottom: -7px;
  font-weight: 500;
  border-radius: 16px;
  margin-top: 4px;
}
.count-number {
  font-size: 2em;
}
@media only screen and (max-width: 1199px) {
  .bar-chart-position {
    margin-top: 25px;
  }
}
.k-chart text {
  font: sans-serif !important;
}

.position-pie-chart {
  height: 337px;
  margin-top: 2%;
  position: relative;
}

.dashboard-header .h4 {
  font-family: calibri light , "Roboto";
  font-size: 80%;
  font-weight: bold !important;
}

.container-fluid .count-data {
  margin-bottom: 14px;
}

.bulkDocDropDown {
  width: 100% !important;
}

.downloadButton {
  position: relative !important;
  right: 20px !important;
  width: auto !important;
}
.ProgressBarCss {
  position: relative;
  left: 27px;
}

.k-button {
  background: #0093d7;
  color: white;
  font-size: 11px;
}

.label {
  font-size: 13px;
  font-weight: 600;
  color: white;
  position: relative;
  right: 8px;
}
.OpenCallsGrid {
  position: relative;
  cursor: pointer;
  font-size: 70%;
  font-family: calibri light , "Roboto";
  font-weight: 500;
}

.ButtonFooterDialog {
  width: 18% !important;
  background: #4ab4de !important;
  color: white !important;
}

kendo-datepicker {
  z-index: 999 !important;
}
.margin-top-section
{
  margin-top: 10% !important ;
}

.footerCssOnToggleClose
{
  position: relative !important;
  top: 315px !important;

}
@media only screen and (max-width: 767px) {
  .mutiselectTextPosition
  {
    font-size: 8px !important;
    position: relative !important;
    /* right: 4%; */
    top: 6px !important;
  }
  .mobileAutoPadding
  {
    padding: 7px 15px !important;
  }

   .md-h1
  {
    position: absolute !important;
    top: 8px !important;

  }
  .width-auto
  {
    position: relative !important;
    // bottom: 37px !important;
  }
  kendo-datepicker
  {
    width: 74% !important;
  }
  .col-md-9
  {
    position: relative !important;
    bottom: 37px !important;
  }
  .col-md-9 kendo-grid {
    position: relative !important;
    right: 0px !important;
    /* width: 103%; */
    width: 100% !important;
    height: 385px !important;
}
}
@media only screen and (max-width: 1129px) {
.margin-top-section
{
  margin-top: 3% !important;
}
}

.dateInputCss {
  width: 114px;
  font-size: 14px;
}


.dateInputCss,
:host ::ng-deep .k-dateinput-wrap,
:host ::ng-deep .k-input {
    height: calc(8px + 1.75em);
    color: rgba(0, 0, 0, 0.87) !important;
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap {
    border-color: rgba(0, 0, 0, 0.87) !important;
    border-top: 0px;
}