import { Component, OnInit, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { Title } from "@angular/platform-browser";
import { SharedDataService } from "../../Services/shared-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import * as moment from 'moment';
import { ActivatedRoute, Router } from "@angular/router";
import { OpenDebtModel, locationDateFilterEntity, InvoiceNumberType, OpenDebtChartKeys } from "./open-debt.model";
import { saveAs } from 'file-saver';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { GridDataResult, DataStateChangeEvent, SelectAllCheckboxState, SelectableSettings } from '@progress/kendo-angular-grid';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'app-open-debt',
  templateUrl: './open-debt.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./open-debt.component.scss']
})

export class OpenDebtComponent implements OnInit {
  public buttonCount: number;
  public previousNext = true;
  locations: string;
  startDate: string;
  endDate: string;
  chartCategory: string;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  start: Date;
  end: Date;
  originalDate = new Date();
  openDebtList: OpenDebtModel[] = [];
  gridView = [];
  public range = {

  };
  public state: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridData: GridDataResult;
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public DataList: any = [];
  public filterGrid: any[];
  pagesize: number = 20;
  private dataFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  fromDateFilter: any;
  showOpenDebtTitle: boolean = true;

  constructor(private spinner: SpinnerVisibilityService, private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute,
    private router: Router
  ) {
    this.allData = this.allData.bind(this);
    this.shareData.removeBacklogData();
    this.setSelectableSettings();
    let paramVal = this.route.snapshot.paramMap.get('value');

    //MS: From Most Expensive Equipment Dashboard chart - Start
    if (paramVal == "MostExpensiveEquipment") {
      this.showOpenDebtTitle = false;
      this.chartCategory = "MostExpensiveEquipment";
    }
    //MS: From Most Expensive Equipment Dashboard chart - End
    else {
      this.chartCategory = OpenDebtChartKeys[paramVal];
    }
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.locationDateFilterData === null) {
      if (this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == "") {
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
  }

  ngOnInit() {
    let pageTite = "Open Debt";
    if (this.showOpenDebtTitle==false) {
      pageTite = "Invoices";
    }
    this.titleService.setTitle(pageTite);


    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;

    }
    this.loadData(this.locations);
  }
  openDebtclick(event) {
  }

  downloadMaintenanceInvoice(data) {
    if (data.InvoiceRef != null && data.InvoiceRef != undefined) {
      let documnetTYPE = InvoiceNumberType[data.InvoiceRef.substring(0, 1)];
      let payload = {
        DocumentType: documnetTYPE + " Invoice",
        PropertyType: "Invoice Number",
        PropertyValue: data.InvoiceRef
      };
      this.spinner.show();
      this.coredata.getDocLinkByID(payload).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              let result = res['_body'];
              let fileType = result.type.split('/')[1];
              let ReportName = res.headers.get('x-filename') + "." + fileType;
              this.shareData.showSuccess(documnetTYPE + " Invoice Document downloaded successfully");
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
              saveAs(blob, ReportName);
            }
            else if (res.status === 204) {
              this.shareData.showWarning("No " + documnetTYPE + " Invoice Document found for Invoice Number:" + data.InvoiceRef);
            }
            this.spinner.hide();
          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    }
  }

  setDataInLocalStorage() {
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locations
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }

  CurrencyToString(value: any) {
    if (value == 0) {
      return 0;
    }
    else {
      // hundreds
      if (value <= 999) {
        return Number.parseFloat(value.toString()).toFixed(2);
      }
      // thousands
      else if (value >= 1000 && value <= 999999) {
        return Number.parseFloat((value / 1000).toString()).toFixed(2).toString() + 'K';
      }
      // millions
      else if (value >= 1000000 && value <= 999999999) {
        return Number.parseFloat((value / 1000000).toString()).toFixed(2).toString() + 'M';
      }
      // billions
      else if (value >= 1000000000 && value <= 999999999999) {
        return Number.parseFloat((value / 1000000000).toString()).toFixed(2).toString() + 'B';
      }
      else
        return value;
    }
  }

  @ViewChild('daterange') public service;
  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridData = process(this.openDebtList, this.state);
  }

  loadData(ids) {
    let id: string;
    if (ids === undefined) {
      id = "";
    }
    else {
      id = ids;
    }
    this.dataGrid = [];
    this.DataList = [];
    this.filterGrid = [];
    this.openDebtList = [];
    if(this.chartCategory == null && this.chartCategory == undefined){
      this.coredata.getOpenDebtApi(id).pipe(first()).subscribe(res => {
        if (res != null && res != undefined) {
          if (res.StatusCode === 200) {
            this.dataGrid = res.response;
            this.dataGrid.map(element => {
              element.DueDate = this.GetFormattedDate(element.DueDate);
              element.InvoiceDate = this.GetFormattedDate(element.InvoiceDate);
              element.InvoiceAmount = parseFloat(element.InvoiceAmount.toString()).toFixed(2);
            });
            this.DataList = this.dataGrid;
            this.filterGrid = this.dataGrid;
            this.state.filter.filters = [];
            this.loadGridData();
          }
        }
      },
        catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
          }
        });
    }else{
      if(this.chartCategory == "MostExpensiveEquipment"){
          if(this.shareData.MostExpEqpBar != undefined && this.shareData.MostExpEqpBar != null && this.shareData.MostExpEqpBar.JobNumber != undefined && this.shareData.MostExpEqpBar.JobNumber != null){
            this.coredata.getMostExpEquipmentInvoices(this.shareData.MostExpEqpBar.JobNumber).subscribe(response =>
              {
                if (response != null && response != undefined) {
                  if (response.StatusCode == 200) {
                    this.dataGrid = response.response;
                    this.dataGrid.map(element => {
                      element.DueDate = this.GetFormattedDate(element.DueDate);
                      element.InvoiceDate = this.GetFormattedDate(element.InvoiceDate);
                      element.InvoiceAmount = parseFloat(element.InvoiceAmount.toString()).toFixed(2);
                    });
                    this.DataList = this.dataGrid;
                    this.filterGrid = this.dataGrid;
                    this.state.filter.filters = [];
                    this.loadGridData();
                  } else {
                    this.shareData.showError("An error occured, please contact support");
                  }
                }
              },
              err => { this.shareData.ErrorHandler(err); },
              () => { console.log('Completed') }
              );
          }else{
            this.router.navigate(["/Dashboard"]);
          }
      }else{
        this.coredata.getOpenDebtApi(id).pipe(first()).subscribe(res => {
          if (res != null && res != undefined) {
            if (res.StatusCode === 200) {
              this.dataGrid = res.response;
              this.dataGrid.map(element => {
                element.DueDate = this.GetFormattedDate(element.DueDate);
                element.InvoiceDate = this.GetFormattedDate(element.InvoiceDate);
                element.InvoiceAmount = parseFloat(element.InvoiceAmount.toString()).toFixed(2);
              });
              this.DataList = this.dataGrid;
              this.state.filter.filters = [{ field: 'Aged', operator: 'contains', value: this.chartCategory }];
              this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
              this.loadGridData();
            }
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
            }
          });
      }
    }
  }
  // FK: auto load scrolling
  loadGridData() {
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "InvoiceDate" }]);
    if (this.filterGrid.length <= this.pagesize) {
      this.isShow = false;
    }
    else {
      this.isShow = true;
    }
    const next = this.openDebtList.length;
    this.openDebtList = [
      ...this.openDebtList,
      ...this.filterGrid.slice(next, next + this.pagesize)
    ];
    this.state.sort = [{ dir: "desc", field: "InvoiceDate" }];
    this.gridData = process(this.openDebtList, this.state);
  }

  loadMore(): void {
    if (this.openDebtList.length >= this.filterGrid.length - this.pagesize) {
      setTimeout(() => {
        this.isShow = false;
      }, 1500);
    }
    else {
      this.isShow = true;
    }
    if (this.openDebtList.length == this.filterGrid.length) {
      this.loading = false;
    } else {
      this.loading = true;
      const next = this.openDebtList.length;
      this.openDebtList = [
        ...this.openDebtList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      setTimeout(() => {
        this.loading = false;
        this.state.sort = [{ dir: "desc", field: "InvoiceDate" }];
        this.gridData = process(this.openDebtList, this.state);
      }, 1500);
    }
  }

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }

  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locations = this.locationDateFilterData.locations;
    }
    else {
      this.locations = "";
    }
    this.range["start"] = new Date(this.locationDateFilterData.start);
    this.range["end"] = new Date(this.locationDateFilterData.end);
    this.loadData(this.locations);
    this.setDataInLocalStorage();
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;

    const result: ExcelExportData = {

      data: process(this.DataList, state).data
    };
    return result;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    this.pageHeight = event.target.innerHeight - 233;
  }
  //FK: all filter
  public filterChange(filter: CompositeFilterDescriptor): void {
    if (filter.filters.length >= 1) {
      this.filterGrid = [];
      this.filterGrid = filterBy(this.DataList, filter);
      if (this.filterGrid.length <= this.pagesize) {
        this.isShow = false;
      }
      else {
        this.isShow = true;
      }
      this.openDebtList = [];
      this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "InvoiceDate" }]);
      const next = this.openDebtList.length;
      this.openDebtList = [
        ...this.openDebtList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      this.state.sort = [{ dir: "desc", field: "InvoiceDate" }];
      this.gridData = process(this.openDebtList, this.state);
    }
    else {
      this.DataList = [];
      this.filterGrid = [];
      this.DataList = this.dataGrid;
      this.filterGrid = this.dataGrid;
      if (this.filterGrid.length <= this.pagesize) {
        this.isShow = false;
      }
      else {
        this.isShow = true;
      }
      this.openDebtList = [];
      this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "InvoiceDate" }]);
      const next = this.openDebtList.length;
      this.openDebtList = [
        ...this.openDebtList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      this.state.sort = [{ dir: "desc", field: "InvoiceDate" }];
      this.gridData = process(this.openDebtList, this.state);
    }
  }
  //FK: filter
  public dataFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
    this.dataFilter.splice(
      0, this.dataFilter.length,
      ...flatten(filter).map(({ value }) => value)
    );
    return this.dataFilter;
  }

  public selectAllState: SelectAllCheckboxState = 'unchecked';
  public selectableSettings: SelectableSettings;
  public mySelection: any[] = [];
  selectedCheckbox: Array<any> = [];


  public onSelectedKeysChange(e) {
    const len = this.mySelection.length;
    if (len === 0) {
      this.selectAllState = 'unchecked';
    } else if (len > 0 && len < this.openDebtList.length) {
      this.selectAllState = 'indeterminate';
    } else {
      this.selectAllState = 'checked';
    }
  }

  public onSelectAllChange(checkedState: SelectAllCheckboxState) {
    if (checkedState === 'checked') {
      this.mySelection = this.openDebtList.map((item) => item.InvoiceRef);
      this.selectAllState = 'checked';
    } else {
      this.mySelection = [];
      this.selectAllState = 'unchecked';
    }
  }

  ngDoCheck() {
    this.selectedCheckbox = [];
    this.mySelection.map(el => {
      let findElement = this.openDebtList.find(ele => ele.InvoiceRef === el);
      this.selectedCheckbox.push(findElement);
    });
  }

  public setSelectableSettings(): void {
    this.selectableSettings = {
      checkboxOnly: true,
      mode: 'multiple'
    };
  }

  DownloadAllDoc(){
    if(this.selectedCheckbox.length > 0){
      for (let data of this.selectedCheckbox) {
        this.downloadMaintenanceInvoice(data);
      }
    }else{
      this.shareData.showWarning("Please select at least one record");
    }
  }
}
