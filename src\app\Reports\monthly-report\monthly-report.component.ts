import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { ContactListEntities, CustomersEntities, locationDateFilterEntity, LocationByCustomerEntities, GenerateReportEntities } from "./monthly-report.model";
import { SharedDataService } from "../../Services/shared-data.service";
import { AuthenticationService } from "../../Services/authentication.service";
import { SpinnerVisibilityService } from 'ng-http-loader';
import { Router, ActivatedRoute } from "@angular/router";
import { saveAs } from '@progress/kendo-file-saver';
import { Title } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { CodeData, TokenDecodedEntities } from '../../Common/shared';
import { SessionStorageService } from 'ngx-webstorage';
import { element } from 'protractor';
import { first } from 'rxjs/operators';
export let isBrowserRefreshed: boolean = false;
@Component({
  selector: 'app-monthly-report',
  templateUrl: './monthly-report.component.html',
  styleUrls: ['./monthly-report.component.scss']
})
export class MonthlyReportComponent implements OnInit, OnDestroy {
  locationsData: any;
  customersData: any;
  defaultLocation = [];
  defaultCustomer = [];
  defaultContact = [];
  Contact: Array<any> = [];
  subscription: Subscription;
  monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
    "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
  public opened = false;
  public showCustomer = false;
  showCustomerError = false;
  defaultDropdown = "Select Locations";
  defaultCustomerDropdown = "Select Customers";
  defaultContactDropdown = "Select Contact";
  defaultLocationAsString: string;
  defaultCustomerAsString: string;
  defaultContactAsString: string;
  hasAllValueSelected: boolean = false;
  hasAllContactValueSelected: boolean = false;
  hasAllCustomerValueSelected: boolean = false;
  tokenData: TokenDecodedEntities = new TokenDecodedEntities();
  LocationsByCustomerDetails: Array<LocationByCustomerEntities> = [];
  locationDropdown: LocationByCustomerEntities[] = [];
  ContactDropdown: ContactListEntities[] = [];
  public selectedLocationList: Array<any> = [];
  totalCount: number;
  totalCustomerCount: number;
  totalSelectedLocations: string;
  totalSelectedCustomers: string;
  totalSelectedContact: string;
  public AllLocationList: CustomersEntities[] = [];
  contactData: ContactListEntities[] = [];
  customersDetails: CustomersEntities[] = [];
  originalDate = new Date();
  StateCodeList: any = [];
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  startDate: string;
  endDate: string;
  locationsByUserId: CustomersEntities[] = [];
  codeData: CodeData[] = [];
  StateList: CodeData[] = [];
  contactsByCustomer: ContactListEntities[] = [];
  ReportData: GenerateReportEntities = new GenerateReportEntities();
  Currentdate = new Date();
  range: any;
  MonthlyReportName: string;
  orgName: string = "";
  tokenKey: string = ""


  public isBMS: boolean = false;
  showContact: boolean = false;
  totalContactCount: number;
  constructor(private session: SessionStorageService, private route: ActivatedRoute, private titleService: Title, private router: Router, private spinner: SpinnerVisibilityService, private coredata: CoreDataService, private shareData: SharedDataService, private authdata: AuthenticationService) {
    let pageTite = this.route.snapshot.data['title'];
    this.tokenData = this.authdata.getTokenData();
    this.titleService.setTitle(pageTite);
    this.shareData.removeBacklogData();
    if (window.location.origin.includes("optimumair.co.nz")) {
      this.isBMS = true
    }

    isBrowserRefreshed = !router.navigated;
    if (isBrowserRefreshed === false) {
      this.loadCustomer(this.tokenData.UserID, undefined);
    }

    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));

    if (this.locationDateFilterData === null) {
      this.range = {
        start: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth() - 1, 1),
        end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), 0)
      }
    }
    else {
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
  }

  ngOnInit() {
    this.ReportData.StartDate = this.range.start;
    this.ReportData.EndDate = this.range.end;
    this.loadCodeData();
  }
  removeTag(index) {
    if (index != null && index != undefined) {
      let data = this.ReportData.Location.slice();
      data.splice(index, 1);
      this.ReportData.Location = data;
      this.checkIsListSelectedAll();
    }
  }
  // FK:
  removeCustomerTag(index) {
    if (index != null && index != undefined) {
      let data = this.ReportData.Customer.slice();
      data.splice(index, 1);
      this.ReportData.Customer = data;
      this.checkIsListSelectedAllCustomer();
    }
  }
  removeContactTag(index) {
    if (index != null && index != undefined) {
      let data = this.ReportData.Contact.slice();
      data.splice(index, 1);
      this.ReportData.Contact = data;
      this.checkIsListSelectedAllContact();
    }
  }
  ngDoCheck() {
    let locations = [];
    let defaultLocation = [];
    let customers = [];
    let defaultCustomer = [];
    let defaultContact = [];
    let Contact = [];
    this.ReportData.Customer.map((element, index) => {
      if ((index + 1) <= 2) {
        this.totalCustomerCount = index + 1;
        defaultCustomer.push(element.CustName);
        this.defaultCustomer = defaultCustomer;
      }
      this.defaultCustomerAsString = this.defaultCustomer.toString();
      customers.push(element.CustName);
    });
    this.totalSelectedCustomers = customers.toString();

    this.ReportData.Location.map((element, index) => {
      if ((index + 1) <= 2) {
        this.totalCount = index + 1;
        defaultLocation.push(element.Name);
        this.defaultLocation = defaultLocation;
      }
      this.defaultLocationAsString = this.defaultLocation.toString();
      locations.push(element.Name);
    });
    this.totalSelectedLocations = locations.toString();

    this.ReportData.Contact.map((element, index) => {

      if ((index + 1) <= 2) {
        this.totalContactCount = index + 1;
        defaultContact.push(element.Name);
        this.defaultContact = defaultContact;
      }
      this.defaultContactAsString = this.defaultContact.toString();
      Contact.push(element.Name);
    });
    this.totalSelectedContact = Contact.toString();
  }

  public open(dataItem) {
    this.opened = true;
    // this.locationsData = dataItem;
  }
  // FK: open customer dialog box
  public openCustomer(dataItem) {
    this.showCustomer = true;
    // this.locationsData = dataItem;
  }
  public openContact(dataItem) {
    this.showContact = true;
  }

  //FK: clear all customer
  clearAllCustomer() {
    this.ReportData.Customer = [];
    this.ReportData.Location = [];
    this.LocationsByCustomerDetails = [];
    this.locationDropdown = []
    this.ReportData.Contact = [];
    this.contactData = [];
    this.customersData = this.ReportData.Customer;
    this.locationsData = this.ReportData.Location;
    this.showCustomer = false;
    this.hasAllCustomerValueSelected = false;
    this.showCustomerError = false;
  }

  clearAll() {
    this.ReportData.Location = [];
    this.locationsData = this.ReportData.Location;
    this.opened = false;
    this.hasAllValueSelected = false;
  }

  clearAllContact() {
    this.ReportData.Contact = [];
    //this.ContactDropdown=this.ReportData.Contact;
    this.showContact = false;
    this.hasAllContactValueSelected = false;
  }

  loadCustomer(id, locationfromBrowserRefresh) {
    if (locationfromBrowserRefresh === undefined) {
      let locationsInSession = this.session.retrieve('locations');
      if (locationsInSession != null) {

        let duplicateArray = [];
        this.locationsByUserId = locationsInSession;
        locationsInSession.map(element => {
          let indexofElement = duplicateArray.findIndex(data => data.CustomerID === element.CustomerID);
          if (indexofElement === -1) {
            duplicateArray.push(element);
          }
        });
        this.customersDetails = duplicateArray;
        this.AllLocationList = this.customersDetails.slice();
      }
      else if (locationsInSession === null) {
        this.coredata.getCustomerLocation(id).pipe(first()).subscribe(res => {
          if (res.StatusCode === 200) {
            let duplicateArray = [];
            this.locationsByUserId = res.response;
            res.response.map(element => {
              let indexofElement = duplicateArray.findIndex(data => data.CustomerID === element.CustomerID);
              if (indexofElement === -1) {
                duplicateArray.push(element);
              }
            });
            this.customersDetails = duplicateArray;
            this.AllLocationList = this.customersDetails.slice();
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
              // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
            }
          });
      }
    }
    else if (locationfromBrowserRefresh != undefined) {
      let locationsInSession = locationfromBrowserRefresh;
      let duplicateArray = [];
      this.locationsByUserId = locationsInSession;
      locationsInSession.map(element => {
        let indexofElement = duplicateArray.findIndex(data => data.CustomerID === element.CustomerID);
        if (indexofElement === -1) {
          duplicateArray.push(element);
        }
      });
      this.customersDetails = duplicateArray;
      this.AllLocationList = this.customersDetails.slice();
    }


  }
  filterCustomer(value) {
    if (value != null && value != undefined) {
      // this.AllLocationList = this.customersDetails.filter((s) => s.CustName.toLowerCase().indexOf(value.toLowerCase()) !== -1);
      this.AllLocationList = this.customersDetails.filter((s) =>
        s.CustName.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
        s.CustomerCode.toLowerCase().indexOf(value.toLowerCase()) !== -1
      );
    }
    this.checkIsListSelectedAllCustomer();
  }
  filterContact(value) {
    if (value != null && value != undefined) {
      this.contactData = this.contactsByCustomer.filter((s) => s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1);
    }

  }
  filterLocation(value) {
    if (value != null && value != undefined) {
      // this.locationDropdown = this.LocationsByCustomerDetails.filter((s) => s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1);

      this.locationDropdown = this.LocationsByCustomerDetails.filter((s) =>
        s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1 ||
        s.LocationCode.toLowerCase().indexOf(value.toLowerCase()) !== -1
      );
    }
    this.checkIsListSelectedAll();
  }
  valueChange() {
    this.checkIsListSelectedAllCustomer();
  }

  // FK:
  checkIsListSelectedAllCustomer() {
    if (this.ReportData.Customer.length === this.AllLocationList.length) {
      this.hasAllCustomerValueSelected = true;
    }
    else if (this.ReportData.Customer.length < this.AllLocationList.length) {
      this.hasAllCustomerValueSelected = false;
    }
  }

  loadLocations(CustomerCode) {
    //this.LocationsByCustomerDetails = [];
    let filterList = this.locationsByUserId.filter(element => element.CustomerCode === CustomerCode);
    //this.LocationsByCustomerDetails = filterList;
    filterList.map((element) => {
      this.locationDropdown.push(element);
    });
  }
  //MS:
  loadCodeData() {
    this.coredata.getCodeData().pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        this.codeData = res.response;
        this.StateList = this.codeData.filter(data => data.CodeName === "STATE");
      }
    }, catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
  generateReport() {
    this.ReportData.AccountManager = this.tokenData.UserID;
    let LocationCode = [];
    this.locationsData.map(element => {
      LocationCode.push(element.LocationCode);
    })
    let CustomerCode = [];
    this.customersData.map(element => {
      CustomerCode.push(element.CustomerCode);
    })
    let ContactCode = [];
    this.ReportData.Contact.map(element => { ContactCode.push(element.ContactID) }
    )
    let stateCode = this.StateList.find((st) => { return st.CodeID == this.tokenData.StateCd }).Code;
    let payload = {
      AccountManager: this.ReportData.AccountManager,
      FireContract: this.ReportData.FireContract,
      Contact: ContactCode.toString(),
      Customer: CustomerCode.toString(),
      //State: this.StateCodeList.toString(),
      State: stateCode,
      EndDate: this.ReportData.EndDate.getFullYear() + "-" + (this.ReportData.EndDate.getMonth() + 1) + "-" + this.ReportData.EndDate.getDate(),
      Location: LocationCode.toString(),
      StartDate: this.ReportData.StartDate.getFullYear() + "-" + (this.ReportData.StartDate.getMonth() + 1) + "-" + this.ReportData.StartDate.getDate(),
      ReportName: (this.ReportData.FireContract == true ? "AFSMonthlyReport" : "AIRMonthlyReport")
    }


    if (origin.includes("airmaster.com.au")) {
      this.orgName = "AIR";
    } else if (origin.includes("airmasterfire.com.au")) {
      this.orgName = "AFS";
    } else if (
      origin.includes("optimumair.co.nz") ||
      origin.includes("controlco.nz")
    ) {
      this.orgName = "OPT";
    } else if (origin.includes("cp")) {
    }


    if (window.location.hostname == 'localhost') {
      this.tokenKey = 'CPToken';
    }
    if (window.location.hostname.split(".")[1]?.toUpperCase() == "AIRMASTER") {
      this.tokenKey = 'CPTokenAIR';
    } else if (window.location.hostname.split(".")[1]?.toUpperCase() == "AIRMASTERFIRE") {
      this.tokenKey = 'CPTokenAFS';
    }
    else if (window.location.hostname.split(".")[1]?.toUpperCase() == "OPTIMUMAIR") {
      this.tokenKey = 'CPTokenOPT';
    }
    else if (window.location.hostname.split(".")[1]?.toUpperCase() == "CONTROLCO") {
      this.tokenKey = 'CPTokenControlco';
    }

    var mapForm = document.createElement("form");
    mapForm.target = "_blank";
    mapForm.method = "POST";
    mapForm.action = window.location.origin + "/Reports/Report";
    // mapForm.action = "https://aetest.airmaster.com.au/Reports/Report";

    var mapInput1 = document.createElement("input");
    var mapInput2 = document.createElement("input");
    var mapInput3 = document.createElement("input");
    var mapInput4 = document.createElement("input");
    var mapInput5 = document.createElement("input");
    var mapInput6 = document.createElement("input");
    var mapInput7 = document.createElement("input");
    var mapInput8 = document.createElement("input");
    var mapInput9 = document.createElement("input");
    var mapInput10 = document.createElement("input");
    var mapInput11 = document.createElement("input");
    var mapInput12 = document.createElement("input");



    mapInput1.name = "Customer";
    mapInput2.name = "Location";
    mapInput3.name = "Contact";
    mapInput4.name = "State";
    mapInput5.name = "StartDate";
    mapInput6.name = "EndDate";
    mapInput7.name = "AccountManager";
    mapInput8.name = "FireContract";
    mapInput9.name = "ReportType";
    mapInput10.name = "IsCP";
    mapInput11.name = "token";
    mapInput12.name = "OrgName";

    mapInput1.setAttribute("value", payload.Customer);
    mapInput2.setAttribute("value", payload.Location);
    mapInput3.setAttribute("value", payload.Contact);
    mapInput4.setAttribute("value", payload.State);
    mapInput5.setAttribute("value", payload.StartDate);
    mapInput6.setAttribute("value", payload.EndDate);
    mapInput7.setAttribute("value", payload.AccountManager);
    mapInput8.setAttribute("value", payload.FireContract.toString());
    mapInput9.setAttribute("value", payload.ReportName);
    mapInput10.setAttribute("value", "true");
    mapInput11.setAttribute("value", localStorage.getItem(this.tokenKey).toString());
    mapInput12.setAttribute("value", this.orgName);

    mapForm.appendChild(mapInput1);
    mapForm.appendChild(mapInput2);
    mapForm.appendChild(mapInput3);
    mapForm.appendChild(mapInput4);
    mapForm.appendChild(mapInput5);
    mapForm.appendChild(mapInput6);
    mapForm.appendChild(mapInput7);
    mapForm.appendChild(mapInput8);
    mapForm.appendChild(mapInput9);
    mapForm.appendChild(mapInput10);
    mapForm.appendChild(mapInput11);
    mapForm.appendChild(mapInput12);


    // console.log(mapForm)
    mapForm.hidden = true;
    document.body.appendChild(mapForm);

    mapForm.submit();
  }

  loadContacts(customeriD) {
    this.coredata.getContacts(customeriD).subscribe(res => {
      if (res.StatusCode === 200) {
        if (res.response != null && res.response != undefined) {
          this.contactsByCustomer = [];
          this.contactsByCustomer = res.response;
          this.contactsByCustomer.map((element) => {
            this.contactData.push(element);
          });
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);

        }
      }
    );
  }
  locationChange(event) {
    this.loadCustomer(this.tokenData.UserID, event);
  }
  ngOnDestroy() {
    // this.subscription.unsubscribe();
  }

  public close(value) {
    if (value === "cancel") {
      this.opened = false;
      this.hasAllValueSelected = false;
      this.ReportData.Location = [];
      this.locationsData = [];
    }
    else if (value === "yes") {
      this.opened = false;
      this.locationsData = this.ReportData.Location;

    }
  }
  public closeContact(value) {
    if (value === "cancel") {
      this.showContact = false;
      this.hasAllContactValueSelected = false;
      this.ReportData.Contact = [];
      //this.contactData = [];
    }
    else if (value === "yes") {
      this.showContact = false;
      //this.contactData = this.ReportData.Contact;

    }
  }
  // FK:
  public closeCustomer(value) {
    if (value === "cancel") {
      this.ReportData.Customer = [];
      this.ReportData.Location = [];
      this.customersData = [];
      this.LocationsByCustomerDetails = [];
      this.locationDropdown = [];
      this.locationsData = [];
      this.ReportData.Contact = [];
      this.contactData = [];
      this.hasAllCustomerValueSelected = false;
      this.showCustomerError = false;
      this.showCustomer = false;
    }
    else if (value === "yes") {
      this.LocationsByCustomerDetails = [];
      this.ReportData.Contact = [];
      this.ReportData.Location = [];
      this.locationDropdown = [];
      this.contactData = [];
      this.StateCodeList = [];
      if (this.ReportData.Customer.length > 0) {
        this.ReportData.Customer.map((element) => {
          //this.StateCodeList.push(element.StateCode);
          this.loadLocations(element.CustomerCode);
          this.loadContacts(element.CustomerID);
        });
        this.checkIsListSelectedAllCustomer();
        this.customersData = this.ReportData.Customer;
        this.LocationsByCustomerDetails = this.locationDropdown;
        this.showCustomerError = false;
        this.showCustomer = false;
      }
      else {
        this.showCustomerError = true;
      }
    }
  }

  //FK:
  OnSelectAllCustomerChange() {
    this.hasAllCustomerValueSelected = true;
    this.ReportData.Customer = this.AllLocationList;
    this.showCustomerError = false;
  }
  //FK:
  OnDeSelectAllCustomerChange() {
    this.hasAllCustomerValueSelected = false;
    this.ReportData.Customer = [];
    this.showCustomerError = true;
  }

  OnSelectAllChange() {
    this.hasAllValueSelected = true;
    this.ReportData.Location = this.locationDropdown;
  }
  OnDeSelectAllChange() {
    this.hasAllValueSelected = false;
    this.ReportData.Location = [];
  }

  OnSelectAllContactChange() {
    this.hasAllContactValueSelected = true;
    this.ReportData.Contact = this.contactData;
  }
  OnDeSelectAllContactChange() {
    this.hasAllContactValueSelected = false;
    this.ReportData.Contact = [];
  }


  checkIsListSelectedAll() {
    if (this.ReportData.Location.length === this.locationDropdown.length) {
      this.hasAllValueSelected = true;
    }
    else if (this.ReportData.Location.length < this.locationDropdown.length) {
      this.hasAllValueSelected = false;
    }
  }
  checkIsListSelectedAllContact() {
    if (this.ReportData.Contact.length === this.ContactDropdown.length) {
      this.hasAllContactValueSelected = true;
    }
    else if (this.ReportData.Location.length < this.ContactDropdown.length) {
      this.hasAllContactValueSelected = false;
    }
  }
  OnLocationChange() {
    this.checkIsListSelectedAll();
  }
  OnContactChange() {
    this.checkIsListSelectedAllContact();
  }


}
