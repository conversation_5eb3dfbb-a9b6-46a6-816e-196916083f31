<div class="container">
  <form class="form">
    <h1 *ngIf='changePassword' >Change Password</h1>
    <h1 *ngIf='!changePassword' >TWO-FACTOR AUTHENTICATION</h1>
    <div  class="form__group form__pincode">
      <label
        >A message with a One Time Password (OTP) has been sent to your email. Please  enter the OTP</label
      >
    </div>

  
  
  <div *ngIf='changePassword' class="form-group-material">
    <label for="pwd">Password:</label>
    <!-- <input id="login-password" type="password" minlength="4" name="loginPassword"
      class="input-material padding-left" ngModel required /> -->
    <input
      type="password"
      name="password"
      [(ngModel)]="password"
      #Pa="ngModel"
     
      class="input-material padding-left"
      [ngClass]="{
        'is-invalid':
      false
      }"
      id="password"
      pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{10,16}$"
      required
    />
  <div *ngIf="Pa.errors && (Pa.dirty || Pa.touched)" class="invalidField ">
    <div class="" [hidden]="(!Pa.errors.required )">
        <span style="color: red;font-size: 13px;">Password is required !</span>
    </div>
    <div class="" *ngIf="(Pa.errors && Pa.errors.pattern)">
        <span style="color: red;font-size: 13px;">Password must be between 10-16 characters long and must contain a-z, A-Z, 0-9, '@,$,!,%,*,?,&' </span>
    </div>
</div>
</div>
  

  
<div *ngIf='changePassword' class="form-group-material">
  <label for="pwd">Retype Password:</label>
  <!-- <input id="login-password" type="password" minlength="4" name="loginPassword"
    class="input-material padding-left" ngModel required /> -->
  <input
    type="password"
    name="retypepassword"
    [(ngModel)]="retypepassword"
    #P="ngModel"
   
    class="input-material padding-left"
    [ngClass]="{
      'is-invalid':
     false
    }"
   
    pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{10,16}$"
    required
  />
  <div *ngIf="P.errors && (P.dirty || P.touched)" class="invalidField ">
    <div  [hidden]="(!P.errors.required )">
        <span style="color: red;font-size: 13px;">Password is required !</span>
    </div>
    <div class="" *ngIf="(P.errors && P.errors.pattern)">
        <span style="color: red;font-size: 13px;">Password must be between 10-16 characters long and must contain a-z, A-Z, 0-9, '@,$,!,%,*,?,&' </span>
    </div>
</div>
</div>
<div class="form-group-material">
<label *ngIf='changePassword' for="pwd">OTP:</label>

    <div class="pin-blocks">
      <input
        type="tel"
        class="input_blocks"
        id="pincode-1"
        (keydown)="keydown($event, '1')"
        (keyup)="keyup($event, '1')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="1"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-2"
        (keydown)="keydown($event, '2')"
        (keyup)="keyup($event, '2')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="2"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-3"
        (keydown)="keydown($event, '3')"
        (keyup)="keyup($event, '3')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="3"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-4"
        (keydown)="keydown($event, '4')"
        (keyup)="keyup($event, '4')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="4"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-5"
        (keydown)="keydown($event, '5')"
        (keyup)="keyup($event, '5')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="5"
        placeholder="·"
        autocomplete="off"
      />
      <!-- <input
        type="tel"
        class="input_blocks"
        id="pincode-6"
        (keydown)="keydown($event, '6')"
        (keyup)="keyup($event, '6')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="6"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-7"
        (keydown)="keydown($event, '7')"
        (keyup)="keyup($event, '7')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="7"
        placeholder="·"
        autocomplete="off"
      />
      <input
        type="tel"
        class="input_blocks"
        id="pincode-8"
        (keydown)="keydown($event, '8')"
        (keyup)="keyup($event, '8')"
        pattern="[A-Za-z0-9]{1}"
        maxlength="1"
        tabindex="8"
        placeholder="·"
        autocomplete="off"
      /> -->
    </div>
    <span style="color: red;font-size: 13px;margin-left: 128px;">Do not press TAB, Just type the OTP</span>
  </div>

    <div *ngIf='!changePassword' class="form__buttons">
      <button
        (click)="submit()"
        type="submit"
        class="button button--primary"
      >
        Continue
      </button>
    </div>

    <div *ngIf='changePassword' class="form__buttons">
      <button
        (click)="ChangePassword()"
        type="submit"
        class="button button--primary"
      >
        Submit
      </button>
    </div>
    <div *ngIf='changePassword' class="form__buttons">
      <button
        (click)="Back()"
        type="submit"
        class="button button--primary"
      >
        Back
      </button>
    </div>
  </form>
  <div class="form__buttons" >
    <span
      >Did not receive code ?<a *ngIf="resendOTP" id="resCode" (click)="ResendCode()">
      Resend Code</a
      ></span
    >
  </div>
 
</div>