<admin-header (valueChange)='locationChange($event)'></admin-header>

<section class="margin-top-table-list">
    <div [ngStyle]="{'height.px': pageHeight}">
    <kendo-grid class="gridFontStyle" [data]="gridData" [skip]="state.skip" filterable="menu" [sort]="state.sort" 
        [filter]="state.filter" [sortable]="{allowUnsort: true, mode:'multiple'}" [loading]="loading" (filterChange)="filterChange($event)"
        (dataStateChange)="dataStateChange($event)" [style.height]="'100%'" (scrollBottom)="loadMore()" [navigable]="true"
    >
        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
            <div class="container-fluid p-0">
                <div class="row">
                    <div class="col-md-6 col-6">
                        <h1>Quotes in Progress</h1>
                    </div>
                    <div class="col-md-6 col-6 text-right">
                        <button type="button" [disabled]="OpenJCCCallsList.length===0" class="ExportToExcelButtonCss"
                            kendoGridExcelCommand icon="file-excel">
                            <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                            Export to Excel</button>
                    </div>
                </div>
            </div>
        </ng-template>

        <kendo-grid-column field="LocationName" title="Location Name" width="130">

        </kendo-grid-column>
        <kendo-grid-column field="JobNumber" title="Job Number" width="100">

        </kendo-grid-column>
        <kendo-grid-column field="JobName" title="Job Name" width="130">

        </kendo-grid-column>
        <kendo-grid-column field="ExpectedContract" title="$ Value" width="80">
            <ng-template kendoGridCellTemplate let-dataItem>
                <div class="k-grid-ignore-click">
                    ${{ dataItem.ExpectedContract }}
                </div>
            </ng-template>
            <ng-template kendoGridFilterCellTemplate let-filter let-column="column">
                <kendo-grid-string-filter-cell [showOperators]="false" [column]="column" [filter]="filter">
                </kendo-grid-string-filter-cell>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="StartDate" title="Start Date" width="90" filter="date" operator="eq"
            format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
            <!-- a -->
        </kendo-grid-column>

        <kendo-grid-column field="EndDate" title="End Date" width="100" filter="date" operator="eq"
            format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="Divisions" title="Divisions" width="90">
            <ng-template kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService"
         >
             <kendo-multiselect
                 style="width:160px"
                 [data]="division"
                 textField="Divisions"
                 valueField="Divisions"
                 [valuePrimitive]="true"
                 [value]="dataFilters(filter)"
                 (valueChange)="quoteTypeChange($event, filterService)"
             >
             </kendo-multiselect>
         </ng-template>
        </kendo-grid-column>

        <kendo-grid-excel fileName="Quotes In Progress.xlsx" [fetchData]="allData">
            <kendo-excelexport-column field="LocationName" title="Location Name" width="130">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="JobNumber" title="Job Number" width="100">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="JobName" title="Job Name" width="130">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="ExpectedContract" title="$ Value" width="80">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="StartDate" title="Start Date" width="90">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="EndDate" title="End Date" width="100">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Divisions" title="Divisions" width="90">
            </kendo-excelexport-column>
        </kendo-grid-excel>
    </kendo-grid>
    <div *ngIf="isShow" style="text-align: center;">
        <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
            <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
        </button>
    </div>
    </div>
</section>
<admin-footer class="footer-css"></admin-footer>