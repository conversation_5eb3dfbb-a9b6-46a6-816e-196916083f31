export interface GetMomListRequest {
    LocationIDs: string;
    StartDate: Date|string;
    EndDate: Date | string;
}

export interface GetQuoteSummaryRequest {
    Statuses: string;
        SummaryType: string;
        LocationIDs: string;
        startDate: Date | string | null;
        endDate: Date | string | null; 
}
    
export interface GetQuoteSummaryListRequest {
    Statuses: string;
        LocationIDs: string;
        startDate: Date | string | null;
        endDate: Date | string | null;
}
export interface GetTMMonthGraphRequest {
    LocationIDs: string | null;
        StartDate: Date | string;
        EndDate: Date | string;
}
export interface GetTMMonthGraphRequest2 {
    LocationIDs: string | null;
        // StartDate: Date | string;
        // EndDate: Date | string;
}
export interface GetCustomerServiceRequest {
    startDate: Date | string | null;
        endDate: Date | string | null;
    LocationIDs: string | null;
    }
export interface GetOpenDebtRequest {
    LocationIDs: string | null;
}
export interface GetOpenDebtsRequest {
    LocationIDs: string|null;
    startDate: Date | string | null;
    endDate: Date | string | null;
}
export interface GetMostExpensiveEquipmentRequest {
    DateFilter: Date | string;
    LocationIDs: string | null;
    }