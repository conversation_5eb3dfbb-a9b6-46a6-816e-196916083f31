/*Font-awesome integration*/
 
form
{
    padding: 10px 0px;
}
#contact{
    background-color:#f1f1f1;
    font-family: calibri light , "Roboto";
}
@media (max-width: 1130px) { 
    .section-css{
        margin-top: 10px !important;
    }
    
}


/* @media (max-width: 550px) { 
    .button-css {
        width:12% ;
    }
} */

@media (max-width: 850px) { 
    .button-css {
        width: 19%;
    }
}

/* @media (min-width: 851px) { 
    .button-css {
        width:7% ;
    }
} */

.k-form-field
{
    display: block;
    text-align: left;
    margin-bottom: 5px;
}
.footer-css
{
    position: absolute;
bottom: 0;
/* top: 30px; */
left: 0;
right: 0;
}
.errorMessage
{
    color: #ff0000d1;
    font-size: smaller;
 }
.mt-top
{
    margin-top: -12px;
}
section
{
    margin-top: 169px;
}
#contact .well{
    margin-top:30px;
    border-radius:0;
}

textarea
{
    resize: none;
    min-height: 91px !important;
     width: 98% !important;
    padding: 6px;
 }
/* // span
// {
//     font-family: sans-serif;
//     font-size: 105%;
//     font-weight: 700;
//     position: relative;
    
    
// } */
.defaultSpanPosition
{
     font-size: 105%;
     position: relative;
    position: relative;
    top: 9px;
    font-family: calibri light , "Roboto";
    font-weight: 600;
    color: #000000f7;
}
label{
    display: inline-grid;

}
.mutiselectTextPosition
{
    position: relative;
  
    text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px;

  letter-spacing: 0px;
  font-weight: 600px;
}
.ul-container
{
    padding: 0px 12px;
}
 .row label{
    margin-bottom: -13px;

 }
.dropdownListWidth 
{
    width: 64%;
    position: relative;
    bottom: 3px;
 }
    .k-textbox
    {
        width: 64%!important;
        position: relative;
        bottom: 7px;    }
    .container .background:hover
    {
        background: white !important;
    }
#contact .form-control{
    border-radius: 0;
    border:2px solid #1e1e1e;
}
 #contact button{
    border-radius:0;
    border:2px solid #1e1e1e;
}

#contact .row{
    margin-bottom:30px;
}

@media (max-width: 768px) { 
    #contact iframe {
        margin-bottom: 15px;
    }
    
}
.k-required
{
    color: red;
}




  
 
.mutiselectTextPosition
{
    text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover
{
    transition: 0.5s !important;
    background: black !important;
    
}
 
 
@media only screen and (min-width: 991px) {
    .bar-chart-position
    {
        margin-top: 0;
        margin-bottom: 30px;
    
    }
}
/* .ul-container
{
   // position: absolute;
   // bottom: 15px;
   
   padding: 0px 14px !important;
} */

/* //  .ul-row {
//    position: absolute;
//    top: 39px;
//    left: 15px;
//  } */
.k-i-close
{
    font-size: 12px;
    padding: 2px;
    box-sizing: content-box;
}
.k-select{
    position: relative;
    font-size: 75%;
    bottom: 1px;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.4);
    color: #fff;
    margin-left: .5em;
    margin-right: -.2em;
    
}
.span-content
{
    background: #80808042;    margin-right: 8px;
    padding: 4px;
    font-family: calibri light , "Roboto";
    margin-bottom: -7px;
    font-weight: 500;
    border-radius: 16px;
    margin-top: 4px;
    display: unset !important;
        /* font-family: sans-serif; */
    /* font-size: 105%; */
    /* font-weight: 700; */
    /* position: relative; */
}
.title {
    background-color: #343a40;
    font-family: calibri light , "Roboto";
    font-weight: 700;
    padding: 8px 0px;
    font-size: 1rem;
  }


  @media (max-width: 576px) { 
    .k-textbox,.dropdownListWidth
    {
        width:100% !important;
    }
  
}
 
@media only screen and (max-width:767px)
{
  .mutiselectTextPosition
  {
    font-size: 8px !important;
   
  }
  .mobileAutoPadding
  {
    padding: 7px 15px !important;
  }
 
}








