<admin-header (valueChange)='locationChange($event)'></admin-header>

<section class="dashboard-header section-padding section-top-dashboard">
  <div class="container-fluid">

    <div class="row d-flex align-items-md-stretch" style="margin-bottom: 28px;">

      <!-- <div class="col-md-8 col-12">
        <div class="card bar-chart-example screenScroll " style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">SERVICE CALLS</h2>
            <p>*Date filters not applicable</p>
          </div>
          <div *ngIf="ServiceCallsCount===true">
            <kendo-chart class="serviceCallGraphCss"
              [seriesColors]="['#0291d5','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']">
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [title]="{ text: 'Miles' }">
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [labels]="{ rotation: 'auto' }"
                  [categories]="categoriesServiceCallsChart">
                  <kendo-chart-category-axis-item-labels [font]="10">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [stack]='true' name="T&M - Recent 13 Months" [data]="MonthList">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      T&M - Recent 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>
                <kendo-chart-series-item type="bar" [data]="AHTotalCalls" name="AH - Recent 13 Months">

                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      AH - Recent 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

                 <kendo-chart-series-item type="line" [data]="TotalAhCalls" name="AH - Previous 13 Months">
                   <kendo-chart-series-item-tooltip>
                      <ng-template let-value="value" let-category="category">
                        AH - Previous 13 Months : {{value }}
                      </ng-template>
                    </kendo-chart-series-item-tooltip>
                  </kendo-chart-series-item>
                  <kendo-chart-series-item type="line" [data]="TotalTMCalls" name="T&M - Previous 13 Months">
                      <kendo-chart-series-item-tooltip>
                        <ng-template let-value="value" let-category="category">
                          T&M - Previous 13 Months : {{value }}
                        </ng-template>
                      </kendo-chart-series-item-tooltip>
                  </kendo-chart-series-item>
              </kendo-chart-series>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
            </kendo-chart>
          </div>
        </div>
      </div> -->

      <!-- FK: Service Calls by status-->
      <div class="col-md-8 col-12">
        <div class="card bar-chart-example screenScroll " style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">SERVICE CALLS BY STATUS</h2>
            <p style="left: 80% !important;">*Date filters not applicable</p>
          </div>
          <div>
            <kendo-chart class="serviceCallGraphCss"
              [seriesColors]="['#0291d5','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
              (seriesClick)="onServiceCallByStatus($event)">
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [title]="{ text: 'Miles' }">
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [labels]="{ rotation: 'auto' }"
                  [categories]="categoriesServiceCallsChartByStatusCalls">
                  <kendo-chart-category-axis-item-labels [font]="10">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [stack]='true' [data]="OPENCallsByStatus"
                  name="Open Calls - Recent 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Open Calls - Recent 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

                <!-- <kendo-chart-series-item type="bar" [data]="COMPLETECallsByStatus"
                  name="Complete Calls - Recent 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Complete Calls - Recent 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item> -->

                <kendo-chart-series-item type="bar" [data]="INVOICEDCallsByStatus"
                  name="Invoiced Calls - Recent 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Invoiced Calls - Recent 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

                <kendo-chart-series-item type="line" [data]="preOpenCalls" name="Open Calls - Previous 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Open Calls - Previous 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>
                <!-- <kendo-chart-series-item type="line" [data]="preCompleteCalls"
                  name="Complete Calls - Previous 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Complete Calls - Previous 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item> -->

                <kendo-chart-series-item type="line" [data]="preInvoicedCalls"
                  name="Invoiced Calls - Previous 13 Months">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      Invoiced Calls - Previous 13 Months : {{value }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

              </kendo-chart-series>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
            </kendo-chart>
          </div>
        </div>
      </div>

      <div class="col-md-4 col-12">
        <div class="card bar-chart-example scrollScreen" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">SERVICE CALLS</h2>
          </div>
          <app-open-calls-chart class="QuoteByStatusCssForMobile" [startDate]="range.start" [endDate]="range.end">
          </app-open-calls-chart>
        </div>
      </div>

    </div>

    <div class="row d-flex align-items-md-stretch" style="margin-bottom: 28px;">

      <div class="col-md-4 col-12 bar-chart-position margin-bottom-cards">
        <div class="card quoteStatusGraphheight bar-chart-example scrollScreen"
          style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header  align-items-center">
            <h2 class="display h4">QUOTES BY STATUS </h2>
          </div>
          <div *ngIf="Quotestatus">
            <kendo-chart class="QuoteByStatusCssForMobile"
              [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']" style="height: 323px;
                             margin-top: 2%;" (seriesClick)="onSeriesBarChart($event)">
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category"
                  let-series="series">
                  {{ category }} : {{ (value/TotalStatus) | percent }}:
                </ng-template>
              </kendo-chart-tooltip>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [categories]="categories">
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [gap]="2" [data]="ApproveData">
                  <kendo-chart-series-item-labels>
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
                <kendo-chart-series-item type="column" [data]="AwaitingApprove">
                  <kendo-chart-series-item-labels>
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
              </kendo-chart-series>
            </kendo-chart>
          </div>
        </div>
      </div>

      <div class=" col-md-4 col-12 bar-chart-position">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">QUOTES BY TYPE</h2>
            <p *ngIf="TotalValueQuoteType===0">*No Data Found</p>
          </div>
          <div>
            <kendo-chart class="quotebyType" (seriesClick)="onSeriesQuoteType($event)"
              [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']">

              <kendo-chart-legend position="bottom">
              </kendo-chart-legend>
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category"
                  let-series="series">
                  {{ category }} : {{ (value/TotalValue*10000)/100 | number:'.2-2'}}%
                </ng-template>
              </kendo-chart-tooltip>
              <kendo-chart-series>
                <kendo-chart-series-item type="donut" [data]="pieDataQuoteType" field="value"
                  *ngFor="let series of model; let outermost = last;" [startAngle]="190" [name]="series.name"
                  [data]="value" field="value" categoryField="category" colorField='#9de219' [autoFit]="autofit">
                  <kendo-chart-series-item-labels background="none" [visible]='true' [content]="category">
                    <kendo-chart-series-item-labels *ngIf="outermost" position="outsideEnd" background="none"
                      [content]="labelContent">
                    </kendo-chart-series-item-labels>
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
              </kendo-chart-series>
            </kendo-chart>
          </div>
        </div>
      </div>

      <div class=" col-md-4 col-12">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">QUOTES BY PRIORITY</h2>
            <p *ngIf="Total===0">*No Data Found</p>
          </div>
          <div>
            <kendo-chart class="position-pie-chart" (seriesClick)="onSeries($event)"
              [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']">

              <kendo-chart-legend position="bottom">
              </kendo-chart-legend>
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category"
                  let-series="series">
                  {{ category }} : {{ (value/TotalValue*10000)/100 | number:'.2-2'}}%
                </ng-template>
              </kendo-chart-tooltip>
              <kendo-chart-series>
                <kendo-chart-series-item type="donut" [data]="pieData" field="value"
                  *ngFor="let series of model; let outermost = last;" [startAngle]="190" [name]="series.name"
                  [data]="value" field="value" categoryField="category" colorField='#9de219' [autoFit]="autofit">
                  <kendo-chart-series-item-labels background="none" [visible]='true' [content]="category">
                    <kendo-chart-series-item-labels *ngIf="outermost" position="outsideEnd" background="none"
                      [content]="labelContent">
                    </kendo-chart-series-item-labels>
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
              </kendo-chart-series>
            </kendo-chart>
          </div>
        </div>
      </div>

    </div>

    <div class="row d-flex align-items-md-stretch quotePriorityRow">
      <div class="col-md-4 col-12 bar-chart-position">
        <div class="card quoteStatusGraphheight bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header  align-items-center">
            <h2 class="display h4">QUOTES IN PROGRESS</h2>
          </div>
          <app-open-jc-chart [startDate]="range.start" [endDate]="range.end"></app-open-jc-chart>
        </div>
      </div>
      <div class="col-md-4 col-12 margin-bottom-cards">
        <div class="card bar-chart-example quoteStatusGraphheight scrollScreen"
          style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">OPEN DEBT ({{defaultCurrency}})</h2>
            <p style="left: 60% !important;">*Date filters not applicable</p>
          </div>
          <div *ngIf="countOpenDebt===true">
            <kendo-chart class="openDebtGraphCssForMobile "
              [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
              (seriesClick)="onSeriesOpenDebt($event)">
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category"
                  let-series="series">
                  {{ category }} : ${{value }}{{currency}}
                </ng-template>
              </kendo-chart-tooltip>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [categories]="OpenDebtcategories">
                  <kendo-chart-category-axis-item-labels [font]="10">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="openDebtData">
                  <kendo-chart-series-item-labels format="C0" [margin]="-6">
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
              </kendo-chart-series>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [labels]="{ format: 'C0' }">
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
            </kendo-chart>
          </div>
        </div>
      </div>
      <div class="col-md-4 col-12 bar-chart-position">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgb(218 224 235 / 60%)">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">PPM COMPLETION %</h2>
          </div>
          <app-ppm-percentage-chart [startDate]="range.start" [endDate]="range.end"></app-ppm-percentage-chart>
        </div>
      </div>
    </div>
    <div class="row d-flex align-items-md-stretch quotePriorityRow">
      <div class="col-md-12 col-12">
        <div class="card bar-chart-example screenScroll " style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);">
          <div class="card-header d-flex align-items-center">
            <h2 class="display h4">Asset Expense (in $)</h2>
          </div>
          <div>
            <kendo-chart class="openDebtGraphCssForMobile "
              [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
              (seriesClick)="onMostExpensiveEquipChartClick($event)">
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category"
                  let-series="series">
                  {{ category }} : ${{value | number:'1.0-0' }}
                </ng-template>
              </kendo-chart-tooltip>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [categories]="MostExpensiveEquipCharCategories">
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="MostExpensiveEquipmentChartData">
                  <kendo-chart-series-item-labels format="C0" [margin]="-6">
                  </kendo-chart-series-item-labels>
                </kendo-chart-series-item>
              </kendo-chart-series>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [labels]="{ format: 'C0' }">
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
            </kendo-chart>
          </div>
        </div>
      </div>
    </div>
  </div>

</section>
<admin-footer class="dashboardFooterCss"></admin-footer>
<app-scroll-top></app-scroll-top>
