import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from "./Services/authentication.service";
 @Component({
  selector: 'admin-app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  timedOut = false;
  lastPing?: Date = null;
  isLoggedIn: boolean;
  constructor(private authService: AuthenticationService) {
    this.isLoggedIn = this.authService.isAuthenticated();
    authService.loggedIn$.subscribe(
      data => {
        // this.ngOnInit();

      });


  }

  ngOnInit() {
    this.isLoggedIn = this.authService.isAuthenticated();
    if (this.isLoggedIn) {
      this.authService.isLoggedIn();
    }
  }
}


