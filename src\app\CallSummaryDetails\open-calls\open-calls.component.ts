import { Component, OnInit, HostListener, ViewChild, ViewEncapsulation, DoCheck } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { Title } from "@angular/platform-browser";
import { SharedDataService } from "../../Services/shared-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import * as moment from 'moment';
import { saveAs } from "file-saver";
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { OpenCallsListModel, locationDateFilterEntity, ServiceCallsChartKeys } from "./open-calls.model";
import { GridDataResult, DataStateChangeEvent, FilterService } from '@progress/kendo-angular-grid';
import { ActivatedRoute, Router } from '@angular/router';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'app-open-calls',
  templateUrl: './open-calls.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./open-calls.component.scss']
})
export class OpenCallsComponent implements OnInit, DoCheck {
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  originalDate = new Date();
  locations: string;
  listByCategory: string;
  startDate: string;
  endDate: string;
  public range: any = {};
  OpencallsList: Array<OpenCallsListModel> = [];
  gridView: Array<OpenCallsListModel> = [];
  public state: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridData: GridDataResult;
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public filterGrid: any[];
  public DataList: any = [];
  pagesize: number = 20;
  private dropdownFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  public resolutions: any[];
  public callStatus: any[];
  public callTypes: any[];
  public division: any[];
  fromDateFilter: any;
  applyDateFilter: boolean = false;
  dateParamVal: string;
  constructor(private spinner: SpinnerVisibilityService, private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute, private router: Router) {
    this.applyDateFilter = false;
    this.shareData.removeBacklogData();
    this.allData = this.allData.bind(this);
    let paramVal = this.route.snapshot.paramMap.get('value');

    //MS: From Service Call By Status Calls Dasboard chart - Start
    let catParamVal = paramVal.split("-")[0];
    this.dateParamVal = paramVal.split("-")[1]; //"Oct' 20"
    if (catParamVal.split(" ").length == 2 && catParamVal.split(" ")[1] == "13") {
      this.applyDateFilter = true;
    }
    let value = catParamVal.split(" ")[0];
    this.listByCategory = ServiceCallsChartKeys[value];
    //MS: From Service Call By Status Calls Dasboard chart - End

    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();

    if (this.locationDateFilterData === null)
    {
      if (this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == "") {
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }

    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
  }
  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;
    }

    this.loadData(this.locations, this.range);
  }
  @ViewChild('daterange') public service;
  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridData = process(this.gridView, this.state);
  }

  setDataInLocalStorage() {
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locations
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }


  DownloadSummaryReport(data) {
    if (data.ServiceCallID != null && data.ServiceCallID != undefined) {
      let payload = {
        DocumentType: "Call Summary Report",
        PropertyType: "Service Job Number",
        PropertyValue: data.ServiceCallID
      };
      this.spinner.show();
      this.coredata.getDocLinkByID(payload).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              let result = res['_body'];
              let fileType = result.type.split('/')[1];
              let ReportName = res.headers.get('x-filename') + "." + fileType;
              this.shareData.showSuccess("Call Summary Document downloaded successfully");
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
              saveAs(blob, ReportName);
            }
            else if (res.status === 204) {
              this.shareData.showWarning("No Call Summary Document found for Service Call : " + data.ServiceCallID);
            }
            this.spinner.hide();

          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    }
  }

  loadData(locations, range) {
    let id: string;
    if (locations == undefined) {
      id = "";
    }
    else {
      id = locations;
    }

    this.coredata.getOpenCallsData(id, range).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode == 200) {
          this.dataGrid = res.response;
          this.dataGrid.map(element => {
            element.DATE = this.GetFormattedDate(element.DATE)
          });
          this.resolutions = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Resolution === x.Resolution) === idx);
          this.resolutions = this.resolutions.filter(function (el) {
            return el.Resolution != "";
          });
          this.callStatus = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.CallStatus === x.CallStatus) === idx);
          this.callStatus = this.callStatus.filter(function (el) {
            return el.CallStatus != "";
          });
          this.callTypes = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.CallType === x.CallType) === idx);
          this.callTypes = this.callTypes.filter(function (el) {
            return el.CallType != "";
          });
          this.division = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Divisions === x.Divisions) === idx);
          this.division = this.division.filter(function (el) {
            return el.Divisions != "";
          });
          this.DataList = this.dataGrid;
          this.filterGrid = [];
          this.OpencallsList = [];
          //MS: Opening Service Calls from Dashboard : Service Call By status Call Chart
          if (this.listByCategory === "Open Calls") {
            //MS: Apply Date Filter when open Service Call from Dashboard Service Call By Status Graph
            if (this.applyDateFilter) {
              //Default
              var date = new Date();
              let startDate = new Date(date.getFullYear() - 1, date.getMonth(), 1);
              let endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);

              //from Service graph
              if (this.dateParamVal != null && this.dateParamVal != undefined) {
                let mon = this.dateParamVal.split("'")[0].trim();
                let monthNumb = new Date(Date.parse(mon + " 1, 2012")).getMonth() + 1;
                let yearNumb = "20" + this.dateParamVal.split("'")[1].trim();

                startDate = new Date(parseInt(yearNumb), monthNumb - 1, 1);
                endDate = new Date(parseInt(yearNumb), monthNumb, 0);
              }

              this.filterGrid = filterBy(this.DataList, {
                logic: 'and',
                filters: [
                  { field: 'DATE', operator: 'gte', value: startDate },
                  { field: 'DATE', operator: 'lte', value: endDate },
                  { field: 'CallStatus', operator: 'contains', value: "OPEN" }
                ]
              });
            }
            else {
              // CallStatus: "OPEN"
              this.state.filter.filters = [{ field: 'CallStatus', operator: 'contains', value: "OPEN" }];
              this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            
            }
            this.loadGridData();
          }
          else if (this.listByCategory === "Complete Calls") {
            //MS: Apply Date Filter when open Service Call from Dashboard Service Call By Status Graph
            if (this.applyDateFilter) {

              //default
              var date = new Date();
              let startDate = new Date(date.getFullYear() - 1, date.getMonth(), 1);
              let endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);

              //from Service call Graph
              if (this.dateParamVal != null && this.dateParamVal != undefined) {
                let mon = this.dateParamVal.split("'")[0].trim();
                let monthNumb = new Date(Date.parse(mon + " 1, 2012")).getMonth() + 1;
                let yearNumb = "20" + this.dateParamVal.split("'")[1].trim();

                startDate = new Date(parseInt(yearNumb), monthNumb - 1, 1);
                endDate = new Date(parseInt(yearNumb), monthNumb, 0);
              }

              this.filterGrid = filterBy(this.DataList, {
                logic: 'and',
                filters: [
                  { field: 'DATE', operator: 'gte', value: startDate },
                  { field: 'DATE', operator: 'lte', value: endDate },
                  { field: 'CallStatus', operator: 'contains', value: "COMPLETE" }
                ]
              });
            }
            else {
              // CallStatus: "COMPLETE"
              this.state.filter.filters = [{ field: 'CallStatus', operator: 'contains', value: "COMPLETE" }];
              this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            
            }
            this.loadGridData();
          }
          else if (this.listByCategory === "Invoiced Calls") {

            //MS: Apply Date Filter when open Service Call from Dashboard Service Call By Status Graph
            if (this.applyDateFilter) {
              //defaults
              var date = new Date();
              let startDate = new Date(date.getFullYear() - 1, date.getMonth(), 1);
              let endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);

               //from Service call Graph
               if (this.dateParamVal != null && this.dateParamVal != undefined) {
                let mon = this.dateParamVal.split("'")[0].trim();
                let monthNumb = new Date(Date.parse(mon + " 1, 2012")).getMonth() + 1;
                let yearNumb = "20" + this.dateParamVal.split("'")[1].trim();

                startDate = new Date(parseInt(yearNumb), monthNumb - 1, 1);
                endDate = new Date(parseInt(yearNumb), monthNumb, 0);
              }

              this.filterGrid = filterBy(this.DataList, {
                logic: 'and',
                filters: [
                  { field: 'DATE', operator: 'gte', value: startDate },
                  { field: 'DATE', operator: 'lte', value: endDate },
                  { field: 'CallStatus', operator: 'contains', value: "INVOICED" }
                ]
              });
            }
            else {
              // CallStatus: "INVOICED"
              this.state.filter.filters = [{ field: 'CallStatus', operator: 'contains', value: "INVOICED" }];
              this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
         
            }
            this.loadGridData();
          }

          else {
            this.filterGrid = this.dataGrid;
            this.state.filter.filters = [];
            this.loadGridData();
          }
        }
      }

    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }
  // FK: Auto Load Scrolling
  loadGridData() {
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "DATE" }]);
    if (this.filterGrid.length <= this.pagesize) {
      this.isShow = false;
    }
    else {
      this.isShow = true;
    }
    const next = this.OpencallsList.length;
    this.OpencallsList = [
      ...this.OpencallsList,
      ...this.filterGrid.slice(next, next + this.pagesize)
    ];
    this.gridView = this.OpencallsList;
    this.state.sort = [{ dir: "desc", field: "DATE" }];
    this.gridData = process(this.gridView, this.state);
  }

  loadMore(): void {
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "DATE" }]);
    if (this.OpencallsList.length >= this.filterGrid.length - this.pagesize) {
      setTimeout(() => {
        this.isShow = false;
      }, 1500);
    }
    else {
      this.isShow = true;
    }
    if (this.OpencallsList.length == this.filterGrid.length) {
      this.loading = false;
    } else {
      this.loading = true;
      const next = this.OpencallsList.length;
      this.OpencallsList = [
       ...this.OpencallsList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      setTimeout(() => {
        this.loading = false;
        this.gridView = this.OpencallsList;
        this.state.sort = [{ dir: "desc", field: "DATE" }];
        this.gridData = process(this.gridView, this.state);
      }, 1500);
    }
  }

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }

  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locations = this.locationDateFilterData.locations;
    }
    else {
      this.locations = "";
    }
    this.range["start"] = new Date(this.locationDateFilterData.start);
    this.range["end"] = new Date(this.locationDateFilterData.end);
    this.loadData(this.locations, this.range);
    this.OpencallsList = [];
    this.setDataInLocalStorage();
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;
    const result: ExcelExportData = {
      data: process(this.DataList, state).data
    };
    return result;
  }

  ngDoCheck() {
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    //FK: page height
    this.pageHeight = event.target.innerHeight - 233;
  }
  //FK: all filter
  public filterChange(filter: CompositeFilterDescriptor): void {
    this.OpencallsList = [];
    
    const next = this.OpencallsList.length;
    if (filter.filters.length >= 1) {
      this.filterGrid = [];
      this.filterGrid = filterBy(this.DataList, filter);
      if (this.filterGrid.length <= this.pagesize) {
        this.isShow = false;
      }
      else {
        this.isShow = true;
      }
      this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "DATE" }]);
      this.OpencallsList = [
        ...this.OpencallsList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      this.gridView = this.OpencallsList;
      this.state.sort = [{ dir: "desc", field: "Date" }];
      this.gridData = process(this.gridView, this.state);
    }
    else {
      this.DataList = [];
      this.filterGrid = [];
      this.DataList = this.dataGrid;
      this.filterGrid = this.dataGrid;
      if (this.filterGrid.length <= this.pagesize) {
        this.isShow = false;
      }
      else {
        this.isShow = true;
      }
      this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "DATE" }]);
      this.OpencallsList = [
        ...this.OpencallsList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      this.gridView = this.OpencallsList;
      this.state.sort = [{ dir: "desc", field: "Date" }];
      this.gridData = process(this.gridView, this.state);
    }
  }
  //FK: dropdown filters
  public resolutionChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'Resolution',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }
  // FK: dropdown filters
  public callStatusChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'CallStatus',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }
  // FK: dropdown filters
  public callTypeChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'CallType',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }
  public divisionsChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'Divisions',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }
  // FK: dropdown filters
  public dropdownFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
    this.dropdownFilter.splice(
      0, this.dropdownFilter.length,
      ...flatten(filter).map(({ value }) => value)
    );
    return this.dropdownFilter;
  }

}
