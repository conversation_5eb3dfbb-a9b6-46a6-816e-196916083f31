.custom-filter-btn:focus,
.custom-filter-btn:active {
  outline: none !important;
  box-shadow: none !important;
  background-color: #007bff !important; /* Your original primary color */
  border-color: #007bff !important;
}

.loc-im {
  font-size: 18px; 
  font-family: Roboto; 
  font-weight: 500; 
  padding: 6px 0;
}

.loc-caret-im {
  color: #575757; 
  left: 6px !important; 
  top: 0 !important; 
  cursor: pointer;
}

.date-caret-im {
  color: #575757; 
  left: 0 !important; 
  top: 0 !important;
  cursor: pointer;
}

.header-im {
  height: 7vh!important;
}

.header-font-size {
  padding: 7px 13px;
  font-size: 76%;
  font-family: "Roboto", calibri light;
  text-decoration: none;
}
.imgHight {
   height: 41px;
}
.eValuateButton {
  margin-right: 23px;
}


.buttonFooter {
  background: #4ab4de !important;
  color: white !important;
}
.eValuateButton:hover {
  background: none !important;
}
.navbar-nav {
  margin-bottom: 1px !important;
}

.fa-caret-down {
  position: relative;
  left: 9px;
  top: 1px;
}
.submenu li {
  padding: 0px 5px;
  border-bottom: 1px solid rgba(128, 128, 128, 0.2784313725490196);
}
.report-builder:active
{
  background: #17b0e9;
}
.report-builder:hover .submenu {
  color: black;
  background: #17b0e9;
  display: block;
  top: 95%;
  width: 168px;
  right: 18%;
    border-radius: 0px;
  margin-top: 0px;
  padding: 0;
}
.logOutFormCss {
  display: none;
  width: 168px;
  position: absolute;
  background: #17b0e9;
  top: 100%;
  right: 46px;
}
.lihover:hover{
  background-color: rgba(0, 0, 0, 0.607) !important;
  
}
.nav-item:hover {
  background: #17b0e9;
}
.nav-item {
  padding: 10px 0px !important;
}

//For IM Only
.nav-item-im:hover,
.nav-item-im a.link-active {
  background: #e0e0e0;
  font-weight: bold;
}
.nav-item-im,
.nav-item-im a {
  text-decoration: none;
  color: black;
  border-radius: 20px;
  transition: background-color 0.3s ease, font-weight 0.3s ease;
}

::ng-deep .is-im-dashboard {
  .k-menu:not(.k-context-menu)>.k-item {
      color: black !important;
    }

    .k-menu:not(.k-context-menu)>.k-item>.k-state-active {
      color: black!important;
    }
  
  .menu-item {
    display: block;
    width: 100%;
    color: black;
    font-family: Roboto!important;
    border-radius: 20px;
    transition: background-color 0.3s ease, font-weight 0.3s ease;
    cursor: pointer;
  }
}

:host ::ng-deep .k-menu-popup.k-menu-group {
  background-color: #f9f9f9 !important;
  color: black !important;
}

.nav-item-im.hover-logout:hover .logOutFormCss {
  background: #f9f9f9 !important;
  top: 78% !important;
  right: 20px !important;
}

.lihover-im:hover{
  background-color: #e0e0e0;
}

.user-icon-container {
  background-color: #ddd;
  width: 35px;
  height: 32px;
  border-radius: 8px !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.user-icon-container i {
  font-size: 16px;
  color: #666;
}

.submenu a {
  padding: 8px;
  color: white;
}
.submenu li a {
  padding: 8px;
  text-align: left;
}

.submenu li:hover {
  background: rgba(128, 128, 128, 0.2196078431372549);
}

ul li p {
  margin-bottom: -20px;
  position: absolute;
  right: 66px;
  color: rgba(212, 212, 212, 1);
  font-size: 79%;
  font-weight: 500;
}
@media only screen and (max-width: 991px) {


  



  ul li p {
    text-align: center;
    left: 0px;
    position: relative;
  }
}
ul li a:hover {
  text-decoration: none;
  //       transition: 0.5s;
  //       color: gray;
}

ul li {
  padding: 0px;
}

.fa-icon {
  font-size: 14px;
}
// .fa-sign-out {
//   position: relative;
//   left: 4px;
// }

.mutiselectTextPosition {
  text-transform: capitalize;
  font-family: calibri light, "Roboto";
  font-size: 13px;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover {
  transition: 0.5s !important;
  background: black !important;
}

@media only screen and (min-width: 991px) {
  .bar-chart-position {
    margin-top: 0;
    margin-bottom: 30px;
  }
}
.ul-container {
  padding: 0 12px;
}
.ul-row {
  height: 101px;
  overflow-y: scroll;
}
.k-i-close {
  font-size: 12px;
  padding: 2px;
  box-sizing: content-box;
  cursor: pointer;
}
.k-select {
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  margin-left: 0.5em;
  margin-right: -0.2em;
}
.span-content {
  background: rgba(128, 128, 128, 0.25882352941176473);  margin-right: 8px;
  padding: 4px;
  font-family: calibri light , "Roboto";
  margin-bottom: -7px;
  font-weight: 500;
  border-radius: 16px;
  margin-top: 4px;
}
.count-number {
  font-size: 2em;
}
@media only screen and (max-width: 1199px) {
  .bar-chart-position {
    margin-top: 25px;
  }
}
.k-chart text {
  font: sans-serif !important;
}

.position-pie-chart {
  height: 337px;
  margin-top: 2%;
  position: relative;
}
.count-number {
  margin-top: -8px;
}

.top-text-open-debt {
  position: relative;
  left: 8%;
  top: 1px;
  margin: 0px;
  font-size: 11px;
}
.dashboard-header .h4 {
  font-family: calibri light , "Roboto";
  font-size: 80%;
  font-weight: bold !important;
}

.text-uppercase a {
  color: #009adc;
}
.text-uppercase a:hover {
  transition: 0.7s;
  color: black;
}
.count-number a {
  color: black;
}
.container-fluid .count-data {
  margin-bottom: 14px;
}
.margin-bottom-location {
  margin-bottom: 1%;
}
.dashboard-counts strong {
  font-size: 72%;
}
.dashboard-counts {
  z-index: 999;
  margin-top: 94px;
  top: 3em;
  position: fixed;
  // background: #5e8fa6;
  background: #343a40;
  width: 100%;
}
.top-text {
  position: relative;
  left: 67%;
  top: 1px;
  margin: 0px;
  font-size: 11px;
}
.header-datefilter {
  margin-bottom: 3px;
}
.datePickerRightPosition {
  position: relative;
  left: 1px;
}

.hoverQuote:hover {
  cursor: pointer !important;
}

.margin-top-table {
  margin-top: 94px;
}
.k-button {
  font-size: 12px;
}

.margin-top-table-list {
  margin-top: 176px;
  width: 91%;
  position: relative;
  margin-left: 4%;
  left: 6px;
}
.dateInputCss {
  width: 87px;
  height: calc(8px + 1.75em);
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
  font-size: 15px;
}

.start-dateInputCss-im {
  width: 78px;
  height: calc(8px + 1.75em);
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
  font-size: 20px;
}

.end-dateInputCss-im {
  width: 70px;
  height: calc(8px + 1.75em);
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
  font-size: 20px;
}

.btn-sm {
  border-radius: 4px;
  color: white;
  background-color: #00ccff;
  border-color: #008cd1;
}
.btn-sm:hover {
  border-radius: 4px;
  background-color: #037db5;
  border-color: #037db5;
}

.label {
  font-size: 14px;
  font-weight: 600;
  color: white;
  position: relative;
  right: 8px;
}
.OpenCallsGrid {
  position: relative;
  cursor: pointer;
  font-size: 70%;
  font-family: calibri light , "Roboto";
  font-weight: 500;
}

section .title-position {
  position: relative;
  bottom: -7px;
  position: relative;
  bottom: -7px;
  max-width: 1227px;
}

.circle-tile {
  margin-bottom: 15px;
  text-align: center;
}
.circle-tile-heading {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 100%;
  color: #ffffff;
  height: 80px;
  margin: 0 auto -40px;
  position: relative;
  transition: all 0.3s ease-in-out 0s;
  width: 80px;
}
.circle-tile-heading .fa {
  line-height: 80px;
}
.info-box:hover {
  cursor: pointer;
  transition: 0.5s;
  // box-shadow: 0 0 8px 5px #48abe0;
  box-shadow: 1px 2px 3px 5px rgba(32, 31, 31, 0.571);
}
// .info-box:hover {
//   cursor: pointer;
//   box-shadow: 1px 2px 3px 5px rgba(0, 0, 0, 0.2);
//   transition: 0.5s;
// // }


.logOutFormCss a {
  padding: 8px;
  font-size: 0.9em;
}
.circle-tile-number {
  font-size: 26px;
  font-weight: 700;
  line-height: 1;
  padding: 5px 0 15px;
}
.circle-tile-description {
  text-transform: uppercase;
}
.circle-tile-footer {
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.5);
  display: block;
  padding: 5px;
  transition: all 0.3s ease-in-out 0s;
}
.circle-tile-footer:hover {
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.5);
  text-decoration: none;
}
.circle-tile-heading.dark-blue:hover {
  background-color: #2e4154;
}
.circle-tile-heading.green:hover {
  background-color: #138f77;
}
.circle-tile-heading.orange:hover {
  background-color: #da8c10;
}
.circle-tile-heading.blue:hover {
  background-color: #2473a6;
}
.circle-tile-heading.red:hover {
  background-color: #cf4435;
}
.circle-tile-heading.purple:hover {
  background-color: #7f3d9b;
}
.tile-img {
  text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.9);
}

.dark-blue {
  background-color: #34495e;
}
.green {
  background-color: #16a085;
}
.blue {
  background-color: #2980b9;
}
.orange {
  background-color: #f39c12;
}
.red {
  background-color: #e74c3c;
}
.purple {
  background-color: #8e44ad;
}
.dark-gray {
  background-color: #7f8c8d;
}
.gray {
  background-color: #95a5a6;
}
.light-gray {
  background-color: #bdc3c7;
}
.yellow {
  background-color: #f1c40f;
}
.text-dark-blue {
  color: #34495e;
}
.text-green {
  color: #16a085;
}
.text-blue {
  color: #2980b9;
}
.text-orange {
  color: #f39c12;
}
.text-red {
  color: #e74c3c;
}
.text-purple {
  color: #8e44ad;
}
.text-faded {
  color: rgba(255, 255, 255, 0.7);
}
.hasOneCustomer {
  position: relative;
  bottom: 8px;
  left: 10px;
  font-size: 10px;
  font-family: calibri light , "Roboto";
}
.card {
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);
}
.card {
  border: 0;
  margin-bottom: 30px;
  margin-top: 30px;
  border-radius: 6px;
  color: #333;
  background: #fff;
  width: 100%;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid #eee;
  border-radius: 0.25rem;
}
.card [class*="card-header-"] {
  margin: 0 15px;
  padding: 0;
  position: relative;
}
.card .card-header {
  z-index: 3 !important;
}
.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}
.card .card-header {
  border-bottom: none;
  background: transparent;
}
.card [class*="card-header-"] .card-icon,
.card [class*="card-header-"] .card-text {
  border-radius: 3px;
  background-color: #999;
  padding: 5px 20px;
  margin-top: -14px;
  float: left;
}
.card .card-header-warning .card-icon,
.card .card-header-warning .card-text,
.card .card-header-warning:not(.card-header-icon):not(.card-header-text) {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(255, 152, 0, 0.4);
}
.card.bg-warning,
.card .card-header-warning .card-icon,
.card .card-header-warning .card-text,
.card .card-header-warning:not(.card-header-icon):not(.card-header-text),
.card.card-rotate.bg-warning .back,
.card.card-rotate.bg-warning .front {
  background: #0291d5;
}
.card-stats .card-header .card-category:not([class*="text-"]) {
  color: #999;
  font-size: 14px;
}
.card-stats .card-header.card-header-icon .card-category,
.card-stats .card-header.card-header-icon .card-title,
.card-stats .card-header.card-header-text .card-category,
.card-stats .card-header.card-header-text .card-title {
  margin: 0;
}
.card-stats .card-header .card-icon + .card-category,
.card-stats .card-header .card-icon + .card-title {
  padding-top: 10px;
}
.card-stats .card-header.card-header-icon .card-category,
.card-stats .card-header.card-header-icon .card-title,
.card-stats .card-header.card-header-text .card-category,
.card-stats .card-header.card-header-text .card-title {
  margin: 0;
}
.card .card-header.card-header-icon .card-title,
.card .card-header.card-header-text .card-title {
  color: #3c4858;
}
.h3,
.h4,
h3,
h4 {
  line-height: 1.4em;
}
.small,
small {
  font-size: 80%;
  font-weight: 400;
}
.card .card-header.card-header-icon .card-title,
.card .card-header.card-header-text .card-title {
  margin-top: 10px;
  color: #0291d5;
  font-size: 13px;
}
.title-header {
  position: relative;
  left: 41%;
}
.material-icons {
  color: white;
  color: white;
  position: relative;
  right: 5px;
  bottom: 4px;
}

.info-box {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  height: 50px;
  display: flex;
  cursor: default;
  padding: 0px;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  margin-top: 10%;
  border-radius: 5px;
}

.info-box .icon {
  display: inline-block;
  text-align: center;
  width: 51px;
  padding: 18px;
  background: #0291d5;
}
.info-box .content {
  display: inline-block;
  padding: 0px 0px;
}
.info-box .content .text {
  font-size: 15px;
  margin-top: 0px;
  font-weight: 500;
  color: #555;
}
.bg-pink .content .text,
.bg-pink .content .number {
  color: #fff !important;
}
.info-box .content .number {
  font-weight: normal;
  font-size: 21px;
  margin-top: -1px;
  color: #555;
}
.hover-logout:hover {
  .logOutFormCss {
    display: block !important;
    width: 138px !important;
    position: absolute !important;
    background: #28afdf !important;
    top: 98% !important;
    right: 48px !important;
  }
}
.bg-pink .content .text,
.bg-pink .content .number {
  color: black !important;
}
 
.loggedInUserCss {
  position: relative;
  bottom: 10px;
  left: 8px;
  font-size: 8px;
  font-family: calibri light , "Roboto";
}

.hover-logout > p
{
  z-index: -1;
}
.ToolTipCss * {
  font-size: 12px;
  padding: 2px 5px;
 }
 .logOutFormCss li:hover
 {
  background-color: rgba(0, 0, 0, 0.07);
 }

 .userInfo
 {
  font-size: 18px !important;
  letter-spacing: 8px !important;
  position: relative !important;
  top: 2px !important;
 }
 .logOutFormCss  span
 { 
  font-size: 14px !important;
  }
 
  .div
  {
    width: 20% !important;
    padding: 0px 15px !important;
   }
   .im-Dashboard{
    display: flex;
    background-color: #F5F3EF;
    z-index: 999;
    margin-top: 94px;
    top: 3em;
    position: fixed;
    width: 100%;
   }
  @media screen and (max-width: 1015px) {
    .div
    {
      width: 33.330% !important;
      padding: 0px 15px !important;
    }
  }
  @media screen and (max-width: 656px) {
    .div
    {
      width: 50% !important;
      padding: 0px 15px !important;
      
    }
  }
  @media only screen and (max-width: 1200px) {
    .autoWidth {
      max-width: 16.6667% !important;
     }
     .autoWidth  .number
     {
       font-size: 88% !important;
     }

  }
  @media only screen and (max-width: 1130px) {
    .autoWidth {
      max-width: 33.33% !important;
     }
     .autoWidth .number
     {
      font-size: 21px !important;
     }
     .dashboard-counts
     {
      z-index: 999 !important;
      margin-top: 33px !important;
      top: 7% !important;
      position: relative !important;
      // background: #5e8fa6 !important;
      background: #343a40 !important;
      }
      .im-Dashboard{
        z-index: 999 !important;
        margin-top: 33px !important;
        top: 7% !important;
        position: relative !important;
        background: #F5F3EF !important;
      }
  
  }


  @media only screen and (max-width: 562px) {
    .autoWidth {
      max-width: 50% !important;
     }
  }
  @media only screen and (max-width: 991px) {
    // kendo-menu {
    //   top:-8px;
    //   position: relative !important;
    //   left: 41% !important;
    // }
   
    .header-font-size
    {
      margin-bottom: 17px;
    }
    .report-builder
    {
      position: relative;
      bottom: 0px;
      display: flex;
      justify-content: center;
      margin-bottom: 22px;
      left: 4px;
    }
    .hover-logout[_ngcontent-c11]:hover .logOutFormCss[_ngcontent-c11] {
      display: block !important;
      width: 138px !important;
      position: absolute !important;
      background: #17b0e9 !important;
      top: 99% !important;
      margin: 0 41% !important;
      right: -38px !important;
   }
  }
  @media only screen and (max-width: 779px) {
  
    .mutiselectTextPosition {
     
      font-size: 8px !important;
   
  }
  .template
  {
     font-size: 11px !important;
    position: relative !important;
    right: 10px !important;
    font-weight: 500 !important;
  }
  .customerName-MDcSS
  {
    position: relative !important;
    right: 17PX !important;
  }
  .selectCss
  {
    text-align: right !important;
    padding-right: 5px !important;
    position: absolute !important;
  }
  .state
  {
    position: relative !important;
    left: -13px !important;

  }
  .stateText
  {
    position: relative !important;
    left: 6px !important;
  }
  .stateCss
  {
    position: relative !important;
    right: 15px !important;
  }
  }
  .stateText
  {
    position: relative;
    left: 20px;
  }
  .state{
    position: relative;
    right: 4%
  }

  @media (min-width: 1368px) and (max-width: 1454px) {
    .header-font-size {
      font-size: 14px;
    }

    .img-height-im {
      height: 40px !important;
      width: 64px !important;
      margin: 6px 0 6px 40px !important;
    }

    ::ng-deep .k-menu .k-item > .k-link {
      font-size: 14px;
    }

    .user-icon-container {
      height: 35px;
      width: 40px;

      i {
        font-size: 20px;
      }
    }

    .logoutFormCss a {
      font-size: 14px;
    }

    .header-datefilter {
      margin-top: 2vh !important;
      margin-right: -2.8vw !important;
    }

    .loc-im {
      font-size: 20px !important;
    }

    .start-dateInputCss-im {
      width: 84px;
      font-size: 22px;
    }

    .end-dateInputCss-im {
      width: 76px;
      font-size: 22px;
    }

    .loc-caret-im {
      left: 7px !important; 
      top: 0px !important;
      font-size: 17px !important;
    }

    .date-caret-im {
      color: #575757; 
      left: 2px !important; 
      top: 1px !important;
      font-size: 17px !important;
    }
  }

  @media (min-width: 1455px) and (max-width: 1600px) {
    .header-font-size {
      font-size: 17px;
    }

    .img-height-im {
      height: 45px !important;
      width: 70px !important;
      margin: 7px 0 7px 42px !important;
    }

    ::ng-deep .k-menu .k-item > .k-link {
      font-size: 17px;
    }

    .user-icon-container {
      height: 40px;
      width: 45px;

      i {
        font-size: 22px;
      }
    }

    .logoutFormCss a {
      font-size: 17px;
    }

    .header-im {
      height: 7.5vh!important;
    }

    .header-datefilter {
      margin-top: 2.4vh !important;
      margin-right: -2.5vw !important;
    }

    .loc-im {
      font-size: 22px !important;
    }

    .start-dateInputCss-im {
      width: 90px;
      font-size: 24px;
    }

    .end-dateInputCss-im {
      width: 84px;
      font-size: 24px;
    }

    .loc-caret-im {
      left: 8px !important; 
      top: -1px !important;
      font-size: 18px !important;
    }
    
    .date-caret-im {
      color: #575757; 
      left: 3px !important; 
      top: 2px !important;
      font-size: 18px !important;
    }
  }

  @media (min-width: 1601px) and (max-width: 1800px) {
    .header-font-size {
      font-size: 18.5px;
    }

    .img-height-im {
      height: 48px !important;
      width: 76px !important;
      margin: 8px 0 8px 44px !important;
    }

     ::ng-deep .k-menu .k-item > .k-link {
      font-size: 18.5px;
    }
    .user-icon-container {
      height: 45px;
      width: 48px;

      i {
        font-size: 24px;
      }
    }

    .logoutFormCss a {
      font-size: 18.5px;
    }

    .header-im {
      height: 8vh!important;
    }

    .header-datefilter {
      margin-top: 3vh !important;
      margin-right: -2.1vw !important;
    }

    .loc-im {
      font-size: 24px !important;
    }

    .start-dateInputCss-im {
      width: 96px;
      font-size: 26px;
    }

    .end-dateInputCss-im {
      width: 90px;
      font-size: 26px;
    }

    .loc-caret-im {
      left: 10px !important; 
      top: 0px !important;
      font-size: 20px !important;
    }

    .date-caret-im {
      color: #575757; 
      left: 2px !important; 
      top: 2px !important;
      font-size: 20px !important;
    }
  }

  @media (min-width: 1801px) and (max-width: 2000px) {
    .header-font-size {
      font-size: 20px;
    }

    .img-height-im {
      height: 55px !important;
      width: 86px !important;
      margin: 9px 0 9px 54px !important;
    }
    ::ng-deep .k-menu .k-item > .k-link {
      font-size: 20px;
    }
    .user-icon-container {
      height: 50px;
      width: 50px;

      i {
        font-size: 28px;
      }
    }

    .logOutFormCss a {
      font-size: 20px !important;
    }

    .header-im {
      height: 9vh!important;
    }

    .header-datefilter {
      margin-top: 4vh !important;
      margin-right: -1.9vw !important;
    }
    .loc-im {
      font-size: 26px !important;
    }

    .start-dateInputCss-im {
      width: 102px;
      font-size: 28px;
    }

    .end-dateInputCss-im {
      width: 96px;
      font-size: 28px;
    }

    .loc-caret-im {
      left: 12px !important; 
      top: 0px !important;
      font-size: 24px !important;
    }

    .date-caret-im {
      color: #575757; 
      left: 5px !important; 
      top: 3px !important;
      font-size: 24px !important;
    }
  }

  @media (min-width: 1401px) and (max-width: 1600px) {
    .info-box .content .text {
      font-size: 13px;
      margin-top: 2px;
      font-weight: 500;
      color: #555;
    }
  }
  @media (min-width: 1300px) and (max-width: 1400px) {
    .info-box .content .text {
      font-size: 12px;
      margin-top: 2px;
      font-weight: 500;
      color: #555;
    }
  }

  @media (min-width: 1240px) and (max-width: 1299px) {
    .info-box .content .text {
      font-size: 11px;
      margin-top: 7px;
      font-weight: 500;
      color: #555;
    }
    .autoWidth .number{
       font-size: 88% !important;
    }
  }

  @media (min-width: 1131px) and (max-width: 1239px) {
    .info-box .content .text {
      font-size: 9px;
      margin-top: 10px;
      font-weight: 500;
      color: #555;
    }
    .autoWidth .number{
       font-size: 88% !important;
    }
  }



  .priority-container {
    display: flex;
    flex-direction: column;
    // padding: 10px;
  }
  
  .box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    height: 60px;
  }
  
  .priority-icon {
    width: 40px;
    height: 100%;
  }
  
  .priority {
    padding: 5px 10px;
    border-radius: 14px;
    cursor: move;
  }
  
  .high {
    display: flex;
    align-items: center;
    border: 1px solid #ff000078;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-evenly;
  }
  
  .medium {
    display: flex;
    align-items: center;
    border: 1px solid #ffbf00;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-between;
  }
  
  .low {
    display: flex;
    align-items: center;
    border: 1px solid #4dff4d;
    box-shadow: inset 6px 6px 10px 0 rgba(0, 0, 0, 0.2), inset -6px -6px 10px 0 rgba(255, 255, 255, 0.5);
    width: 90px;
    justify-content: space-evenly;
  }
  .high-dot{
    height: 11px;
    width: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }
  
  .medium-dot{
    height: 11px;
    width: 10px;
    background-color: #ffbf00;
    border-radius: 50%;
    display: inline-block;
    margin-right: 3px;
  }
  .low-dot{
    height: 11px;
    width: 10px;
    background-color: #4dff4d;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }
  
  .equipment-container {
    padding: 10px;
    border-radius: 5px;
  }
  
  .equipment-container h2 {
    margin-top: 0;
  }
  
  .equipment-container input[type="text"] {
    width: 100%;
    padding: 5px;
    margin-bottom: 10px;
    box-sizing: border-box;
    margin-left: -10px;
    border: 1px solid lightgray;
  }
  
  .equipment-list {
    margin-bottom: 20px;
  }
  
  .equipment-list label {
    display: block;
    margin-bottom: 5px;
    position: relative;
  }
  .select-all hr{
    width: 10px;
    position: absolute;
    top: -10px;
    left: -10px;
    height: 25px;
    border-right: 0px !important;
    border-left: 1px solid lightgray;
    border-top: 1px solid lightgray;
  }
  
  .equipment-list hr{
    width: 10px;
    position: absolute;
    top: -10px;
    left: -10px;
    height: 23px;
    border-right: 0px !important;
    border-left: 1px solid lightgray;
    border-top: 1px solid lightgray;
  }
  
  .equip-buttons {
    display: flex;
    justify-content: end;
  }
  
  .equip-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
  }
  
  .equip-buttons button:first-child {
    margin-right: 10px;
    border: 1px solid lightgray;
    background: #fff;
  }
  
  .equip-buttons button:last-child {
    border: 1px solid lightgray;
    background: #fff;
  }
  .select-all label{
     position: relative;
  }
  
  .equipment-list label:last-child hr {
    height: 0;
  }