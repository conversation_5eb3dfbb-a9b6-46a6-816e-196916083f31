// @media only screen and (min-width: 991px) {
//   .bar-chart-position {
//     margin-top: 0;
//     margin-bottom: 30px;
//   }
// }

// @media only screen and (max-width: 1199px) {
//   .bar-chart-position {
//     margin-top: 25px;
//   }
// }

.position-pie-chart {
  height: 323px;
  margin-top: 2%;
  position: relative;
}

.quotebyType {
  height: 323px;
  margin-top: 2%;
  position: relative;
}

.top-text-open-debt {
  position: relative;
  left: 8%;
  top: 1px;
  margin: 0px;
  font-size: 12px;
}
.dashboard-header .h4 {
  font-family: calibri light , "Roboto";
  font-size: 14px;
  font-weight: bold !important;
}

.top-text {
  position: relative;
  left: 67%;
  top: 1px;
  margin: 0px;
  font-size: 12px;
}

.dashboardFooterCss
{
position:absolute;
left: 0;
right: 0;
top: auto;
bottom: auto;
}

@media only screen and (max-width: 562px) {
  .top-text  {
    position: relative;
    left: 17%;
    top: 1px;
    margin: 0px;
    font-size: 12px;
  }
}

p
{
  position: absolute;
  left: 78%;
  font-size: 13px;
}
// @media screen and (orientation: landscape) {
//   .bar-chart-position {
//     margin-top: 300px;
//   }
// }

@media only screen and (max-width:1130px)
{
  .section-top-dashboard
{
  margin-top: 5% !important;   
}
.section-padding {
  padding: 0rem 0;
}
}
.section-top-dashboard
{
  margin-top: 10%;
}
.quotePriorityRow
{
  margin-bottom: 30px;
}


.serviceCallGraphCss
{
  height: 333px !important;
  position: relative !important;

  /* top: 0px; */
  /* overflow: scroll; */
}
.openDebtGraphCssForMobile
{
  height: 331px;
}
 
@media only screen and (max-width:1093px)
{ .screenScroll{
    overflow: scroll !important;
  }
}

@media only screen and (max-width:1083px)
{ .scrollScreen{
    overflow: scroll !important;
  }
}


@media only screen and (max-width:1092px)
{
  .serviceCallGraphCss
  {
    width: 43rem !important;
  }
  .openDebtGraphCssForMobile
  {
    width: 19rem !important;
  }
  p {
    position: absolute;
    left: 78%;
    font-size: 10px;
}
.QuoteByStatusCssForMobile
{
  width: 17rem !important;
}
}

@media (min-width: 1600px) and (max-width: 2100px) {
  p{
    font-size: 15px;
  }
}
@media (min-width: 2101px){
  p{
    font-size: 16px;
  }
}