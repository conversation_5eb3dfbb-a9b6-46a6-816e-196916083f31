<header class="header">
  <nav [class]="IsIMDashboard ? 
              'navbar navbar-expand-lg navbar-light bg-white fixed-top' : 
              'navbar navbar-expand-lg navbar-dark bg-dark fixed-top'" style="padding: 0px">
    <div class="container-fluid" [ngStyle]="{'padding': IsIMDashboard ? '0' : '' }">
      <a *ngIf="!IsIMUser" class="navbar-brand" [routerLink]='["/Dashboard"]'>
        <img style="position: relative; bottom: 1px !important;" 
            [style]="IsIMDashboard ? 'width: 60px; height: 35px; margin: 5px 0 5px 40px;' : 'width: 118px !important; height: 18px;'"
        [src]="IsIMDashboard ? '../../../assets/images/airmasterLogo-previous.png' : '../../../assets/images/logoHeader.png'" 
        [class]="IsIMDashboard ? 'img-height-im' : 'imgHight'" alt="" />
      </a>
      <a *ngIf="IsIMUser" class="navbar-brand">
        <img style="width: 60px !important; height: 35px; margin-left: 40px; position: relative; bottom: 1px !important;" 
         src="../../../assets/images/airmasterLogo-previous.png" class="imgHight" alt="" />
      </a>
      <button class="navbar-toggler collapsed" type="button" data-toggle="collapse" data-target="#navbarResponsive"
        aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="navbar-collapse collapse" id="navbarResponsive">
        <ul class="navbar-nav ml-auto">
          <li *ngIf="!IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item active'">
            <a class="padding header-font-size" [routerLink]='["/Dashboard"]' [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-icon fa-bar-chart"></i>
              Dashboard
            </a>
          </li>

          <li *ngIf="!IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item active'">
            <a href="javascript:void(0)" (click)="redirectToUpTick()" class="padding header-font-size" [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-sharp fa-solid fa-check"></i>
              UpTick
            </a>
          </li>

          <li *ngIf="!IsIMUser || shareData.isIMReport" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item active'">
            <a class="padding header-font-size" [routerLink]='["/IM-Dashboard"]' routerLinkActive="link-active" 
              [routerLinkActiveOptions]="{ exact: true }" [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-icon fa fa-desktop"></i>
              IM Interface
            </a>
          </li>

          <!-- <li *ngIf="tokenData.Role==='6'" class="nav-item"><a class="padding header-font-size"
              [routerLink]='["/clientPerceptionReport"]'><i class="fa fa-icon fa-wpforms"></i>
              Client Perception Report</a></li> -->
          <li *ngIf="!IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item active'">
            <a href="javascript:void(0)" (click)="redirectToEvaluateSystem()" class="padding header-font-size" 
                  [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-check-square-o" aria-hidden="true"></i>
              eValuate
            </a>
          </li>
          <li *ngIf="!IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item report-builder'">
            <kendo-contextmenu class="ContextMenu" [items]="items">
            </kendo-contextmenu>
            <kendo-menu [items]="items" (select)="onSelectMenu($event)" [ngClass]="{ 'is-im-dashboard': IsIMDashboard }">
              <ng-template kendoMenuItemTemplate let-item="item">
                <span class="menu-item">
                  <i *ngIf="item.text==='Reports' && !IsIMDashboard" style="position: relative;right: 4px;" class="fa-icon fa fa-file"></i>
                  {{item.text}}
                </span>
              </ng-template>
            </kendo-menu>
          </li>
          <li *ngIf="isInternalUser && !IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item'">
            <a class="padding header-font-size" [routerLink]='["/MoM"]' [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-icon fa-file-text-o"></i>
              MoM
            </a>
          </li>
          <li *ngIf="!IsIMUser" [class]="IsIMDashboard ? 'nav-item-im' : 'nav-item placeCall'">
            <a class="padding header-font-size" [routerLink]='["/PlaceCall"]' [style]="IsIMDashboard ? 'color: black !important;' : ''">
              <i *ngIf="!IsIMDashboard" class="fa fa-icon fa-volume-control-phone"></i>
              Place a Call
            </a>
          </li>

          <li [class]="IsIMDashboard ? 'nav-item-im hover-logout' : 'nav-item hover-logout'" 
              [style]="IsIMDashboard ? 'background: none !important; font-weight: bold;' : ''">
            <!-- <p>{{Name}}<span *ngIf="hasOneCustomer">{{CustomerName}}</span></p> -->
            <a href="javascript:void(0)" [class]="IsIMDashboard ? 'user-icon-container' : 'padding header-font-size'"
              [style]="IsIMDashboard ? 'margin-right: 25px; padding: 0;' : ''">
              <i [class]="IsIMDashboard ? 'fa-regular fa-user' : 'fa fa-user fa-icon'"
                aria-hidden="true" [style]="IsIMDashboard ? '' : 'padding:4px 3px;line-height: 7px; position: relative; left: 4px;'"></i>
              {{IsIMDashboard ? '' : LoggedInUserName}}
            </a>
            <p id="customerNameCss"><span class="loggedInUserCss" *ngIf="hasOneCustomer">{{CustomerName}}</span></p>
            <ul class="logOutFormCss">
              <li [class]="IsIMDashboard ? 'lihover-im' : 'lihover'">
                <a [routerLink]='["/userInfo"]'>
                  <i class="fa-solid fa-circle-user userInfo" aria-hidden="true"></i>
                  <span> User Info </span>
                </a>
              </li>

              <li [class]="IsIMDashboard ? 'lihover-im' : 'lihover'">
                <a href="javascript:void(0)" (click)="logout()">
                  <i class="fa fa-sign-out userInfo" aria-hidden="true"></i>
                  <span>Log Out</span>
                </a>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</header>
<!-- <div *ngIf="IsIMDashboard" style="background-color: #f2f2f2;
padding: 15px;
width: 100%;
z-index: 999;
margin-top: 7px;
top: 5%;
/* bottom: 10%; */
position: fixed;"></div> -->
<section class="dashboard-counts" style="margin-top: 0px;" [ngClass]="IsIMDashboard ? 'im-Dashboard' : ''">
  <!-- <div *ngIf="!IsIMDashboard" class="col-md-1" style="position: relative;">
    <img src="../../../assets/images/airmasterLogo.png" style="margin: 0;
    position: absolute;
    top: 57%;
    left: 50%;
    bottom: 45%;
    -ms-transform: translate(-50% , -50%);
    transform: translate(-50%, -50%);
    width: 7vw;
    margin-left: 23px;
    height: 9vh;
">
  </div> -->
  <div class="container-fluid" [style]="IsMonthlyReport ? 'margin-bottom: 2vh' : IsMonthlyIMReport ? 'margin-bottom: 2vh' : IsBulkDownloads ? 'margin-bottom: 2vh' : ''" [ngClass]="IsIMDashboard ? 'col-md-12 header-im' : 'col-md-12'">
    <div class="row count-data" *ngIf="tokenData.IsExternalUser==='0' && !IsIMDashboard">
      <div class="col-xl-2 autoWidth col-md-4  col-6">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOutStandingQuotes()">
          <div class="icon">
            <i class="material-icons"> description </i>
          </div>
          <div class="content">
            <div class="text">Quotes</div>
            <div *ngIf="!IsIMUser" class="number count-to">{{DashboardCount.TotalQuotes}}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="col-xl-2 autoWidth col-md-4  col-6">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenCalls()">
          <div class="icon" style="background: lightgreen;">
            <i class="material-icons"> offline_pin </i>
          </div>
          <div class="content">
            <div class="text">Service Calls</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalTMServiceCalls }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="col-xl-2 autoWidth col-md-4  col-6">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenjc()">
          <div class="icon" style=" background: #ff9933;">
            <i class="material-icons"> assessment </i>
          </div>
          <div class="content">
            <div class="text">Quotes in Progress</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalJCServiceCalls }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="col-xl-2 autoWidth col-md-4  col-6">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectMCCService()">
          <div class="icon" style="background: #6B5B95;">
            <i class="material-icons"> data_usage </i>
          </div>
          <div class="content">
            <div class="text">PPM Completion %</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalMCCServices}}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="col-xl-2 autoWidth col-md-4  col-6" *ngIf="isInternalUser">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToSubContractor()">
          <div class="icon" style="    background: #77212E;">
            <i class="material-icons"> supervisor_account </i>
          </div>
          <div class="content">
            <div class="text">Sub Contractor</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{ DashboardCount.TotalSubContractor }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="col-xl-2 autoWidth col-md-4  col-6">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenDebt()">
          <div class="icon" style="background: #577284;">
            <i class="material-icons"> monetization_on </i>
          </div>
          <div class="content">
            <div class="text">Open Debt</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{ TotalOpenDetInCurrency }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row count-data" *ngIf="tokenData.IsExternalUser==='1' && !IsIMDashboard">
      <div class="div">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOutStandingQuotes()">
          <div class="icon">
            <i class="material-icons"> description </i>
          </div>
          <div class="content">
            <div class="text">Quotes</div>
            <div *ngIf="!IsIMUser" class="number count-to">{{DashboardCount.TotalQuotes}}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="div">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenCalls()">
          <div class="icon" style="background: lightgreen;">
            <i class="material-icons"> offline_pin </i>
          </div>
          <div class="content">
            <div class="text">Service Calls</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalTMServiceCalls }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="div">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenjc()">
          <div class="icon" style=" background: #ff9933;">
            <i class="material-icons"> assessment </i>
          </div>
          <div class="content">
            <div class="text">Quotes in Progress</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalJCServiceCalls }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>

      <div class="div">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectMCCService()">
          <div class="icon" style="background: #6B5B95;">
            <i class="material-icons"> data_usage </i>
          </div>
          <div class="content">
            <div class="text">PPM Completion %</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{DashboardCount.TotalMCCServices}}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>

      </div>

      <div class="div">
        <div class="info-box bg-pink hover-expand-effect" (click)="redirectToOpenDebt()">
          <div class="icon" style="background: #577284;">
            <i class="material-icons"> monetization_on </i>
          </div>
          <div class="content">
            <div class="text">Open Debt</div>
            <div *ngIf="!IsIMUser" class="number count-to" data-from="0" data-to="125" data-speed="15"
              data-fresh-interval="20">
              {{ TotalOpenDetInCurrency }}</div>
            <div *ngIf="IsIMUser" class="number count-to">--</div>
          </div>
        </div>
      </div>
    </div>
    <div class="row header-datefilter" [style]="IsIMDashboard ? 'margin-top: 1.2vh; margin-right: -3vw': ''">

      <div [ngClass]="IsIMDashboard ? 'col-md-9' : 'col-md-4'" [style]="IsIMDashboard ? 'display: flex; justify-content: flex-end;' : 'display: flex;'">
        <div *ngIf="!IsIMDashboard" kendoTooltip tooltipClass="ToolTipCss" position="bottom">
          <div class="displayInline" *ngIf="selectedLocationList.length>0">
            <button class="k-button" kendoButton title="{{toolTipMsg}}" (click)="open()">{{selectedLocationList.length>1
              ? 'Location Filter'+" ("+defaultLocationAsString+' + '+(selectedLocationList.length-totalCount)+" more"+"
              )":'Location Filter'+"( "+totalSelectedLocations+" )" }}</button>
          </div>
          <div class="displayInline" *ngIf="selectedLocationList.length===0">
            <button class="k-button" kendoButton (click)="open()">{{defaultDropdown}}</button>
          </div>
        </div>
        <div *ngIf="IsIMDashboard">
          <div class="displayInline" *ngIf="selectedLocationList.length>0">
            <span class="loc-im">IM Home - {{ selectedLocationList.length>1
              ? defaultLocationAsString+' + '+(selectedLocationList.length-totalCount)+" more"
              : totalSelectedLocations }}
            </span>
            <i class="fa fa-caret-down loc-caret-im" (click)="open()"></i>
          </div>
          <div class="displayInline" *ngIf="selectedLocationList.length===0">
            <span class="loc-im">IM Home - All Locations</span>
            <i class="fa fa-caret-down loc-caret-im" (click)="open()"></i>
          </div>
        </div>
        <kendo-dialog *ngIf="opened" class="DasBoardDialog" (close)="close('cancel')" [minWidth]="300">
          <kendo-dialog-titlebar>
            <div style="font-size: 19px; line-height: 1.3em;">
              Choose Location<br>
              <span style="font-size: 13px;">Filter Location By State</span> <span>
                <kendo-dropdownlist class="StateLocation" [value]="StateList[0]" (valueChange)="stateChange($event)"
                  style="width: 137px;" [data]="StateList" [filterable]="true">
                </kendo-dropdownlist>
              </span>
            </div>
          </kendo-dialog-titlebar>
          <kendo-multiselect [data]="LocationData" [autoClose]="false" [textField]="'Name'" [textField]="'CustName'"
            (close)="close()" [valueField]="'LocationID'" [filterable]="true" (filterChange)="filterChange($event)"
            (valueChange)="checkStatus()" [(ngModel)]="selectedLocationList">
            <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
              <div class="container drodown-header-location">
                <div class="row">
                  <div class="col-md-4 col-4">
                    <span class="mutiselectTextPosition" style="position: relative; left: 9%;">Location</span>
                  </div>
                  <div class="col-md-4 col-4">
                    <span class="template" class="mutiselectTextPosition"
                      style="position: relative; right: 4%;">Customer Name</span>
                  </div>
                  <div class="col-md-1 col-4 stateCss">
                    <span class="template state" class="mutiselectTextPosition">State</span>
                  </div>
                  <div class="col-md-3 col-12 selectCss"
                    style="padding-right: 0; padding-left: 0; display: flex;align-items: center;justify-content: center;">
                    <span class="mutiselectTextPosition">
                      <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                        class="btn selectAllButtonCss"><b>Select All</b></button>
                      <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                        class="btn deselectAllButtonCss"><b>Deselect All</b></button>
                    </span>
                    <span style="padding-left: 5px;">
                      <button type="submit" (click)="close('yes')" class="btn deselectAllButtonCss"><b>Done</b></button>
                    </span>
                  </div>
                </div>
              </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
              <div class="container">
                <div class="row">
                  <div class="col-md-4 col-4">
                    <span class="template customerName-MDcSS" style="position: relative; right: 6%;">{{dataItem.Name |
                      titlecase}}</span>
                  </div>
                  <div class="col-md-4 col-4">
                    <span class="template" style="position: relative; right: 6%;">{{dataItem.CustName |
                      titlecase}}</span>
                  </div>
                  <div class="col-md-2 col-2 stateText">
                    <span class="template">{{dataItem.StateCode}}</span>
                  </div>
                  <div class="col-md-2 col-2">
                    <!-- <span class="template"> <input type="checkbox" id="auth-2fa" (change)="updateDetails($event,dataItem)" [checked]='dataItem.checked'></span> -->
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-multiselect>
          <kendo-dialog-actions>
            <div class="container-fluid" style="padding: 0px 12px;">
              <div class="row">
                <div class="col-md-6 col-6">
                  <button kendoButton class="buttonFooter" style="background-color: #00ccff !important"
                    (click)="clearAll('yes')" primary="true">Clear All</button>
                </div>
                <div class="col-md-6 col-6">
                  <button class="buttonFooter" kendoButton style="float: right;background-color: #00ccff !important"
                    (click)="close('yes')" primary="true">Filter</button>
                </div>
              </div>
            </div>
          </kendo-dialog-actions>
        </kendo-dialog>

        <!-- <div class="displayInline" style="margin-left: 15px;" *ngIf="IsIMDashboard">
          <button class="k-button"
            style="background-color: #c1e5f5; text-transform: capitalize; font-size: 16px; line-height: 1.5;"
            (click)="openPrioritization()">Prioritization</button>
        </div>


        <div class="displayInline" style="margin-left: 15px;" *ngIf="IsIMDashboard">
          <button class="k-button"
            style="background-color: #c1e5f5; text-transform: capitalize; font-size: 16px; line-height: 1.5;"
            (click)="openEquipments()">Equipment</button>
        </div> -->

      </div>

      <div *ngIf="!IsMonthlyReport && !IsMonthlyIMReport && !IsBulkDownloads" [ngClass]="{
        'col-md-3': IsIMDashboard,
        'col-md-8': !IsIMDashboard,
        'text-right': true
      }">
        <kendo-daterange #daterange class="datePickerRightPosition">
          <label [style]="IsIMDashboard ? '' : 'margin-right: 10px;'">
            <span *ngIf="!IsIMDashboard" class="label">From</span>
            <kendo-dateinput [format]="IsIMDashboard ? 'dd MMM yy' : 'dd-MM-yyyy'" [class]="IsIMDashboard ? 'start-dateInputCss-im' : 'dateInputCss'" [style]="IsIMDashboard ? '': 'width: 87px;'" name="start"
              kendoDateRangeStartInput [(value)]="range.start" [(ngModel)]="range.start" autoCorrectOn="blur">
            </kendo-dateinput>
          </label>
          <span *ngIf="!IsIMDashboard" class="label">To</span>
          <span *ngIf="IsIMDashboard" class="label" style="font-size: 20px; color: black;">-</span>
          <label>
            <kendo-dateinput [format]="IsIMDashboard ? 'dd MMM yy' : 'dd-MM-yyyy'" [class]="IsIMDashboard ? 'end-dateInputCss-im' : 'dateInputCss'" [style]="IsIMDashboard ? '' : 'width: 87px; margin-right: 9px;'"
              name="end" kendoDateRangeEndInput [(value)]="range.end" [(ngModel)]="range.end" autoCorrectOn="blur">
            </kendo-dateinput>
            <i *ngIf="IsIMDashboard" class="fa fa-caret-down date-caret-im"></i>
            <button *ngIf="!IsIMDashboard" class="btn btn-sm" (click)="editdate(true)">Filter</button>
            &nbsp;
            <button *ngIf="!IsIMDashboard" class="btn btn-sm" (click)="saveDateRange()">Save Filter</button>
          </label>
          <kendo-daterange-popup #popup>
            <ng-template kendoDateRangePopupTemplate let-cancel="cancel">
                <kendo-multiviewcalendar kendoDateRangeSelection>
                </kendo-multiviewcalendar>
                <div *ngIf="IsIMDashboard" class="d-flex justify-content-end px-3 py-2">
                  <button class="btn btn-sm btn-primary mr-1 custom-filter-btn" (click)="editdate(true); popup.toggle()">
                      Filter
                  </button>
                  <button *ngIf="IsIMDashboard" class="btn btn-sm custom-filter-btn" (click)="saveDateRange(); popup.toggle()">
                    Save Filter
                  </button>
                </div>
            </ng-template>
          </kendo-daterange-popup>
        </kendo-daterange>
      </div>
    </div>
  </div>
</section>



<kendo-dialog *ngIf="isPrioritization" (close)="closePriority('cancel')" [width]="310">
  <kendo-dialog-titlebar style="width: 100.5% !important;
    border-width: 0 0 1px !important;">
    <div style="font-size: 19px; line-height: 1.3em;">
      Prioritization
    </div>
  </kendo-dialog-titlebar>
  <div>
    <h2 style="font-size: 16px;
    color: darkblue;
    font-weight: 600;
    margin-top: 10px;">Select your priority</h2>
  </div>

  <div class="priority-container">
    <div class="box" *ngFor="let item of priorityItems; let i = index">
      <img [src]="item.icon" [alt]="item.label + ' Icon'" class="priority-icon">
      <!-- <i [ngClass]="item.icon" style="font-size: 30px;"></i> -->
      <span class="item-label" [innerHTML]="item.label.split(' ').join('<br/>')"
        style="color: darkblue; font-weight: 400;"></span>
      <!-- <span class="priority" [ngClass]="{ 'high': item.priority === 'HIGH', 'medium': item.priority === 'MEDIUM', 'low': item.priority === 'LOW' }">
        <span [ngClass]="{ 'high-dot': item.priority === 'HIGH', 'medium-dot': item.priority === 'MEDIUM', 'low-dot': item.priority === 'LOW' }"></span>{{ item.priority }}
      </span> -->

      <span class="priority"
        [ngClass]="{ 'high': item.priority === 'High', 'medium': item.priority === 'Medium', 'low': item.priority === 'Low' }"
        [attr.draggable]="true" (dragstart)="onDragStart($event, i)" (dragover)="onDragOver($event)"
        (drop)="onDrop($event, i)">
        <span
          [ngClass]="{ 'high-dot': item.priority === 'High', 'medium-dot': item.priority === 'Medium', 'low-dot': item.priority === 'Low' }"></span>
        {{ item.priority }}
      </span>
    </div>
    <button kendoButton (click)="ApplyPriority()">Apply</button>
  </div>
</kendo-dialog>


<kendo-dialog *ngIf="isEquipment" (close)="closeEquipment('cancel')" [width]="312">
  <kendo-dialog-titlebar style="width: 100.5% !important;
    border-width: 0 0 1px !important;">
    <div style="font-size: 19px; line-height: 1.3em;">
      Equipment
    </div>
  </kendo-dialog-titlebar>
  <div class="equipment-container">
    <h2 style="font-size: 16px;
    color: darkblue;
    font-weight: 600;
    margin-left: -10px;">Search Equipment</h2>

    <input type="text" placeholder="Search Equipment" [(ngModel)]="searchTerm" (input)="filterEquipment()">

    <div style="max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    margin-top: 10px;">
      <h2 style="font-size: 16px;
    color: darkblue;
    font-weight: 600;
    margin-left: -10px;">Select Equipment</h2>
      <div *ngIf="!searchTerm" class="select-all">
        <label>
          <hr />
          <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleSelectAll()">
          {{ selectAll ? '(Unselect All)' : '(Select All)' }}
        </label>
      </div>

      <div class="equipment-list">
        <label *ngFor="let item of filteredEquipment">
          <hr /><input type="checkbox" [(ngModel)]="item.selected" (ngModelChange)="updateSelectAll()"> {{ item.name }}
        </label>
      </div>
    </div>

    <div class="equip-buttons">
      <button (click)="confirmSelection()">OK</button>
      <button (click)="cancelSelection()">Cancel</button>
    </div>
  </div>

</kendo-dialog>