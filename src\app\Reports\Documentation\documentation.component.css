@media only screen and (max-width: 1139px) {
  .margin-top-section {
    margin-top: 3% !important;
  }

  .section-padding {
    padding: 0px;
  }
}

.margin-top-section {
  margin-top: 95px;
  position: relative;
  margin-bottom: -42px;
  display: block;
}
.k-breadcrumb-container {
  margin-top: 0px !important;
}
.dashboardFooterCss
{
  position: relative;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.header-bar {
  padding-bottom: 4px;
  border-bottom: 1.5px solid #979b9c6e;
  margin-bottom: 3px;
}
.newUI{
  background-color: #0093d7 !important;
  height: 29px;
}
.newUI .k-menu:not(.k-context-menu)>.k-item{
  color:white !important;
}

.onedrive .newUI .k-icon{
  color:white !important;
}

.dashboard-header .onedrive li {
  margin-bottom: 0px !important;
}


/*
.k-menu-group .k-item:hover{
background-color: #0093d7 !important;
}

.k-menu-group .k-item:hover .k-icon{
  color:white !important;
} */
.uploadUI{
  background-color: #f4f7fa;
  padding-left: 10px;
  height: 31px;
}
.uploadUI .k-icon{
  color: #000 !important;
}
.uploadUI .k-menu:not(.k-context-menu) > .k-item > .k-state-active{
  color: #000 !important;
}

.uploadUI .k-menu-group .k-item {
  color: #000 !important;
}
.uploadUI .k-menu-popup .k-menu-group{
  background: white !important;
    color: black !important;
}
.uploadUI .k-menu:not(.k-context-menu)>.k-item {
  color: rgba(0,0,0,0.87) !important;
}

.downloadUI{
  padding-left: 10px;
  height: 31px;
}
.downloadUI:hover{
  background-color: rgba(0,0,0,0.2)
}
.downloadUI .k-icon{
  color: #000 !important;
}
.downloadUI .k-menu-group .k-item {
  color: #000 !important;
}
.downloadUI .k-menu:not(.k-context-menu) > .k-item:hover {
    color: black !important;
}
.downloadUI .k-menu:not(.k-context-menu)>.k-item {
  color: rgba(0,0,0,0.87) !important;
}
.uploadUI .k-menu-popup .k-menu-group{
    color: black !important;
}
.breadCrumbUI {
  font-size: 16px !important;
  padding-bottom: 5px;
  font-family: calibri light, "Roboto" !important;
}
.breadCrumbUI .k-breadcrumb-container .k-breadcrumb-item {
  cursor: pointer;
}

.onedrive .k-grid td {
  padding: 10px 20px;
  outline: 0;
  font-weight: inherit;
  text-align: inherit;
  overflow: hidden;
  text-overflow: clip;
}
.onedrive .k-grid th {
  padding: 10px 20px;
  border-width: 0 0 1px;
  white-space: nowrap;
  text-overflow: clip;
  /* background: #1c7443; */
}
.onedrive .k-grid tr.k-alt {
  background-color: white;
}
.onedrive .k-grid tr.k-alt:hover {
  background-color: rgba(0,0,0,0.07);
}
.onedrive .k-grid tbody tr:hover{
  cursor: pointer;
}

onedrive .k-grid thead {
  color: #d7d7d7;
  text-align: left;
  border-color: inherit;
}
.onedrive .k-icon{
  color:black !important;
}

@media only screen and (max-width: 1139px) {
  .header-bar {
    height: 80px;
    display: flex;
    flex-direction: column;
 }
}

.header-bar .HeaderTools {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  flex-grow: 1;
  align-items: center;
}
.header-bar .group2 {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  flex-grow: 0;
  align-items: center;
}

.file-manager-grid {
  padding: 32px;
}

#CreateFolderPopup > div.k-dialog.k-widget.k-window.ng-trigger.ng-trigger-dialogSlideInAppear{
  width: 20% !important;
}
.CreateFolderPopUpHeaderClose{
  margin-left: 40px;
  height: 25px;
  width: 25px;
  float: right;
}

#CreateFolderByLocationPopup > .k-dialog-wrapper .k-dialog {
  width: 60% !important;
}


body > admin-app-root > app-documentation > kendo-dialog > div.k-widget.k-window.k-dialog.ng-trigger.ng-trigger-dialogSlideInAppear{
  width: 60% !important;
}

/* body > admin-app-root > kendo-popup{
    left: 300px !important;
    min-width: 600px !important;
    width: 635px !important;
} */
.fa-stack {
  width: 2em;
  height: 1em !important;
  line-height: 1em !important;
  vertical-align: middle;
}

#FolderLocationsTemplate .k-list .k-item {
  padding: 2px 0px !important;
  border-bottom: ridge !important;
  min-height: 2em !important;
  line-height: 2em !important;
  white-space: normal;
}

