 
 <div *ngIf="count===true">
 
  <kendo-chart 
  class="chart-size-css"
  [seriesColors]="['#9F9F9F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"  
  (seriesClick)="onSeries($event)"
   >
   <kendo-chart-tooltip>
    <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category" let-series="series">
      {{ category }} : {{value }}
    </ng-template>
  </kendo-chart-tooltip>
    <kendo-chart-category-axis>
        <kendo-chart-category-axis-item [categories]="['Open Calls','Complete Calls']">
            <kendo-chart-category-axis-item-labels [font]="10"   >
              </kendo-chart-category-axis-item-labels>
        </kendo-chart-category-axis-item>
    </kendo-chart-category-axis>
    <kendo-chart-series>
        <kendo-chart-series-item type="column" [gap]="2" [data]="ApproveData">
            <kendo-chart-series-item-labels [margin]="-6"  >
               
              </kendo-chart-series-item-labels >
        </kendo-chart-series-item>
      <kendo-chart-series-item type="column" [data]="AwaitingApprove">
          <kendo-chart-series-item-labels [margin]="-6"  >
               
            </kendo-chart-series-item-labels >
      </kendo-chart-series-item>
     
      </kendo-chart-series>
  </kendo-chart>
</div>