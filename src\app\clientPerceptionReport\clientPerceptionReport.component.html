<div id="headerView"></div>
<admin-header (addLocationListOnRefreshBrowser)='locationChange($event)'></admin-header>
<section>
  <div class="container-fluid">
    <form ngNativeValidate #customerStatus="ngForm" class="k-form" >
      <div class="row">
        <div class="col-md-12">
          <div class="mb-2 pl-2 text-white d-block title">Client Perception Report</div>
          <span style="font-size: 13px !important;
          color: #000000c9 !important;
          padding: 0px 3px 0px 10px !important; font-weight: 500 !important; background: #d3d3d359 !important;">
            At Airmaster, we understand that the success of our business depends on satisfying the needs and
            expectations of our clients. We hope this is apparent in the way we work with you.<br>
            We would appreciate your feedback on how well we succeed in meeting your needs and expectations, and
            accordingly would like to take a few minutes of your time to complete this Client Perception Report.</span>
        </div>
        <label class="k-form-field col-md-12">
          <span class="col-md-6">
            <span class="defaultSpanPosition">Date </span>
            <kendo-dateinput [format]="'dd/MM/yyyy'" style="
           margin-right: 9px;" name="end" [(ngModel)]="date" readonly="true" autoCorrectOn="blur">
            </kendo-dateinput>
          </span>
        </label>
        <label class="k-form-field col-md-12"  style="margin-top: 10px;">
            <span class="col-md-6">
          <span class="defaultSpanPosition">Client Organisation Name <span class="k-required">*</span></span>
          <input  kendoTextBox placeholder="Enter Organisation Name" [(ngModel)]="clientPerceptionData.OrganisationName" name="OrganisationName"  #OrganisationName="ngModel" (blur)="trimValue($event)"  [readonly]="isReadOnly" required/>   
          <div *ngIf="OrganisationName.errors && OrganisationName.touched">
              <div class="invalid-login" *ngIf="OrganisationName.errors.required" >
                <span class="errorMessage">Client Organisation Name is Required</span>
              </div>
              <div class="invalid-login" *ngIf="OrganisationName.value && OrganisationName.invalid" >
                <span class="errorMessage">Client Organisation Name is invalid</span>
              </div>
            </div>
          </span>
        </label>
        <label class="k-form-field col-md-12" style="margin-top: 10px;">
            <span class="col-md-6">
          <span class="defaultSpanPosition">Contact Email <span class="k-required">*</span></span>
          <input  class="input-css" placeholder="Enter Contact Email" kendoTextBox [(ngModel)]="clientPerceptionData.EmailID" pattern="^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$"  name="EmailID" readonly  #EmailID="ngModel" required/>
          <div *ngIf="EmailID.errors && EmailID.touched">
              <div class="invalid-login" [hidden]="(!EmailID.errors.required)">
                <span class="errorMessage">Contact Email is Required</span>
              </div>
              <div *ngIf="!EmailID.errors.required">
                  <div class="invalid-login" [hidden]="(EmailID.errors.invalid)">
                    <span class="errorMessage">Contact Email is invalid</span>
                  </div>
                </div> 
            </div> 
          </span>
        </label>
        <label class="k-form-field col-md-12" style="margin-top: 10px;">
            <span class="col-md-6">
          <span class="defaultSpanPosition">Client Contact Name <span class="k-required">*</span></span>
          <input kendoTextBox placeholder="Enter Client Contact Name" [(ngModel)]="clientPerceptionData.ContactName" name="ContactName"  readonly #ContactName="ngModel" required />
          <div *ngIf="ContactName.errors && ContactName.touched">
              <div class="invalid-login" [hidden]="(!ContactName.errors.required )">
                <span class="errorMessage">Client Contact Name is Required</span>
              </div>
            </div>
          </span>
        </label>


        <label class="k-form-field col-md-12" style="margin-top: 10px;">
            <span class="col-md-6">
          <span class="defaultSpanPosition">State/Territory <span class="k-required">*</span></span>
          <kendo-dropdownlist class="k-multiselect"
          (close)="close($event)"
          [defaultItem]="defaultItem" class="margin-auto" [data]="StateList"
            [(ngModel)]="selectedState"  [filterable]="true" [textField]="'Code'" [valuePrimitive]="true" [valueField]="'CodeID'"
            (filterChange)="filterLocation($event)" name="State"  (valueChange)="locationSelectionChange($event)"
            #State="ngModel"  class="dropdownListWidth" required>
            <!-- <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
              <div class="container drodown-header-location col-md-12" style="padding-left: -100px !important;">
                <div class="row">
                  <div class="col-md-12">
                    <div>
                    <span class="mutiselectTextPosition" style="margin-left:20px; " >State</span>
                   </div> 
                </div>
                 </div>
              </div>
            </ng-template> -->
            <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
              <div class="container " >
                <div class="row">
                  <div class="col-md-12">
                    <!-- <input type="checkbox" class="k-checkbox"
                      [checked]="isItemSelected(dataItem.text)"> -->
                    <span class="template" style=" text-align:center !important;" class=" ">{{dataItem.Code}}</span>
                  </div>
                </div>
                </div>
              </ng-template>
            </kendo-dropdownlist>
          <div *ngIf="State.errors && State.touched">
            <div class="invalid-login" [hidden]="(!State.errors.required )">
              <span class="errorMessage">State/Territory is Required</span>
            </div>
            </div>
          </span>

        </label>
        <label class="k-form-field col-md-12" style="margin-top: 10px;">
            <span class="col-md-6">
          <span class="defaultSpanPosition">Site(s) Serviced by Airmaster <span class="k-required">*</span></span>
          <input kendoTextBox placeholder="Enter Site(s)" [(ngModel)]="clientPerceptionData.SiteServicedBy"  name="SiteServicedBy"(blur)="trimValue($event)"  #SiteServicedBy="ngModel" required/>
            <div *ngIf="SiteServicedBy.errors && SiteServicedBy.touched">
              <div class="invalid-login" *ngIf="SiteServicedBy.errors.required" >
                <span class="errorMessage">Site(s) Name is Required</span>
              </div>
              <div class="invalid-login" *ngIf="SiteServicedBy.value && SiteServicedBy.invalid" >
                <span class="errorMessage">Site(s) Name is invalid</span>
              </div>
            </div>
          </span>
        </label>
    <div class="k-form-field" style="margin: 6px !important;"></div>
        <label class="k-form-field col-md-12 margin-top-12">
          <h6>
            Please think about Airmaster and other service providers you know, and rate our performance on the following
            criteria</h6>
        </label>

        <div class="k-form-field col-md-12">
          <div class="ClientPerception margin-top-12">
            <kendo-grid [data]="gridData" class="form-check-inline" style="width: 61rem;">
              <kendo-grid-column field="Name" title="" width="90"  >
              </kendo-grid-column>
              <kendo-grid-column  field="" title="Not Applicable" width="50" >
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">
                    <!-- <label class="labelOnTop">1</label> -->
                  <!-- <input type="radio" class="radioAlign" name="Delivery" value="1"
                    (change)="groupNameSelectAllChange($event,rowIndex,colIndex,dataItem)" /> -->
                    <span style="text-align: center;margin: 0 28px;">
                      <input style="margin:auto;" type="radio"  id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,1)" value="1" >
                  </span>

                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="" title="Very Unsatisfied" width="55">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">
                <!-- <label class="labelOnTop">2</label>  -->
                 <!-- <input type="radio" class="radioAlign" name="Delivery"
                value="2"  (change)="groupNameSelectAllChange($event,rowIndex,colIndex,dataItem)" /> -->
                <span style="text-align: center;margin: 0 30px;;">
                    <input style="margin: auto;" type="radio" class="form-check-input" id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,2)" value="2" >
           </span>
              </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="" title="Unsatisfied" width="50">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">

                  <span style="text-align: center;margin: 0 28px;">
                  <input style="margin: auto;" type="radio" class="form-check-input" id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,3)" value="3" >
                </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="" title="Neutral" width="50">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">
                  <!-- <label class="labelOnTop">4</label> -->
                  <!-- <input type="radio" class="radioAlign" name="Delivery"
                  value="4" (change)="groupNameSelectAllChange($event,rowIndex,columnIndex,dataItem)" /> -->
                  <span style="text-align: center;margin: 0 28px;">                 
                     <input style="margin: auto;" type="radio" class="form-check-input" id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,4)" value="4" >
                  </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="" title="Satisfied"width="50">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">
                 <!-- <label class="labelOnTop">5</label>  -->
                 <!-- <input type="radio" class="radioAlign" name="Delivery"
                 value="5" (change)="groupNameSelectAllChange($event,rowIndex,colIndex,dataItem)" /> -->
                 <span style="text-align: center;margin: 0 28px;">
                    <input style="margin: auto;" type="radio" class="form-check-input" id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,5)"  value="5" >
              </span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="" title="Very Satisfied" width="55">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex" let-columnIndex="columnIndex">
                 <!-- <label class="labelOnTop">6</label> -->
                  <!-- <input type="radio" class="radioAlign" name="Delivery"
                 value="6" (change)="groupNameSelectAllChange($event,rowIndex,colIndex,dataItem)" /> -->
                 <span style="text-align: center;margin: 0 35px;">
                   <input style="margin: auto;" type="radio" class="form-check-input" id="{{dataItem.propCode}}{{rowIndex}}" name="{{dataItem.propCode}}{{rowIndex}}" (click)="setRatingValue(dataItem.propCode,6)" value="6" >
                  </span>
                </ng-template>
              </kendo-grid-column>      
            </kendo-grid>
          </div>
        </div>
        <label class="k-form-field col-md-12 margin-top-12" style="margin-top: 20px;">
          <h6>
            How likely are you to recommend Airmaster to someone else? <span class="k-required">*</span></h6>
        </label>
        <div class="k-form-field col-md-12 margin-top-12">
          <div class="k-form-field">
            <input type="radio" name="recommendToOthers"  id="likely" class="k-radio"  (click)="changeRecommendation(true)" value="true" checked>
            <label class="k-radio-label" for="likely" >Likely</label>

            <input type="radio" name="recommendToOthers"  id="unlikely" class="k-radio"  (click)="changeRecommendation(false)" value="false">
            <label class="k-radio-label" for="unlikely">Unlikely</label>
          </div>
        </div>
        <label class="k-form-field col-md-12 margin-top-12" style="margin-top: 20px;">
          <h6>
            Can you suggest any other ways our service could be improved?</h6>
            <textarea kendoTextArea [(ngModel)]="clientPerceptionData.ServiceImproveSuggestion" class="k-textarea" name="ServiceImproveSuggestion" style="width: 46%;" [autoSize]="true"></textarea>
          <!-- <textarea class="k-textarea" name="description" >
                    </textarea> -->
        </label>
        <label class="k-form-field col-md-12 margin-top-12" style="margin-top: 20px;
            ">
          <h6 style="width: 47%;
                  color: #0000009e;">
            Would you like to keep your responses confidential?
            NOTE: All requests to 'Keep feedback Confidential' will be strictly adhered to informing only personnel who
            are directly involved with the client/contract</h6>

        </label>
        <label class="k-form-field col-md-12 margin-top-12">
          <input type="checkbox"  name="keepConfidential" id="keepConfidential" class="k-checkbox" [(ngModel)]="isKeepConfidential">
          <label class="k-checkbox-label" for="keepConfidential">Keep my feedback Confidential</label>
        </label>
        <label class="k-form-field col-md-12" style="margin-top: 31px;">
          <button class="SubMitButtonCss" (click)=" saveClientForm()" type="button" class="k-button k-primary" >Submit</button>&nbsp;
        </label>
      </div>
    </form>
  </div>
</section>
<admin-footer class="footer-css"></admin-footer>