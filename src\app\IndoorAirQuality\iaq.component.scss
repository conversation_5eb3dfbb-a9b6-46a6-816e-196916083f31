.dashboard-header {
    margin-left: 220px;
    height: calc(100vh - 92.5px);
    margin-bottom: 0;

    @media (max-width: 768px) {
        margin-left: 0;
    }
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap,
:host ::ng-deep .k-input {
    height: calc(8px + 1.75em);
    color: black !important;
}

.dateInputCss,
:host ::ng-deep .k-dateinput-wrap {
    border-color: #F5F3EF !important;
    border-width: 0 0 1px !important;
}

.section-top-dashboard {
    margin-top: 4%;
}

.dashboardFooterCss {
    position: absolute;
    left: 0;
    right: 0;
    top: auto;
    bottom: auto;
    z-index: 999;
}

.panel-container {
  margin-bottom: 15px;
  background: white;
}

.panel-btn {
  font-weight: 600;
  font-size: 1.6vw;
  box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 0.25);
  background: white;
  border-radius: 6px 6px 0 0;
  width: 100%;
  justify-content: space-between;
  padding: 10px 15px;
  border: 1px solid #757575;
  transition: border-radius 0.3s ease;

  span {
    display: inline-block;
    text-transform: none !important;
  }

  .k-icon {
    float: right;
    font-size: 16px;
  }
}

.collapse-panel {
  background: white;
  overflow: hidden;
  border-radius: 0 0 6px 6px;
  border: 1px solid #757575;
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.25);
  border-top: 0;
}

.switch {
    position: relative;
    display: inline-block;
    width: 26px;
    height: 12px;

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0; left: 0;
      right: 0; bottom: 0;
      background-color: #d9d9d9;
      transition: background-color 0.3s;
      border-radius: 16px;

      &::before {
        position: absolute;
        content: "";
        height: 13px;
        width: 13px;
        left: 0;
        bottom: 0;
        background-color: whitesmoke;
        transition: 0.4s;
        border-radius: 50%;
        transition: transform 0.3s, background-color 0.3s;
      }
    }

    input:checked + .slider:before {
      transform: translateX(12px);
      background-color: #575757;
    }
}

.graphContainer {
    // height: auto !important;
    border: 1px solid #b3b3b3;
    padding: 1rem;
    border-radius: 0.63rem;
    background: #fff;
    margin-bottom: 1rem;

    span {
        font-size: 1.2vw !important;
        font-family: 'Roboto';
        font-weight: bold;
        color: black !important;
    }
}

.tabs {
    width: 150px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    margin-left: 8px;
}

.tabs p {
    cursor: pointer;
}

.active-tab {
  border-bottom: 2px solid #000;
  color:#000;
}

// Modal
.zone-modal-content {
  border-radius: 6px;
  background-color: #d9d9d9;
  border: 1px solid #000000;
  box-shadow: 4px 4px 4px 0 rgba(0, 0, 0, 0.25);
}

.zone-modal-header {
  background-color: #d9d9d9;
  border: 0;
  border-radius: 6px;

  .modal-title {
    font-size: 1rem;
    color: black;
    font-weight: 700;
    font-family: Roboto;
  }

  .zone-modal-close {
    width: 20px;
    height: 20px;
  }
}

.zone-modal-body {
  background-color: #d9d9d9;
  border-radius: 6px;

  .tasks-section {
    margin-bottom: 5px;

    .section-title {
      font-weight: 700;
      font-size: 1rem;
      margin-bottom: 2px;
      font-family: Roboto;
      color: #575757
    }

    .workflow-list,
    .backlog-list {
      // .list-group-item {
      //   padding: 2px;
      // }
       padding: 6px 8px;
      border: none;
      background-color: transparent;
    }

    .workflow-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .item-left {
        display: flex;
        gap: 4px;

        .job-number { font-weight: bold; }
        .description { color: #000000; }
      }
      .status-badge {
        /* Figma: badge background, text color, border‑radius */
         background-color: #d3d3d3;
        color: #000000;
        font-size: 0.75rem;
        border-radius: 5px;
        padding: 2px 6px;
      }
    }

    .backlog-item {

       .description {
        font-weight: 500;
        color: #000;
      }

      .view-data-btn {
        background-color: #6e5b75;
        color: #fff;
        font-size: 0.75rem;
        padding: 2px 8px;
        border-radius: 8px;
      }

      .place-call-btn {
        /* Figma: button border, padding, font */
         background-color: #198df5;
        color: #fff;
        font-size: 0.75rem;
        padding: 2px 8px;
        border-radius: 8px;
      }
      .prioritise-switch {
        // margin-left: /* */;
        /* Figma: switch sizing/colors if custom */
        margin-left: 8px;
      }
    }
  }
   .info-icon {
    font-size: 1rem;
    color: #000;
    margin-left: 6px;
    cursor: pointer;
  }
}


// .dialog-title {
//     border-radius: 6px 6px 0 0;
//     background: #e9e9e9;
//     top: 0;
//     right: 0;
//     width: 100%;
//     color: #000;
//     padding: 1.1vw 0.7vw;

//     div {
//         font-weight: bold;
//         font-size: 1.2vw;
//         color: #000;
//     }
// }

// .dialog-content {
//   font-family: Roboto, sans-serif;
//   font-size: 1vw;
//   margin-top: 1.2vw;

//   p {
//     margin: 0 0 0.9vw 0;
//     font-weight: 500;
//   }
// }

// .task-grid {
//   display: grid;
//   grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
//   gap: 0.8vw;
//   align-items: center;
//   margin-bottom: 0.9vw;
//   font-size: 0.9vw;
//   justify-items: center;
// }

// .task-grid.header {
//   font-weight: 600;
//   text-align: center;
// }

// .task-grid.row {
//     margin-left: 0;
//     margin-right: 0;
// }

// .task-link {
//   font-weight: bold;
//   color: #156082;
//   text-decoration: underline;
// }

// .task-type {
//   color: #555;
// }

// .badge {
//   padding: 10px 0;
//   border-radius: 3px;
//   font-size: 0.9vw;
//   color: #fff;
//   display: inline-block;
// }

// .badge.severe {
//   color: #e1211e;
//   font-weight: 600;
//   border-radius: 7px;
//   text-align: center;
//   background-color: #fae4d7;
//   width: 100%;
// }

// .badge.minor {
//   color: #26d535;
//   font-weight: 600;
//   border-radius: 7px;
//   text-align: center;
//   background-color: #d8f2d0;
//   width: 100%;
// }

// .badge.place {
//   background-color: #56dbfc;
//   width: 100%;
// }

// .history-btn {
//   font-weight: 600;
//   font-size: 1vw;
//   background: #e9e9e9;
//   border-radius: 6px 6px 0 0;
//   width: 100%;
//   justify-content: space-between;
//   padding: 10px 15px;
//   border: 1px solid #000;
//   transition: border-radius 0.3s ease;

//   span {
//     display: inline-block;
//     text-transform: none !important;
//   }

//   .k-icon {
//     float: right;
//     font-size: 1.2vw;
//   }
// }

// .history-section {

//   .history-item {
//     display: flex;
//     gap: 1vw;
//     margin-bottom: 1vw;
//   }

//   .history-left {
//     min-width: 220px;

//     .job-link {
//       display: block;
//       font-size: 0.9vw;
//       font-weight: bold;
//       color: #444;
//       background: #dedede;
//       padding: 0.5vw;
//       border-radius: 6px;
//       text-align: center;
//     }

//     .issue-link {
//       margin-top: 0.6vw;

//       a {
//         text-decoration: underline;
//         color: #156082;
//         font-size: 0.9vw;
//       }
//     }
//   }

//   .history-right {
//     flex: 1;
//     background: #fff;
//     border: 1px solid #ccc;
//     border-radius: 6px;

//     .technician-header {
//       background: #505050;
//       color: #fff;
//       padding: 0.5vw;
//       font-weight: bold;
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//     }

//     .technician-summary {
//       padding: 0.7vw;
//       font-size: 0.85vw;
//       color: #333;
//       line-height: 1.5;
//     }
//   }
// }

.highcharts-scrolling {
    scrollbar-width: auto !important;
}

// ::ng-deep .k-widget.k-window {
//     border-radius: 6px;
//     border: 1px solid #000;
//     width: 55vw;
// }

// ::ng-deep div.k-content.k-window-content.k-dialog-content {
//     border-radius: 0 0 6px 6px;
// }

@media (max-width: 991px) {
    .dashboard-header div[class*='col-'] {
        margin-bottom: 0px;
    }
}

@media only screen and (min-width: 1801px) and (max-width: 1920px) {
    .dashboard-header {
        margin-left: 330px;
        height: calc(100vh - 145px);
    }
    .section-top-dashboard {
        margin-top: 5.4%;
    }
}

@media only screen and (min-width: 1601px) and (max-width: 1800px) {
    .dashboard-header {
        margin-left: 295px;
        height: calc(100vh - 125px);
    }
    .section-top-dashboard {
        margin-top: 4.9%;
    }
}

@media only screen and (min-width: 1921px) and (max-width: 2115px) {
    .dashboard-header {
        margin-left: 220px;
    }
}

@media only screen and (min-width: 2115px) {
    .dashboard-header {
        margin-left: 220px;
    }
}

@media (min-width: 1368px) and (max-width: 1454px) {
    .dashboard-header {
        margin-left: 245px;
        height: calc(100vh - 102px);
    }

    .section-top-dashboard {
        margin-top: 4.3%;
    }
}

@media (min-width: 1454px) and (max-width: 1603px) {
    .dashboard-header {
        margin-left: 275px;
        height: calc(100vh - 111.5px);
    }

    .section-top-dashboard {
        margin-top: 4.4%;
    }
}

@media only screen and (max-width:1130px) {
    // .section-top-dashboard {
    //     margin-top: 5% !important;
    // }

    .section-padding {
        padding: 0rem 0;
    }
}

.section_3 {
    display: flex;
    // margin-bottom: 13px;
    margin-right: 0;
}
.card-div {
    max-width: 14.28%;
}
.screenScroll {
    border-radius: 10px;
    padding: 20px;
}
.legend {
    list-style-type: none;
}

.legend-item {
    margin: 0px 0px 0px 50px;
    cursor: pointer;
}

.legend-item .legend-marker {
    display: inline-block;
    width: 12px;
    height: 12px;
    vertical-align: middle;
    user-select: none;
    background-color: #5a5a5a;
    border-radius: 50%;
}


// Grid..........
:host ::ng-deep .k-grid th {
    background-color: #838383 !important;
    font-size: 16px !important;
    // border-right: 1px solid lightgray !important;
}

:host ::ng-deep .k-grid th {
    padding: 16px 24px;
    border-width: 0 0 1px;
    white-space: normal;
    background: #343A40;
    vertical-align: middle;
    color: black;
}

:host ::ng-deep .k-grid-toolbar {
    border: none !important;
}

:host ::ng-deep .k-grid-header {
    border-bottom-width: 0 !important;
}

:host ::ng-deep .k-grid-content {
    border: 1px solid rgba(0, 0, 0, 0.12) !important;
    border-top-width: 0 !important;
}

:host ::ng-deep .k-grid-container {
    width: 99.7% !important;
}

:host ::ng-deep .awaiting-grid .k-grid-container {
    width: 99.6% !important;
}
:host ::ng-deep .k-column-resizer{
  color: black !important;
}



// ********

:host ::ng-deep .k-grid-header .k-filterable>.k-link {
    color: black !important;
    font-weight: bold !important;
}

:host ::ng-deep .k-grid td {
    padding: 10px 12px !important;
    font-size: 14px !important;
}

:host ::ng-deep .k-i-filter {
    color: black !important;
}

:host ::ng-deep .k-grid-header-wrap {
    // border-right: 1px solid lightgray !important;
    // border-left: 1px solid lightgray !important;
    border: 1px solid rgba(0, 0, 0, 0.12) !important;
    border-radius: 8px 8px 0 0 !important;
    // border-bottom-width: 0 !important;
}

:host ::ng-deep kendo-grid {
    border: none !important;
}

// :host ::ng-deep .k-grid th:first-child{
//     border-left: 1px solid lightgray !important;
// }

:host ::ng-deep .k-grid tr.k-alt {
    background-color: #fff !important;
}

:host ::ng-deep .k-grid table tr.k-alt:hover {
    background-color: rgba(0, 0, 0, 0.07) !important;
}

:host ::ng-deep .k-grid tr.k-alt.k-state-selected {
    color: #000 !important;
    background-color: #7E9DEB !important;
}

.taskCompleted, .taskBacklogged {
    font-family: Calibri Light !important;
    letter-spacing: 0.3px;
    cursor: pointer;
    color: black;
    font-weight: 600;
    border-radius: 7px;
    text-align: center;
    background-color: #88888859;
    padding: 5px 10px;
    font-size: 14px;
}

.taskCompleted:hover, .taskBacklogged:hover {
    background-color: #7979794d!important;
}
