export class ContactListEntities {
    ContactID: number;
    Customer: string;
    CustomerID: number;
    Email: string;
    FirstName: string;
    LastName: string;
    Name: string;
}
export class CustomersEntities {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}
export class LocationByCustomerEntities {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
}
export class GenerateReportEntities {
    //Customer: string;
    Customer: Array<any>=[];
    Location: Array<any>=[];
    Contact:Array<any>=[];
    StartDate: Date;
    EndDate: Date;
    AccountManager: string;
    SectionSelector: Array<any>=[];
}