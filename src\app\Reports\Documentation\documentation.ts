export interface DriveRootItemsModel {
    Id: string;
    Name: string;
    CustName: string;
    Type: string;
    MimeType: string;
    Level:string;
    ParentReference: ParentReferenceModel;
    LOCATNNM: string;
}
export interface ParentReferenceModel {
    DriveId: string;
    ParentItemId: string;
    ParentItemPath: string;
}
export interface FileResponseModel {
    Content: string;
    FileName: string;
    MimeType: string;
}

export interface FileRequestModel{
    File: File;
    FileName: string;
    ParentID: string;
}
export class CreateFolderRequestModel {
        FolderName: string;
        ParentID: string;
}
export class UserAssignedLocations {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class LocationFolderData{
    CustomerID: number;
    CustomerCode: string;
    CustomerName: string;

    LocationID: number;
    LocationCode: string;
    LocationName: string;

    Status: number;
}