//----Dependency------//
import { BrowserModule, Title } from "@angular/platform-browser";
import { NgModule } from "@angular/core";
import { NgxWebstorageModule } from "ngx-webstorage";
import {
  DatePipe,
  HashLocationStrategy,
  LocationStrategy,
  PathLocationStrategy,
} from "@angular/common";
import { HttpClientModule, HTTP_INTERCEPTORS } from "@angular/common/http";
import { MomentModule } from "angular2-moment";
import { TextareaAutosizeModule } from "ngx-textarea-autosize";
import { DialogsModule } from "@progress/kendo-angular-dialog";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { TooltipModule } from "@progress/kendo-angular-tooltip";
import { DropDownsModule } from "@progress/kendo-angular-dropdowns";
import {
  ButtonGroupModule,
  DropDownButtonModule,
  ButtonModule,
} from "@progress/kendo-angular-buttons";
import { PopupModule } from "@progress/kendo-angular-popup";
import { ProgressBarModule } from "angular-progress-bar";
import { TreeViewModule } from "@progress/kendo-angular-treeview";
import { ContextMenuModule } from "@progress/kendo-angular-menu";
import { AuthHttp } from "angular2-jwt";
import { SwitchModule, InputsModule } from "@progress/kendo-angular-inputs";
import { GridModule, ExcelModule } from "@progress/kendo-angular-grid";
import { HttpModule } from "@angular/http";
import { RouterModule } from "@angular/router";
import { ToastrModule } from "ngx-toastr";
import { routing } from "./app.routing";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { NgHttpLoaderModule } from "ng-http-loader";
import {
  DateInputModule,
  MultiViewCalendarModule,
  CalendarModule,
  DateInputsModule,
  DateRangeModule,
} from "@progress/kendo-angular-dateinputs";
import { RecaptchaModule , RecaptchaFormsModule,
  RECAPTCHA_V3_SITE_KEY,
  RecaptchaV3Module} from "ng-recaptcha"; //recaptcha module
import { GridContextMenuComponent } from "./Common/grid-context-menu.component";
import { FontAwesomeModule } from "@fortawesome/angular-fontawesome";
//----Components------//

import { AppComponent } from "./app.component";
import { UserInformationComponent } from "../app/Users/<USER>/userInformation.component";
import { OpenCallsComponent } from "./CallSummaryDetails/open-calls/open-calls.component";
import { OpenDebtComponent } from "./CallSummaryDetails/open-debt/open-debt.component";
import { SubContractorComponent } from "./CallSummaryDetails/sub-contractor/sub-contractor.component";
import { QuoteDetailComponent } from "./QuoteSummaryDetails/quote-details/quote-detail.component";
import { LoginComponent } from "./Common/login/login.component";
import { HeaderComponent } from "./Common/header/header.component";
import { FooterComponent } from "./Common/footer/footer.component";
import { PlaceCallComponent } from "./ServiceJobDetails/place-call/place-call.component";
import { DashboardComponent } from "./Dashboard/dashboard.component";
import { OutstandingQuotes } from "./QuoteSummaryDetails/outstanding-quotes/outstanding-quotes.component";
import { AttendComponent } from "./CallSummaryDetails/attend/attend.component";
import { OpenJCComponent } from "./CallSummaryDetails/open-jc/open-jc.component";
import { DocumentationComponent } from "./Reports/Documentation/documentation.component";

//----Charts -----//
import { OpenCallsChartComponent } from "./CallsChart/open-calls-chart/open-calls-chart.component";
import { OpenJcChartComponent } from "./CallsChart/open-jc-chart/open-jc-chart.component";
import { PPMPercentageChartComponent } from "./CallsChart/ppm-percentage-chart/ppm-percentage-chart.component";
import { OpenDebtChartComponent } from "./CallsChart/open-debt-chart/open-debt-chart.component";
import { LayoutModule } from "@progress/kendo-angular-layout";
import { ProgressHttpModule } from "angular-progress-http";
//----Services-----//
import { CoreDataService } from "./Services/core-data.service";
import {
  AuthGuardService,
  LoginAuthGuardService,
  MFAGuardService,
} from "./Services/auth-guard.service";
import { AuthenticationService } from "./Services/authentication.service";
import { AuthHttpInterceptor } from "./Services/http-interceptor.service";
import { SharedDataService } from "./Services/shared-data.service";
import { MOMComponent } from "./MoM/mom/mom.component";
import { MonthlyReportComponent } from "./Reports/monthly-report/monthly-report.component";
import { MomListComponent } from "./MoM/mom-list/mom-list.component";
import { GenerateBulkReportComponent } from "./Reports/generate-bulk-report/generate-bulk-report.component";
import { ComplianceReportComponent } from "./Reports/compliance-report/compliance-report.component";
import { ClientPerceptionReportComponent } from "./clientPerceptionReport/clientPerceptionReport.component";
//-------Directives------//
import { TooltipDirective } from "./Directives/tooltip.directive";
import { NavigationModule } from "@progress/kendo-angular-navigation";
import { UploadsModule } from "@progress/kendo-angular-upload";
import { VerificationComponent } from "./Common/verification/verification.component";
import { FTDashboardComponent } from './FT-Dashboard/ft-dashboard.component';
import { GaugeChartModule } from "angular-gauge-chart";
import { GaugesModule } from "@progress/kendo-angular-gauges";
import { SparklineModule, ChartsModule } from "@progress/kendo-angular-charts";
import { ZingchartAngularModule } from 'zingchart-angular';
import { NgApexchartsModule } from 'ng-apexcharts';
import { environment } from "src/environments/environment";
import { CustomDropDownListFilterComponent } from "./Common/dropdownList-filter.component";
import { ScrollTopComponent } from "./Scroll-top/scroll-top.component";
import { MultiSelectFilterComponent } from "./Common/multiselect-filter.component";
import { MonthlyIMReportComponent } from "./Reports/monthly-im-report/monthly-im-report.component";
import { SideNavComponent } from "./Common/side-nav/side-nav.component";
import { IAQComponent } from "./IndoorAirQuality/iaq.component";
import { IconsModule } from '@progress/kendo-angular-icons';

@NgModule({
  declarations: [
    UserInformationComponent,
    OpenDebtComponent,
    OpenDebtChartComponent,
    SubContractorComponent,
    OpenCallsComponent,
    QuoteDetailComponent,
    OutstandingQuotes,
    AppComponent,
    LoginComponent,
    DashboardComponent,
    HeaderComponent,
    FooterComponent,
    OpenJCComponent,
    AttendComponent,
    OpenCallsChartComponent,
    OpenJcChartComponent,
    PPMPercentageChartComponent,
    PlaceCallComponent,
    MOMComponent,
    MonthlyReportComponent,
    MonthlyIMReportComponent,
    MomListComponent,
    GenerateBulkReportComponent,
    ComplianceReportComponent,
    ClientPerceptionReportComponent,
    TooltipDirective,
    DocumentationComponent,
    GridContextMenuComponent,
    VerificationComponent,
    FTDashboardComponent,
    CustomDropDownListFilterComponent,
    ScrollTopComponent,
    MultiSelectFilterComponent,
    SideNavComponent,
    IAQComponent
  ],
  imports: [
    TextareaAutosizeModule,
    MultiViewCalendarModule,
    LayoutModule,
    ExcelModule,
    RecaptchaModule,
    NgxWebstorageModule.forRoot(),
    SparklineModule,
    TooltipModule,
    NavigationModule,
    DateInputsModule,
    DateInputModule,
    CalendarModule,
    InputsModule,
    ChartsModule,
    ProgressHttpModule,
    ButtonModule,
    ProgressBarModule,
    TreeViewModule,
    ContextMenuModule,
    DropDownButtonModule,
    PopupModule,
    ToastrModule.forRoot(),
    SwitchModule,
    DropDownsModule,
    ButtonGroupModule,
    DateRangeModule,
    DialogsModule,
    RouterModule,
    BrowserModule,
    GridModule,
    HttpClientModule,
    NgHttpLoaderModule.forRoot(),
    BrowserAnimationsModule,
    HttpModule,
    routing,
    MomentModule,
    FormsModule,
    ReactiveFormsModule,
    UploadsModule,
    FontAwesomeModule,
    GaugesModule,
    InputsModule,
    ZingchartAngularModule,
    NgApexchartsModule,
    RecaptchaFormsModule,
    RecaptchaV3Module,
    RecaptchaModule,
    IconsModule,
  ],
  providers: [
    {
      provide: LocationStrategy,
      useClass: HashLocationStrategy,
    },
    AuthGuardService,
    LoginAuthGuardService,
    MFAGuardService,
    SharedDataService,
    CoreDataService,
    AuthHttp,
    AuthenticationService,
    AuthHttpInterceptor,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthHttpInterceptor,
      multi: true,
    },
    {
      provide: RECAPTCHA_V3_SITE_KEY,
      useValue: environment.recaptcha.siteKey,
    },
    Title,
    DatePipe
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
