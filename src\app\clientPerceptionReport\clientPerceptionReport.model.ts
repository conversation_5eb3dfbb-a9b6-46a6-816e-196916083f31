export class ClientPerceptionModel {
    OrganisationName: string;
    ContactName: string;
    State: any;
    SiteServicedBy: string;
    EmailID: string;
    DeliveryAgreedService: number;
    GettingRightFirstTime: number;
    RectifyingProblems: number;
    Responsiveness: number;
    InnovativeProposals: number;
    MutuBenRelation: number;
    AccurateInvoicing: number;
    CompetentStaff: number;
    ProfessionalImage: number;
    AMCommunication: number;
    TechCommunication: number;
    OfficeCommunication: number;
    RecommendOthers: boolean;
    ServiceImproveSuggestion: string;
    KeepConfidential: boolean;
}

export class CodeData {
    public CodeID: number;
    public CodeTypeID: number;
    public CodeName: string;
    public Code: string;
    public Description: string;
}

export class PlaceCallModel {
    LocationID: number;
    EquipmentID: number;
    PurchaseOrder: string;
    Description: String;
}
export class equipmentDropdownlistModel {
    Code: string;
    EquipmentID: number;
    EquipmentType: string;
    InstallDate: string;
    Make: string; //manufecturer
    Model: string;
}
export class CustomerLocationByName {
    // CustName: string;
    // Name: string;
    // LocationID: number;
    // CustomerID: number;
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number
    Name: string;
    StateCode: string;
}