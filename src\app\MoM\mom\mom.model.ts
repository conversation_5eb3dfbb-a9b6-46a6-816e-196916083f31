export class MOMEntity {
    meeting: string;
    Date: Date;
    Location: number;
    Present: string;
    Prepared: string;
    HeldAt: string;
    Apologies: string;
    NextMeeting: string;

}
export class CustomerLocationByName {
    CustName: string;
    Name: string;
    LocationID: number;
    CustomerID: number;
}
export class LocationDropdownListModel {
    CustName: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class CodeData {
    public CodeID: number;
    public CodeTypeID: number;
    public CodeName: string;
    public Code: string;
    public Description: string;
}
export class EditMoMListEntity {
    Apologies: string;
    Attendees: string;
    CreatedBy: number;
    CreationDate: Date;
    HeldAt: string;
    MeetingDate: Date;
    MeetingID: number;
    MeetingLocations: string;
    MeetingTitle: string;
    MoMTasks: [];
    NextMeeting: string;
    OwnerID: number;
    State: string;
}
export class CustomersEntities {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}