<div id="headerView"></div>
<admin-header (addLocationListOnRefreshBrowser)='locationChangeOnRefresh($event)'></admin-header>


<section class="margin-top-table-list">
  <div class="container-fluid">

    <div class="row">
      <div class="col-md-12 mt-4">
        <div class="mb-2 pl-2 text-white d-block titulo">MINUTES OF MEETING</div>
        <form #momRef="ngForm">
          <div class="row mb-14">

            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Meeting<span class="k-required">*</span></p>
              </label>
              <input kendoTextBox class="kendoBox" maxlength="200" placeholder="Meeting Info" name="meeting"
                [(ngModel)]="selectedMoMFields.meeting" #meeting="ngModel" required />
              <div *ngIf="meeting.errors && meeting.touched">
                <div class="invalid-login" [hidden]="(!meeting.errors.required )">
                  <span class="errorMessage">This field
                    is required !</span>
                </div>
              </div>

            </div>
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Prepared By </p>
              </label>
              <input kendoTextBox class="kendoBox" readonly placeholder="Meeting Prepared By" name="prepatedBy"
                #prepared="ngModel" [(ngModel)]="selectedMoMFields.Prepared" required />
              <div *ngIf="prepared.errors && prepared.touched">
                <div class="invalid-login" [hidden]="(!prepared.errors.required )">
                  <span class="errorMessage">This field
                    is required !</span>
                </div>
              </div>

            </div>

          </div>
          <div class="row mb-14">
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Present<span class="k-required">*</span></p>
              </label>
              <input kendoTextBox class="kendoBox" maxlength="99" name="present" placeholder="Attendees List"
                #present="ngModel" [(ngModel)]="selectedMoMFields.Present" required />
              <div *ngIf="present.errors && present.touched">
                <div class="invalid-login" [hidden]="(!present.errors.required )">
                  <span class="errorMessage">This field
                    is required !</span>
                </div>
              </div>
            </div>
            <div class="col-md-6 col-12 col-6">

              <label class="k-form-field" style="display: block;    margin-bottom: -2px;">
                <p>Date</p>
                <kendo-datepicker class="dropdownDateWidth" name="Date" [(ngModel)]="selectedMoMFields.Date"
                  format="dd/MM/yyyy" required></kendo-datepicker>
              </label>
            </div>
          </div>
          <div class="row mb-14">
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Held At<span class="k-required">*</span></p>
              </label>
              <input kendoTextBox class="kendoBox" maxlength="99" placeholder=" " name="HeldAt" #HeldAt="ngModel"
                [(ngModel)]="selectedMoMFields.HeldAt" required />
              <div *ngIf="HeldAt.errors && HeldAt.touched">
                <div class="invalid-login" [hidden]="(!prepared.errors.required )">
                  <span class="errorMessage">This field
                    is required !</span>
                </div>
                <!-- asfdaffffdfsdfsdfdsfds
                new test -->
              </div>
            </div>
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Apologies<span class="k-required">*</span></p>
              </label>
              <input kendoTextBox class="kendoBox" maxlength="99" placeholder=" " name="apologies" #apologies="ngModel"
                [(ngModel)]="selectedMoMFields.Apologies" />

            </div>
          </div>
          <div class="row mb-14">
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field">
                <p>Next Meeting </p>
              </label>
              <input kendoTextBox class="kendoBox" maxlength="99" placeholder=" " name="NextMeeting"
                #NextMeeting="ngModel" [(ngModel)]="selectedMoMFields.NextMeeting" />

            </div>
            <div class="col-md-6 col-12 col-6">

              <label class="k-form-field" style="display: block;margin-bottom: -2px;width: 20%;">
                <p>Location<span class="k-required">*</span></p>



                <div class="displayInline" *ngIf="valueDefault.length>0">
                  <button class="k-button" style="background-color: #00ccff;" kendoButton
                    (click)="open(dataItem)">{{valueDefault.length>2 ? defaultLocationAsString+' + '+(valueDefault.length-totalCount)+" more":totalSelectedLocations}}
                  </button>
                </div>
<!-- sdsasda -->
                <div class="displayInline" *ngIf="valueDefault.length===0">
                  <button class="k-button" kendoButton style="background-color: #00ccff;" (click)="open()">{{defaultDropdown}}</button>
                </div>

                <!-- <div class="container-fluid ul-container">
                  <div class="row ul-row">
                    <ul style="display: contents;">
                      <li *ngFor="let item of valueDefault;index as i;">
                        <span class="span-content">
                          <span>{{item.Name}}</span>
                          <span aria-label="delete" class="k-select" aria-hidden="true">
                            <span class="k-icon k-i-close" (click)="removeTag(i)">
                              <i class="fa fa-cross-circle-right fa-lg" (click)="removeTag(i)"></i>
                            </span>
                          </span>
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
                <kendo-multiselect style="margin-bottom: 4%;" [data]="data" [textField]="'Name'"   
                  [textField]="'CustName'" [valueField]="'LocationID'" [filterable]="true" name="location"
                  [(ngModel)]="valueDefault" #location="ngModel" (filterChange)="filterChange($event)"
                  [placeholder]="'Select Location'"
                  (valueChange)="locationchange($event)"
                  required>
                  <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
                    <div class="container drodown-header-location">
                      <div class="row">
                        <div class="col-md-6 col-12 col-6">
                          <span class="mutiselectTextPosition" style="  position: relative;
                                    left: 9%;">Location</span>
                        </div>
                        <div class="col-md-6 col-12 col-6 ">
                          <span class="template" class="mutiselectTextPosition" style="  position: relative;
                                right: 3%;">Customer Name</span>
                        </div>

                      </div>
                    </div>
                  </ng-template>
                  <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
                    <div class="container">
                      <div class="row">
                        <div class="col-md-6 col-12 col-6 ">
                          <span class="template">{{dataItem.Name | titlecase}}</span>
                        </div>
                        <div class="col-md-6 col-12 col-6">
                          <span class=" ">{{dataItem.CustName | titlecase}}</span>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </kendo-multiselect> -->

                <!-- <div *ngIf="location.errors && location.touched">
                  <div class="invalid-login" [hidden]="(!location.errors.required )">
                    <span class="locationErrorMessage">Location is required !
                    </span>
                  </div>
                </div> -->

              </label>
            </div>
          </div>
          <div class="row mb-14">
            <div class="col-md-6 col-12 col-6">
              <label class="k-form-field" style="display: block;
             margin-bottom: 16px;">
                <p>State<span class="k-required">*</span></p>

                <kendo-dropdownlist class="dropdownListWidth" [data]="StateList" [value]="StatedefaultValue"
                  [textField]="'Code'" [valueField]="'CodeID'"  (valueChange)="stateChangeValue($event)" 
                  name="state" required>
                  <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
                    <div class="container drodown-header-location">
                      <div class="row">
                        <div class="col-md-6  col-6">
                          <span class="mutiselectTextPosition" style="  position: relative;
                                  left: 10%;">State</span>
                        </div>
                        <div class="col-md-6  col-6 ">

                        </div>
                      </div>
                    </div>
                  </ng-template>
                  <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
                    <div class="container  ">
                      <div class="row">
                        <div class="col-md-6 col-6 ">
                          <span class="template md-size location-position">{{dataItem.Code }}</span>
                        </div>
                        <div class="col-md-6 col-6">
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </kendo-dropdownlist>

              </label>
            </div>


            <div class="col-md-6 col-12 col-6">

            </div>
          </div>

        </form>


      </div>
    </div>

    <div class="row">
      <div class="container-fluid" style="position: relative;right: 1px;">
        <div class="col-md-12">
          <kendo-grid [data]="gridData" (edit)="editHandler($event)" (cancel)="cancelHandler($event)"
            [sortable]="{allowUnsort: true, mode:'multiple'}" [selectable]="true" [skip]="state.skip"
            [sort]="state.sort" [selectable]="true" (dataStateChange)="dataStateChange($event)" filterable="menu"
            [filter]="state.filter" (save)="saveHandler($event)" (add)="addHandler($event)" [height]="410">
            <ng-template kendoGridToolbarTemplate>
              <button style="background: #00ccff;
                  color: white;" kendoGridAddCommand>Add new</button>
            </ng-template>


            <kendo-grid-column field="" title="#" width="60">
              <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{ dataItem.ITEM}}
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="DESCRIPTION" title="Description" width="225">

              <ng-template kendoGridEditTemplate let-column="column" let-formGroup="formGroup" let-isNew="isNew">

                <textarea  autosize name="text" class="textarea-height" #input kendoTextArea
                  [formControl]="formGroup.get(column.field)"></textarea>

                <kendo-popup [anchor]="input"
                  *ngIf="formGroup.get(column.field).invalid && !(isNew && formGroup.get(column.field).untouched)"
                  popupClass="k-widget k-tooltip k-tooltip-validation k-invalid-msg">
                  <span class="k-icon k-i-warning"></span>
                  Description is required
                </kendo-popup>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="ACTION" title="Action" width="225">
              <ng-template kendoGridEditTemplate let-column="column" let-formGroup="formGroup" let-isNew="isNew">

                <textarea autosize class="textarea-height" #input kendoTextArea
                  [formControl]="formGroup.get(column.field)"></textarea>
                <kendo-popup [anchor]="input"
                  *ngIf="formGroup.get(column.field).invalid && !(isNew && formGroup.get(column.field).untouched)"
                  popupClass="k-widget k-tooltip k-tooltip-validation k-invalid-msg">
                  <span class="k-icon k-i-warning"></span>
                  Action is required
                </kendo-popup>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column field="DATE" editor="date" title="Due Date" width="160" filter="date" operator="eq"
              format="{0:dd-MM-yyyy}">
              <!-- <ng-template kendoGridEditTemplate  let-filterService="filterService" let-filter let-column="column" let-formGroup="formGroup" let-isNew="isNew" style="width: 200px;">

              </ng-template> -->
              <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService" 
                  operator="eq">
                </kendo-grid-date-filter-menu>
              </ng-template>
              <!-- a -->
            </kendo-grid-column>



            <kendo-grid-column field="NOTES" title="Notes" width="225">
              <ng-template kendoGridEditTemplate let-column="column" let-formGroup="formGroup" let-isNew="isNew">

                <textarea autosize class="textarea-height" #input kendoTextArea
                  [formControl]="formGroup.get(column.field)"></textarea>

              </ng-template>
            </kendo-grid-column>




            <kendo-grid-column field="statusId" title="Status" width="150" [filterable]="false">
              <ng-template kendoGridCellTemplate let-dataItem>
                {{category(dataItem.statusId)?.Name}}
              </ng-template>
              <ng-template kendoGridEditTemplate let-dataItem="dataItem" let-column="column" let-formGroup="formGroup">
                <kendo-dropdownlist popupAnchor [defaultItem]="{statusId: null, Name: 'Select status'}"
                  [data]="MoMStatus" textField="Name" valueField="statusId" [valuePrimitive]="true"
                  [formControl]="formGroup.get('statusId')" style="padding: 2px 4px;">
            </kendo-dropdownlist>

              </ng-template>
            </kendo-grid-column>
            <kendo-grid-command-column title="" width="280">
              <ng-template kendoGridCellTemplate let-isNew="isNew" let-dataItem>
             <button class="DefaultWidthCss" kendoGridEditCommand [primary]="true">Edit</button>
                <button kendoGridSaveCommand class="DefaultWidthCss"  style="    background: #00ccff;
                 color: white;" [disabled]="formGroup?.invalid">{{ isNew ? 'Add' : 'Update' }}</button>
                <button class="DefaultWidthCss" [ngClass]="{'discardWidth' : isNew}"   kendoGridCancelCommand>{{ isNew ? 'Discard changes' : 'Cancel' }}</button>
              </ng-template>
            </kendo-grid-command-column>
          </kendo-grid>
          <button type="button" style="background: #00ccff;" (click)="saveMeeting()" [disabled]="valueDefault.length===0"
            class="k-button bottom-button  k-primary">Submit</button>
        </div>
      </div>
  </div>
  </div>
</section>
<kendo-dialog class="multiselextDropdownHeader" title="Choose Locations" *ngIf="opened" (close)="close('cancel')"
  [minWidth]="300">
  <!-- <div class="container-fluid ul-container">
    <div class="row ul-row">
      <ul style="display: contents;">
        <li *ngFor="let item of valueDefault;index as i;">
          <span class="span-content">
            <span>{{item.Name}}</span>
            <span aria-label="delete" class="k-select" aria-hidden="true">
              <span class="k-icon k-i-close" (click)="removeTag(i)">
                <i class="fa fa-cross-circle-right fa-lg" (click)="removeTag(i)"></i>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div> -->
  <kendo-multiselect [autoClose]="false" style="margin-bottom: 4%;" [data]="data" [textField]="'Name'" [textField]="'CustName'"
    [valueField]="'LocationID'" [filterable]="true" name="location" [(ngModel)]="valueDefault" #location="ngModel"
    (filterChange)="filterChange($event)" [placeholder]="'Select Location'" (valueChange)="locationchange($event)"
    required>
    <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
      <div class="container drodown-header-location">
        <div class="row">
          <div class="col-md-6 col-6">
            <span class="mutiselectTextPosition" style="position: relative; left: 9%;">Location</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="template" class="mutiselectTextPosition" style="position: relative; right: 3%;">Customer Name</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="mutiselectTextPosition">
              <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                class="btn selectAllButtonCss"><b>Select All</b></button>
              <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                class="btn deselectAllButtonCss"><b>Deselect All</b></button>
            </span>
          </div>

        </div>
      </div>
    </ng-template>
    <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-6">
            <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
          </div>
          <div class="col-md-6 col-6">
            <span class="md-size">{{dataItem.CustName | titlecase}}</span>
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-multiselect>

  <kendo-dialog-actions>
    <div class="container-fluid" style="    padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton style="width: 29% !important;    width: 29% !important;
          background: #00ccff !important;
          color: white;" class="ButtonFooterDialog" [primary]="true" (click)="clearAll()" primary="true">Clear
            All</button>
        </div>
        <div class="col-md-6 col-6 text-right">
          <button kendoButton class="ButtonFooterDialog" [primary]="true" style="float: right;width: 29% !important;
          background: #00ccff !important;
          color: white;" (click)="close('yes')" primary="true">Add</button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
<admin-footer class="momFooterCss"></admin-footer>