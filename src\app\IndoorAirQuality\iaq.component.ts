import { state, style, transition, trigger, animate } from "@angular/animations";
import { Component } from "@angular/core";
import * as Highcharts from 'highcharts/highstock';
import HighchartsMore from 'highcharts/highcharts-more';
import { locationDateFilterEntity } from '../Dashboard/dashboard.model';
import { SharedDataService } from "../Services/shared-data.service";
import { CoreDataService } from "../Services/core-data.service";
import { ToastrService } from "ngx-toastr";
import { StatusCodes } from "../Common/shared";
import { DatePipe } from "@angular/common";
import Dumbbell from 'highcharts/modules/dumbbell';
import { Console } from "console";
import { AxisLabelVisualArgs } from "@progress/kendo-angular-charts";
import { DataStateChangeEvent, GridDataResult } from "@progress/kendo-angular-grid";
import { Router } from "@angular/router";
import { State, process } from "@progress/kendo-data-query";
import { Modal } from 'bootstrap';

HighchartsMore(Highcharts);
Dumbbell(Highcharts);


@Component({
  selector: 'app-iaq',
  templateUrl: './iaq.component.html',
  styleUrls: ['./iaq.component.scss'],
  animations: [
    trigger('expandCollapse', [
      state('expanded', style({
        height: '*',
        opacity: 1,
        overflow: 'hidden',
        padding: '15px'
      })),
      state('collapsed', style({
        height: '0px',
        opacity: 0,
        overflow: 'hidden',
        padding: '0 15px'
      })),
      transition('expanded <=> collapsed', [
        animate('300ms ease-in-out')
      ])
    ])
  ],
})
export class IAQComponent {

  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  locations = [];
  fromDateFilter: any;
  range: any;
  originalDate = new Date();
  startDate: string;
  endDate: string;

  //Temperature Graphs
  private temperatureDataForBWGraph: any = null;

  //Humidity Graphs
  private humidityDataForBWGraph: any = null;
  private humidityDataForLineGraph: any = null;

  //CO2 Graphs
  private co2DataForBWGraph: any = null;
  private co2DataForLineGraph: any = null;

  temperatureDataAvailable: boolean = false;
  humidityDataAvailable: boolean = false;
  co2DataAvailable: boolean = false;



  // Temperature barLine Graphs From Kendo
    getSiteComfortData = [];
    IsOneMonthSiteComfort: boolean = true;
    IsThreeMonthSiteComfort: boolean = false;
    IsSixMonthSiteComfort: boolean = false;
    IsTwelveMonthComfort: boolean = false;

    animateChart: boolean = true;
    private suffix = "%";

    IsThreeDayMovingAverage: boolean = true;
    IsOneWeekMovingAverage: boolean = false;
    IsOneMonthMovingAverage: boolean = false;
    IsThreeMonthMovingAverage: boolean = false;

    OneMonthSiteData = [];
    ThreeMonthSiteData = [];
    SixMonthSiteData = [];
    TwelveMonthSiteData = [];

    getOneMonthMovingAverage = [];
    getThreeMonthMovingAverage = [];
    getSixMonthMovingAverage = [];
    getTwelveMonthMovingAverage = [];

    ThreeMonthSiteDates: string[] = [];
    SixMonthSiteDates: string[] = [];
    TwelveMonthSiteDates: string[] = [];

    getTwelveMonthChangePercentage: number;
    movingAverageCount: number;
  // ***************************Grid Start from here
  EquipmentThermalComfortdata = [];
  public equipComfortGrid: GridDataResult;
  SelectedRowData: any;
    public EquipComfortState: State = {
      skip: 0,
      //take: 10,
      filter: {
        logic: 'and',
        filters: []
      }
    };
    public IMDataDrivenTCompleteListState: State = {
      skip: 0,
      //take: 10,
      filter: {
        logic: 'and',
        filters: []
      }
    };
     public IMDataDrivenTBacklogListState: State = {
        skip: 0,
        //take: 10,
        sort: [{ field: 'DateLastHit', dir: 'desc' }],
        filter: {
          logic: 'and',
          filters: []
        }
      };
  public gridDataForIMBacklogList: GridDataResult;
  public gridDataForIMCompletedList: GridDataResult;
  getIMDataDrivenTaskCompleteList: any[];
  getIMDataDrivenTaskBacklogList: any[];
  lastTCompletedFilteredRecord: any = null;
  lastTInBacklogFilteredRecord: any = null;



  collapsedSections: { [key: string]: boolean } = {};

  historySectionCollapsed: boolean = true;
  historyData = [
    {
      jobNumber: '2411-12558',
      issue: 'ZT too cold during occupied hours',
      summary: 'PALI01 - Dec 6 2024 11:15AM Have requested access in the past for this tenancy with no luck. Recommend reporting all "no access" retail tenancy IM jobs during the next Airmaster/Dexus monthly meeting.'
    }
  ];

  panels: any[] = [
    {
      title: 'Temperature',
      key: 'Temperature',
      dataKey: 'temperatureDataAvailable'
    },
    {
      title: 'Humidity',
      key: 'Humidity',
      dataKey: 'humidityDataAvailable',
      graphs: [
        { 'graphTitle': 'Daily Average Zone Humidity', graphType: 'Line' },
        { 'graphTitle': 'Zone Humidity Distribution', graphType: 'BoxWhisker' }
      ]
    },
    {
      title: 'CO2',
      key: 'CO2',
      dataKey: 'co2DataAvailable',
      graphs: [
        { 'graphTitle': 'Zone CO2 Distribution', graphType: 'BoxWhisker' },
        { 'graphTitle': 'Daily Average Zone CO2', graphType: 'Line' }
      ]
    }
  ];

  selectedPeriods: any = {
    'Temperature': {
      'BoxWhisker': '1M'
    },
    'Humidity': {
      'BoxWhisker': '1M',
      'Line': '1M'
    },
    'CO2': {
      'BoxWhisker': '1M',
      'Line': '1M'
    }
  };

  showDialog: boolean = false;
  zoneTitle: string = '';
  switchState: boolean = false;

  // openDialog() {
  //   this.showDialog = true;
  // }

  // closeDialog() {
  //   this.showDialog = false;
  //   this.historySectionCollapsed = true;
  // }


  toggleCollapse(key: string) {
    const current = this.collapsedSections[key];
    const newValue = !current;

    this.collapsedSections[key] = newValue;

    this.shareData.updateToggleForIAQ(key, newValue);
  }

  // toggleHistoryCollapse(): void {
  //   this.historySectionCollapsed = !this.historySectionCollapsed;
  // }

  constructor(private shareData: SharedDataService, private coreDataService: CoreDataService, private toastrService: ToastrService,private router: Router) {
    this.fromDateFilter = this.shareData.getStartDateByUser();
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (this.locationDateFilterData === null) {
      if (this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == "") {
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else {
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations.split(",").map(el => parseInt(el));
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
  }

  async ngOnInit(): Promise<void> {
    this.shareData.togglesForIAQ$.subscribe(values => {
      this.collapsedSections = { ...values };
    });

    (window as any).angularComponentRef = {
      openDialog: (category: string) => {
        this.onEquipmentClick(category);
      }
    };

    // await this.loadAllAPIs();
  }

  async  ngAfterViewInit() {
    await this.loadAllAPIs();
  }

  async loadAllAPIs(): Promise<void> {
    try{
      const startDate = new Date(this.range["start"]);
      const endDate = new Date(this.range["end"]);
      const locations = this.locations;

      const [
        siteComfortData,
        temperatureDataForBWGraph,
        equipComfortData,
        humidityDataForLineGraph,
        humidityDataForBWGraph,
        co2DataForLineGraph,
        co2DataForBWGraph
      ] = await Promise.all([
        this.coreDataService.getSiteComfortData(locations).toPromise(),
        this.coreDataService.GetIAQBoxWhiskerGraphData(locations, 'AvgTemp').toPromise(),
        this.coreDataService.GetEquipmentComfortData(locations, startDate, endDate).toPromise(),
        this.coreDataService.GetIAQLineGraphData(locations, 'AvgHumidity').toPromise(),
        this.coreDataService.GetIAQBoxWhiskerGraphData(locations, 'AvgHumidity').toPromise(),
        this.coreDataService.GetIAQLineGraphData(locations, 'AvgCo2').toPromise(),
        this.coreDataService.GetIAQBoxWhiskerGraphData(locations, 'AvgCo2').toPromise()
      ]);

      this.getAllSiteComfortData(siteComfortData);
      this.getIAQBoxWhiskerGraphData(temperatureDataForBWGraph, 'AvgTemp');
      this.getIndividualZonePerformanceGraphData(equipComfortData);
      this.getIAQLineGraphData(humidityDataForLineGraph, 'AvgHumidity');
      this.getIAQBoxWhiskerGraphData(humidityDataForBWGraph, 'AvgHumidity');
      this.getIAQLineGraphData(co2DataForLineGraph, 'AvgCo2');
      this.getIAQBoxWhiskerGraphData(co2DataForBWGraph, 'AvgCo2');
    }
    catch (error) {
      this.shareData.ErrorHandler(error);
    }
  }

  async locationChange(event) {
    if (event != undefined) {
      this.locationDateFilterData = JSON.parse(event);
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations.split(",").map(el => parseInt(el));
      }
      else {
        this.locations = [];
      }
      this.range.start = this.locationDateFilterData.start;
      this.range.end = this.locationDateFilterData.end;

      await this.loadAllAPIs();
    }
  }

  private isGraphDataAvailable(graphData: any): boolean {
    return Object.values(graphData).some((arr: any[]) => arr.length > 0);
  }

  public onEquipmentClick(equipment: string) {
    this.zoneTitle = equipment;
    const modalElm = document.getElementById('boxWhiskerModal')!;
    new Modal(modalElm).show();
  }

  getIAQBoxWhiskerGraphData(response: any, MetricColumn: string) {
    if (response.StatusCode == StatusCodes.OK && response != null) {
      let panelName: string = null;
      if (this.isGraphDataAvailable(response.response)) {
        if (MetricColumn === 'AvgTemp') {
          this.temperatureDataForBWGraph = response.response;
          panelName = 'Temperature';
          this.temperatureDataAvailable = true;
          this.shareData.temperatureAvailable$.next(this.temperatureDataAvailable);
        } else if (MetricColumn === 'AvgHumidity') {
          this.humidityDataForBWGraph = response.response;
          panelName = 'Humidity';
          this.humidityDataAvailable = true;
          this.shareData.humidityAvailable$.next(this.humidityDataAvailable);
        } else if (MetricColumn === 'AvgCo2') {
          this.co2DataForBWGraph = response.response;
          panelName = 'CO2';
          this.co2DataAvailable = true;
          this.shareData.co2Available$.next(this.co2DataAvailable);
        }
          this.updateBoxWhiskerGraphData(panelName, this.selectedPeriods[panelName]['BoxWhisker']);
      } else {
          if (MetricColumn === 'AvgTemperature') {
            this.temperatureDataAvailable = false;
            this.shareData.temperatureAvailable$.next(this.temperatureDataAvailable);
          } else if (MetricColumn === 'AvgHumidity') {
            this.humidityDataAvailable = false;
            this.shareData.humidityAvailable$.next(this.humidityDataAvailable);
          } else if (MetricColumn === 'AvgCo2') {
            this.co2DataAvailable = false;
            this.shareData.co2Available$.next(this.co2DataAvailable);
          }
        }
      } else {
        this.toastrService.error("Internal Server Error, Please Contact Support")
      }
    }

  getIAQLineGraphData(response: any, MetricColumn: string) {
    if (response.StatusCode == StatusCodes.OK && response != null) {
      let panelName: string = null;
      if (this.isGraphDataAvailable(response.response)) {
        if (MetricColumn === 'AvgHumidity') {
          this.humidityDataForLineGraph = response.response;
          panelName = 'Humidity';
          this.humidityDataAvailable = true;
          this.shareData.humidityAvailable$.next(this.humidityDataAvailable);
        } else if (MetricColumn === 'AvgCo2') {
          this.co2DataForLineGraph = response.response;
          panelName = 'CO2';
          this.co2DataAvailable = true;
          this.shareData.co2Available$.next(this.co2DataAvailable);
        }
        this.updateLineGraphData(panelName, this.selectedPeriods[panelName]['Line']);
      } else {
        if (MetricColumn === 'AvgHumidity') {
          this.humidityDataAvailable = false;
          this.shareData.humidityAvailable$.next(this.humidityDataAvailable);
        } else if (MetricColumn === 'AvgCO2') {
          this.co2DataAvailable = false;
          this.shareData.co2Available$.next(this.co2DataAvailable);
        }
      }
    } else {
      this.toastrService.error("Internal Server Error, Please Contact Support")
    }
  }

  getIndividualZonePerformanceGraphData(res: any) {
    this.EquipmentThermalComfortdata = res.response

    let sortedData = res.response.sort((a, b) => {

      const totalA = (a.TooHotHours ?? 0) + (a.TooColdHours ?? 0);
      const totalB = (b.TooHotHours ?? 0) + (b.TooColdHours ?? 0);

      return totalB - totalA;
    });
    this.equipComfortGrid = process(sortedData, this.EquipComfortState);
    const data = sortedData.map(item => ({
      name: item.Equipment,
      OutSideRange: item.PerOfOccupancy,
      low: item.TooColdHours ?? 0,
      high: item.TooHotHours ?? 0
    }));
    this.createDumbbellGraph(data);
  }

  updatePeriod(panelName: string, graphType: string, period: string) {
    this.selectedPeriods[panelName][graphType] = period;
    if (graphType === 'BoxWhisker')
      this.updateBoxWhiskerGraphData(panelName, period);
    else if (graphType === 'Line')
      this.updateLineGraphData(panelName, period);
  }

  private updateBoxWhiskerGraphData(panelName: string, period: string): void {
    var data: any = null;
    if (panelName === 'Temperature') {
      data = this.temperatureDataForBWGraph[period];
    }
    else if (panelName === 'Humidity') {
      data = this.humidityDataForBWGraph[period];
    } else if (panelName === 'CO2') {
      data = this.co2DataForBWGraph[period];
    }
    if (data != null) {
      const categories: string[] = data.map(item => item.EquipName);
      const seriesData: number[] = data.map(item => [
        item.MinValue,
        item.LowerQuartile,
        item.Median,
        item.UpperQuartile,
        item.MaxValue
      ]);

      const meanData: { x: number, y: number }[] = data.map((item, index) => ({
        x: index,
        y: item.MeanValue
      }));

      this.createBoxPlotGraph(panelName, categories, seriesData, meanData);
    }
  }

  lastSelectedPoint: Highcharts.Point | null = null; //For Coloring Box BG(Blue)
  private createBoxPlotGraph(panelName: string, categoriesData: string[], seriesData: number[], meanData: { x: number, y: number }[]) {
    const yAxisText: string = panelName === 'Temperature' ? 'Temperature °C' : panelName === 'Humidity' ? 'Humidity %RH' : 'CO2 parts per million';
    const units: string = panelName === 'Temperature' ? '°C' : panelName === 'Humidity' ? '%RH' : 'ppm';

    console.log(categoriesData, 'Box Plot Data');
    Highcharts.chart(panelName + 'BoxWhiskerContainer', {
    chart: {
        type: 'boxplot',
        height: 350,

        scrollablePlotArea: {
          minWidth: categoriesData.length * 150,
          scrollPositionX: 0,
          opacity: 1
        },
       plotBackgroundColor: '#ffffff',
        backgroundColor: '#ffffff',
        marginRight: 0
    },
    title: {
        text: '',
    },
    credits: {
        enabled: false
    },
    legend: {
        enabled: false
    },
    xAxis: {
        categories: categoriesData,
        gridLineColor: '#eee', // subtle grid lines
        labels: {
            enabled: true,
        },
    },
    yAxis: {
        title: {
            text: yAxisText,
            style: {
              color: '#000000',
              fontFamily: 'Calibri Light !important',
            }
        },
        min: 0,
        // max: 100,
        // tickInterval: 10,
        gridLineColor: '#E0E0E0',
         gridLineWidth: 1,
        labels: {
          rotation: -30,
            style: {
                color: '#333'
            }
        },
    },
    tooltip: {
      useHTML: true,
      backgroundColor: 'transparent',
      borderWidth: 0,
      shadow: false,
      formatter: function () {
        const point = this.point;
        const category = this.key;
        return `
          <div style="
            padding: 10px;
            background: #2b2e34; /* dark background */
            color: #ffffff; /* white text */
            border-radius: 10px;
            font-family: Roboto, sans-serif;
            font-size: 14px;
            text-align: center;
            min-width: 120px;
            max-width: 220px;
            line-height: 1.2;
          ">
            Max: ${point.high} ${units}<br>
            Q3: ${point.q3} ${units}<br>
            Median: ${point.median} ${units}<br>
            Q1: ${point.q1} ${units}<br>
            Min: ${point.low} ${units}
          </div>`;
      }
    },
    plotOptions: {
        boxplot: {
            fillColor: '#cccccc',
            lineColor: '#000000',
            medianColor: '#000000',
            medianWidth: 2,
            whiskerColor: '#000000',
            whiskerLength: '50%',
            stemColor: '#000000',
            states: {
              hover: {
                enabled: true,
                brightness: 0,          // keep same brightness
                fillColor: '#4BA9F7'        // blue on hover
              }
            },
            point: {
              events: {
                 mouseOver: function () {
                    // Reset the last selected point when mouse leaves the series
                      this.update({ fillColor: '#4BA9F7' });

                },
                mouseOut: function () {
                    // Reset the last selected point when mouse leaves the series
                    this.update({ fillColor: '#cccccc' });

                },
                  click: function () {
                    if ((window as any).lastSelectedPoint && (window as any).lastSelectedPoint !== this) {
                    (window as any).lastSelectedPoint.update({ fillColor: '#4BA9F7' });
                  }

                  // Set this one as selected
                  this.update({ fillColor: '#4BA9F7' });
                  (window as any).lastSelectedPoint = this;
                  // this.update({ fillColor: '#4BA9F7' }); // fillColor for boxplot
                  (window as any).angularComponentRef.openDialog(this.category);
                  // this.onEquipmentClick(this.category);
                }
              }
            }
        },
    },
    series: [{
        name: panelName,
        data: seriesData,
      },
    //   {
    //     type: 'scatter',
    //     name: 'Median X',
    //     color: '#000000',
    //     enableMouseTracking: false,
    //     marker: {
    //         symbol: 'url(data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="10" height="10"%3E%3Cline x1="1" y1="9" x2="9" y2="1" stroke="black" stroke-width="2"/%3E%3Cline x1="1" y1="1" x2="9" y2="9" stroke="black" stroke-width="2"/%3E%3C/svg%3E)',
    //         width: 10,
    //         height: 10
    //     },
    //     data: meanData
    // }
  ]
    } as any);
  }

  private updateLineGraphData(panelName: string, period: string) {
    var periodData: any = null;
    var highThreshold: number;
    var lowThreshold: number;
    if (panelName === 'Humidity') {
      periodData = this.humidityDataForLineGraph[period];
      highThreshold = 60; // Set high threshold for humidity
      lowThreshold = 30; // Set low threshold for humidity
    } else if (panelName === 'CO2') {
      periodData = this.co2DataForLineGraph[period];
      highThreshold = 800; // Set high threshold for CO2
      lowThreshold = 0; // Set low threshold for CO2
    }

    // console.log(periodData, 'periodData');

    if (periodData != null) {
      if (panelName === 'Humidity') {
         var data = periodData.map((item: any) => ({
          x: new Date(item.Date).getTime(),
          min: item.MinValue,
          max: item.MaxValue,
          y: item.AvgHumidity
        }));
      } else if (panelName === 'CO2') {
        var data = periodData.map((item: any) => ({
          x: new Date(item.Date).getTime(),
          min: item.MinValue,
          max: item.MaxValue,
          y: item.AvgCo2
        }));
      }else if (panelName === 'Temperature') {}

      this.createLineGraph(panelName, data, highThreshold, lowThreshold);
    }
  }

  private createLineGraph(panelName: string, data: any[], highThreshold: number, lowThreshold: number) {

    const yAxisText: string = panelName === 'Temperature' ? 'Temperature °C' : panelName === 'Humidity' ? 'Humidity %RH' : 'CO2 parts per million';
    const units: string = panelName === 'Temperature' ? '°C' : panelName === 'Humidity' ? '%RH' : 'ppm';
  // console.log(data, 'Line Graph Data');
   const blueData: any[] = [];
   const blackData: any[] = [];

  // Loop through input data to split into segments
    for (let i = 0; i < data.length - 1; i++) {
      const curr = data[i];
      const next = data[i + 1];

      const segments = [];

      // Helper to compute the crossing point at given threshold
      const getCrossingPoint = (threshold: number) => {
        const ratio = (threshold - curr.y) / (next.y - curr.y);
        const xAtThreshold = curr.x + (next.x - curr.x) * ratio;
        return { x: xAtThreshold, y: threshold, isCustom: false, marker: { enabled: true, enableMouseTracking: false }};
      };

    let points: any[] = [curr];

    // Check for crossing 30
    if ((curr.y < lowThreshold && next.y >= lowThreshold) || (curr.y >= lowThreshold && next.y < lowThreshold)) {
      const p = getCrossingPoint(lowThreshold);
      p.isCustom = true; // Hide the dot for crossing point
      p.marker = { enabled: false , enableMouseTracking: false}; // Disable marker for crossing point
      points.push(p);
    }

    // Check for crossing 60
    if ((curr.y < highThreshold && next.y >= highThreshold) || (curr.y >= highThreshold && next.y < highThreshold)) {
      const p = getCrossingPoint(highThreshold);
      p.isCustom = true;
      p.marker = { enabled: false , enableMouseTracking: false};
      points.push(p);
    }

    points.push(next);

    // Now assign color for each small segment
    for (let j = 0; j < points.length - 1; j++) {
      const p1 = points[j];
      const p2 = points[j + 1];
      const avgY = (p1.y + p2.y) / 2;

      if (avgY > highThreshold || avgY < lowThreshold) {
        blackData.push([p1, p2]);
      } else {
        blueData.push([p1, p2]);
      }
    }
      }


    Highcharts.chart(panelName + 'LineContainer', {
        chart: {
            type: 'line',
            height: 350
        },
        credits: {
            enabled: false
        },
        title: {
            text: '',
        },

        xAxis: {
            type: 'datetime',
            tickInterval: 30 * 24 * 3600 * 1000,
            labels: {
                format: '{value:%b}',
                rotation: -45,
              style: {
              fontFamily: 'Calibri Light !important',
              }
            },
            crosshair: {
            color: 'gray',
            dashStyle: 'ShortDot',
            width: 1
          }
        },
        yAxis: {
            title: {
                text: yAxisText
            },
            gridLineWidth: 1 ,
            tickInterval: 10,
            min: 0,
            labels: {
              style: {
                fontFamily: 'Calibri Light !important',
              }
            },
            plotBands: [
            {
              from: 0,
              to:  lowThreshold,
              color: '#F5F5F5',
              label: {
                text: 'Too Dry',
                align: 'right',
                x: -10,
                style: {
                  color: '#ccc',
                  fontWeight: 'bold',
                }
              }
            },
            {
              from:highThreshold,
              to: 100,
              color: '#F5F5F5',
              label: {
                text: 'Too Humid',
                align: 'right',
                x: -10,
                style: {
                  color: '#ccc',
                  fontWeight: 'bold',
                }
              }
            }
          ],
        },
        tooltip: {
        useHTML: true,
        backgroundColor: 'transparent',
        borderWidth: 0,
        shadow: false,
        formatter: function () {
          const point = this.point;
          if (point.isCustom){
            return false;
          }

          const category = new Date(this.key);

          // const day = category.getDate().toString().padStart(2, '0');
          // const month = (category.getMonth() + 1).toString().padStart(2, '0');
          // const year = category.getFullYear();
          // const formattedDate = `${day}-${month}-${year}`;

          // Format date as (Month-Name and Date)
          const formattedDate = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: 'numeric'
          }).format(category);

          return `
            <div style="padding:0.4vw; background: #fff; border: 1px solid #ccc; border-radius: 6px; font-family: Roboto, sans-serif;
                        font-size: 0.9rem; min-width: 120px; max-width: 220px; min-height: 100px; max-height:220px; word-break: break-word;">
            <strong style = "margin: 0.5vw;">${formattedDate}</strong><br>
              <div style="display: flex; flex-direction: column; gap: 0.3vw; margin: 0.5vw;">
                <span style="color: deepskyblue;">Daily avg: ${point.y.toFixed(2)} ${units}</span>
                <span style="color: #383737ff;">Daily min: ${point.min} ${units}</span>
                <span style="color: #383737ff;">Daily max: ${point.max} ${units}</span>
              </div>
            </div>`;
          }
        },
        legend: {
            // enabled: false,
            layout: 'horizontal',
            align: 'center',
            itemStyle: {
              color: '#F5F5F5',
              font: '13px Calibri Light !important'
            },
        },
        series: [
        ...blueData.map(segment => ({
        data: segment,
        color: 'rgb(80,176,240)',
        marker: { enabled: false,
          symbol: 'circle', // optional: set default to circle
          states: {
            hover: {
              enabled: true,
              radius: 5,
              symbol: 'circle' // 👈 force circle on hover
            }
          }
         },
         point: {
        events: {
          mouseOver: function () {
            // @ts-ignore
            if (this.isCustom) {
              this.series.chart.tooltip.hide();
              this.setState(''); // remove hover state
            }
          }
        }
      },
        showInLegend: false,
        enableMouseTracking: true,
        trackByArea: true,
        hover: {
          halo: {
            size: 0
          }
        }
      })),
      ...blackData.map(segment => ({
        data: segment,
        color: '#000000',
         marker: { enabled: false,
          symbol: 'circle', // optional: set default to circle
          states: {
            hover: {
              enabled: true,
              radius: 5,
              symbol: 'circle' // 👈 force circle on hover
            }
          }
         },
         point: {
          events: {
            mouseOver: function () {
              // @ts-ignore
              if (this.isCustom) {
                this.series.chart.tooltip.hide();
                this.setState(''); // remove hover state
              }
            }
          }
        },
        showInLegend: false,
        enableMouseTracking: true,
        trackByArea: true,
        hover: {
          halo: {
            size: 0
          }
        }
      }))
        ]
    } as any);

  }



  createDumbbellGraph(data: any[]) {
    // console.log(data, 'Individual Zone Performance Data');
    // Convert data Too cold and Too hot hours to percentages
    const chartData = data.map(item => {
      const tooColdHrs = item.low;
      const tooHotHrs = item.high;
      const percentOutsideRange = item.OutSideRange;

      const hrsOutsideRange = tooColdHrs + tooHotHrs;

      const percentTooHot = hrsOutsideRange > 0
        ? (tooHotHrs / hrsOutsideRange) * percentOutsideRange
        : 0;

      const percentTooCold = hrsOutsideRange > 0
        ? (tooColdHrs / hrsOutsideRange) * percentOutsideRange
        : 0;

      return {
        name: item.name,
        low: tooColdHrs,
        high: tooHotHrs,
        percentTooHot: +percentTooHot.toFixed(2),
        percentTooCold: +percentTooCold.toFixed(2)
      };
    });

  Highcharts.chart("TemperatureDumbbellContainer", {
  chart: {
    type: 'dumbbell',
    height: 350,
    inverted: true,
     scrollablePlotArea: {
        minHeight: data.length * 29, // You can increase if needed
        scrollPositionY: 0,
        opacity: 1
      }
  },
  credits:
  {
    enabled: false
  },
 title:
  {
    text: '',
  },
  xAxis: {
    categories: data.map(item => item.name),
    min: 0,
    title: {
      text: 'Zone Name'
    }
  },
  yAxis: {
     min: -100,
    max: 100,
    title: null,
    reversed: false,
   labels: {
      formatter: function () {
        if (this.value === 0) return '0%';
        return `${Math.abs(this.value)}%`;
      }
    },
    // tickInterval: 50,
    plotLines: [{
      value: 0,
      color: '#ccc',
      width: 1,
      dashStyle: 'Dash',
      zIndex: 3
    }
  ],
  },
  tooltip: {
    useHTML: true,
    backgroundColor: 'black',
    style: {
      color: 'white',
      fontFamily: 'calibri light',
      fontSize: '0.8rem',
    },
    borderWidth: 1,
    shadow: false,
    shared: false,
    formatter: function () {
      const point = this.point;
      console.log(point, 'point');
      const category = this.key; //Did Not use For Yet
      const lowHrs = (point.options as any).lowHrs;
      const highHrs = (point.options as any).highHrs ;
      return `<div >
      Too Cold: <b>${Math.abs(lowHrs)} hrs</b> | Too Hot: <b>${highHrs} hrs</b>
      </div>`;
    },
  },
  plotOptions: {
    dumbbell: {
      connectorWidth: 1,
      connectorColor: 'rgba(91, 86, 86, 0.2)',
      lowColor: 'deepskyblue',
      color: '#5B4A66',
      marker: {
        radius: 8
      },
    }
  },
 series: [
    {
      name: 'Too Cold',
      type: 'dumbbell',
      // color: 'deepskyblue',
      data: chartData.map(item => ({
        name: item.name,
        low:-item.percentTooCold, // negative for chart positioning
        high: item.percentTooHot, // positive for chart positioning
        lowHrs: item.low,
        highHrs: item.high,
        enableMouseTracking: true,
        trackByArea: true               // store actual hours
      })),
    },
    {
      name: 'Too Hot',
      type: 'dumbbell',
    }
  ]
});


}

// Tempratute BarLine Graphs From Kendo
  changeMonthTabSiteComfort(tab) {
    this.IsThreeDayMovingAverage = true;
    this.IsOneWeekMovingAverage = false
    this.IsOneMonthMovingAverage = false
    this.IsThreeMonthMovingAverage = false;
    if (tab == '1M') {
      this.IsOneMonthSiteComfort = true;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = false;
      this.getOneMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '3M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = true
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = false;
      this.getThreeMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '6M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = true
      this.IsTwelveMonthComfort = false;
      this.getSixMonthSiteComfortData();
      this.animateChart = true;
    }
    if (tab == '13M') {
      this.IsOneMonthSiteComfort = false;
      this.IsThreeMonthSiteComfort = false
      this.IsSixMonthSiteComfort = false
      this.IsTwelveMonthComfort = true;
      this.getTwelveMonthSiteComfortData();
      this.animateChart = true;
    }
  }



  getOneMonthSiteComfortData() {
    // let arr = [];
    // let data = this.getSiteComfortData;
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);
    // })
    // let unique: any = new Set(arr);
    // let monthNumber = Array.from(unique);
    // let oneMonth = data.filter((month) => {
    //   let generateDate = new Date(month.AllDates);
    //   return (generateDate.getMonth() + 1) == monthNumber[monthNumber.length - 1]
    // })
    // this.OneMonthSiteData = oneMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let oneMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    oneMonthsAgo.setMonth(currentDate.getMonth() - 1);

    let lastOneMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= oneMonthsAgo && itemDate <= currentDate;
    });
    this.OneMonthSiteData = lastOneMonthsData;

    this.getOneMonthMovingAverage = [];

    let getLastOneMonthData = lastOneMonthsData;
    if (getLastOneMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getOneMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }
      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getOneMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getOneMonthMovingAverage = arr;
    } else {
      this.getOneMonthMovingAverage = [];
    }
  }
  getThreeMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);
    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let lastMonth = monthNumber[monthNumber.length - 1];
    // let threeMonth = [];
    // for (let i = lastMonth - 3; i <= lastMonth; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == i
    //   })
    //   newArr.forEach(element => {
    //     threeMonth.push(element);
    //   });
    // }
    // this.ThreeMonthSiteData = threeMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let threeMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

    let lastThreeMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= threeMonthsAgo && itemDate <= currentDate;
    });
    this.ThreeMonthSiteData = lastThreeMonthsData;
    this.ThreeMonthSiteDates = this.ThreeMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getThreeMonthMovingAverage = [];

    let getLastThreeMonthData = lastThreeMonthsData;
    if (getLastThreeMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getThreeMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getThreeMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getThreeMonthMovingAverageData(data, 30);
        oneMonthData = oneMonthData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData, 30, 31);
        arr = value.slice(30);
        // for(let i = 0; i< 30; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getThreeMonthMovingAverage = arr;
    } else {
      this.getThreeMonthMovingAverage = [];
    }
  }
  // getting Six month for Site Comfort Data
  getSixMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);

    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let lastMonth = monthNumber[monthNumber.length - 1];
    // let sixMonth = [];
    // for (let i = lastMonth - 6; i <= lastMonth; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == i;
    //   })
    //   newArr.forEach(element => {
    //     sixMonth.push(element);
    //   });
    // }
    // this.SixMonthSiteData = sixMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let SixMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    SixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    let lastSixMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= SixMonthsAgo && itemDate <= currentDate;
    });
    this.SixMonthSiteData = lastSixMonthsData;
    this.SixMonthSiteDates = this.SixMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getSixMonthMovingAverage = [];

    let getLastSixMonthData = lastSixMonthsData;
    if (getLastSixMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getSixMonthMovingAverageData(data, 2);
        threeDayData = threeDayData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData, 2, 3);
        arr = value.slice(2);
        // for(let i = 0; i< 2; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getSixMonthMovingAverageData(data, 6);
        oneWeekData = oneWeekData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData, 6, 7);
        arr = value.slice(6);
        // for(let i = 0; i< 6; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getSixMonthMovingAverageData(data, 30);
        oneMonthData = oneMonthData.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData, 30, 31);
        arr = value.slice(30);
        // for(let i = 0; i< 30; i++){
        //   arr[i].TotalPctComfort = 0;
        // }
      }

      this.getSixMonthMovingAverage = arr;
    } else {
      this.getSixMonthMovingAverage = [];
    }
  }
    // getting twelve month Data for Site Comfort
  getTwelveMonthSiteComfortData() {
    // let data = this.getSiteComfortData;
    // let arr = [];
    // data.map((elem) => {
    //   let d = new Date(elem.AllDates);
    //   arr.push(d.getMonth() + 1);

    // })
    // let unique: any = new Set(arr);
    // let monthNumber: any = Array.from(unique);
    // let twelveMonth = [];
    // for (let i = 0; i < monthNumber.length; i++) {
    //   let newArr = data.filter((month) => {
    //     let generateDate = new Date(month.AllDates);
    //     return (generateDate.getMonth() + 1) == monthNumber[i]
    //   })
    //   newArr.forEach(element => {
    //     twelveMonth.push(element);
    //   });
    // }
    // this.TwelveMonthSiteData = twelveMonth;

    let data = this.getSiteComfortData;
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let TwelveMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    TwelveMonthsAgo.setMonth(currentDate.getMonth() - 13);

    let lastTwelveMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= TwelveMonthsAgo && itemDate <= currentDate;
    });
    this.TwelveMonthSiteData = lastTwelveMonthsData;
    this.TwelveMonthSiteDates = this.TwelveMonthSiteData.map(item => {
      const date = new Date(item.AllDates);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      return `${year}-${month}-${day}`;
    });
    this.getTwelveMonthMovingAverage = [];

    let getLastTwelveMonthData = lastTwelveMonthsData;
    if (getLastTwelveMonthData.length > 0) {
      let arr = [];

      if (this.IsThreeDayMovingAverage) {
        let threeDayData = this.getTwelveMonthMovingAverageData(data, 2);
        threeDayData.movingdata = threeDayData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeDayData.movingdata, 2, 3);
        arr = value;
        if (threeDayData.recordsExist) {
          arr = arr.slice(2);
        } else {
          // let totalrecordsExist = 2 - threeDayData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }
          if (threeDayData.existRecordCount > 0) {
            arr = arr.slice(threeDayData.existRecordCount);
            let difference = 2 - threeDayData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 2; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      if (this.IsOneWeekMovingAverage) {
        let oneWeekData = this.getTwelveMonthMovingAverageData(data, 6);
        oneWeekData.movingdata = oneWeekData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneWeekData.movingdata, 6, 7);
        arr = value;
        if (oneWeekData.recordsExist) {
          arr = arr.slice(6);
        } else {
          // let totalrecordsExist = 6 - oneWeekData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (oneWeekData.existRecordCount > 0) {
            arr = arr.slice(oneWeekData.existRecordCount);
            let difference = 6 - oneWeekData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 6; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      if (this.IsOneMonthMovingAverage) {
        let oneMonthData = this.getTwelveMonthMovingAverageData(data, 30);
        oneMonthData.movingdata = oneMonthData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(oneMonthData.movingdata, 30, 31);
        arr = value;
        if (oneMonthData.recordsExist) {
          arr = arr.slice(30);
        } else {
          // let totalrecordsExist = 30 - oneMonthData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (oneMonthData.existRecordCount > 0) {
            arr = arr.slice(oneMonthData.existRecordCount);
            let difference = 30 - oneMonthData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 30; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }

        }
      }

      if (this.IsThreeMonthMovingAverage) {
        let threeMonthData = this.getTwelveMonthMovingAverageData(data, 89);
        threeMonthData.movingdata = threeMonthData.movingdata.filter(item => item.TotalPctComfort !== 0);
        let value = this.calculateMovingAverageTrending(threeMonthData.movingdata, 89, 90);
        arr = value;
        if (threeMonthData.recordsExist) {
          arr = arr.slice(89);
        } else {
          // let totalrecordsExist = 89 - threeMonthData.existRecordCount;
          // for(let i = 0; i< totalrecordsExist; i++){
          //   arr[i].TotalPctComfort = 0;
          // }

          if (threeMonthData.existRecordCount > 0) {
            arr = arr.slice(threeMonthData.existRecordCount);
            let difference = 89 - threeMonthData.existRecordCount;
            for (let i = 0; i < difference; i++) {
              arr[i].TotalPctComfort = 0;
            }
          } else {
            for (let i = 0; i < 89; i++) {
              arr[i].TotalPctComfort = 0;
            }
          }
        }
      }

      this.getTwelveMonthMovingAverage = arr;
    } else {
      this.getTwelveMonthMovingAverage = [];
    }
  }


    getOneMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let oneMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    oneMonthsAgo.setMonth(currentDate.getMonth() - 1);

    let lastOneMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= oneMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastOneMonthsData[0].AllDates);

    let lastTwoRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastTwoRecords.push(data[i]);
        if (lastTwoRecords.length > count) {
          lastTwoRecords.shift();
        }
      }
    }
    lastTwoRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastTwoRecords.length; j++) {
      lastOneMonthsData.unshift(lastTwoRecords[j]);
    }
    return lastOneMonthsData;
  }
    getThreeMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let threeMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3);

    let lastThreeMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= threeMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastThreeMonthsData[0].AllDates);

    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }
    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastRecords.length; j++) {
      lastThreeMonthsData.unshift(lastRecords[j]);
    }
    return lastThreeMonthsData;
  }
    getSixMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let SixMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    SixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    let lastSixMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= SixMonthsAgo && itemDate <= currentDate;
    });

    const targetDate = new Date(lastSixMonthsData[0].AllDates);
    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }
    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });
    for (let j = 0; j < lastRecords.length; j++) {
      lastSixMonthsData.unshift(lastRecords[j]);
    }
    return lastSixMonthsData;
  }
  getTwelveMonthMovingAverageData(data, count) {
    let getLastElementOfData = data[data.length - 1];
    let currentDate = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    let TwelveMonthsAgo = getLastElementOfData?.AllDates ? new Date(getLastElementOfData.AllDates) : new Date();
    TwelveMonthsAgo.setMonth(currentDate.getMonth() - 13);

    let lastTwelveMonthsData = data.filter(item => {
      let itemDate = new Date(item.AllDates);
      return itemDate >= TwelveMonthsAgo && itemDate <= currentDate;
    });
    const targetDate = new Date(lastTwelveMonthsData[0].AllDates);
    let lastRecords = [];
    for (let i = 0; i < data.length; i++) {
      const currentDate = new Date(data[i].AllDates);
      if (currentDate < targetDate) {
        lastRecords.push(data[i]);
        if (lastRecords.length > count) {
          lastRecords.shift();
        }
      }
    }

    lastRecords.sort((a, b) => {
      const dateA = new Date(a.AllDates);
      const dateB: Date = new Date(b.AllDates);
      return dateB.getTime() - dateA.getTime();
    });

    for (let j = 0; j < lastRecords.length; j++) {
      lastTwelveMonthsData.unshift(lastRecords[j]);
    }

    if (lastRecords.length == count) {
      return { movingdata: lastTwelveMonthsData, recordsExist: true, existRecordCount: lastRecords.length }
    } else {
      return { movingdata: lastTwelveMonthsData, recordsExist: false, existRecordCount: lastRecords.length }
    }
  }

    public toggleSeries(): void {
    this.animateChart = !this.animateChart;
    if (!this.animateChart) {
      if (this.IsOneMonthSiteComfort) {
        this.OneMonthSiteData = [];
        this.getOneMonthMovingAverage = [];
      }
      if (this.IsThreeMonthSiteComfort) {
        this.ThreeMonthSiteData = [];
        this.getThreeMonthMovingAverage = [];
      }
      if (this.IsSixMonthSiteComfort) {
        this.SixMonthSiteData = [];
        this.getSixMonthMovingAverage = [];
      }
      if (this.IsTwelveMonthComfort) {
        this.TwelveMonthSiteData = [];
        this.getTwelveMonthMovingAverage = [];
      }
    }
    else {
      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }
      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
  }

    categories = []
    getAllSiteComfortData(response) {
      if (response.StatusCode == StatusCodes.OK && response != null) {
        if (response.response.SiteComfortData.length == 0) {
          this.temperatureDataAvailable = false;
          this.shareData.temperatureAvailable$.next(this.temperatureDataAvailable);
        } else {
          this.temperatureDataAvailable = true;
          this.shareData.temperatureAvailable$.next(this.temperatureDataAvailable);
        }
        let data = response.response.SiteComfortData.map(function (obj) {
        let date;
        if (obj.AllDates != undefined && obj.AllDates != null)
          date = new Date(obj.AllDates);
          // obj.AllDates = (date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()).toString();
          obj.AllDates = date;
          return obj;
        });
        let filteredData = data.filter(
        (item) => (item.TotalPctComfort ?? 0) !== 0);

        this.getSiteComfortData = filteredData;
        this.categories = this.getSiteComfortData.map(item => item.AllDates);
        this.getOneMonthSiteComfortData();
        this.changeMonthTabSiteComfort('1M')
        this.getTwelveMonthChangePercentage = 0
        this.getTwelveMonthChangePercentage = response.response.PercentageChange;
        this.movingAverageCount = response.response.MonthsDiff;
      } else {
        this.temperatureDataAvailable = false;
        this.shareData.temperatureAvailable$.next(this.temperatureDataAvailable);
        this.toastrService.error("Internal Server Error, Please Contact Support")
      }
    }

    calculateMovingAverageTrending(data, recordCount, trendingCount) {
    const movingAverages: any[] = [];
    for (let i = 0; i < data.length; i++) {
      let sum = 0;
      let count = 0;
      for (let j = Math.max(0, i - recordCount); j <= i; j++) {
        if (data[j]) {
          sum += data[j].TotalPctComfort;
          count++;
        }
      }
      const average = sum / trendingCount;
      movingAverages.push({
        'AllDates': data[i].AllDates,
        'TotalPctComfort': average
      });
    }
    return movingAverages;
  }

    changeMovingAverageTrending(trendLine) {
    if (trendLine == '3D') {
      this.IsThreeDayMovingAverage = true;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '1W') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = true
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '1M') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = true
      this.IsThreeMonthMovingAverage = false;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
    if (trendLine == '3M') {
      this.IsThreeDayMovingAverage = false;
      this.IsOneWeekMovingAverage = false
      this.IsOneMonthMovingAverage = false
      this.IsThreeMonthMovingAverage = true;

      if (this.IsOneMonthSiteComfort) {
        this.getOneMonthSiteComfortData();
      }
      if (this.IsThreeMonthSiteComfort) {
        this.getThreeMonthSiteComfortData();
      }
      if (this.IsSixMonthSiteComfort) {
        this.getSixMonthSiteComfortData();
      }

      if (this.IsTwelveMonthComfort) {
        this.getTwelveMonthSiteComfortData();
      }
    }
  }

  // For Labels
    customDivisionsForTwelveMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.TwelveMonthSiteDates, 10).map(
          (d) => new Date(d).toLocaleDateString()));
        // console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit'
      })
      : '';
    };
  })();
   //Formatting the dates for site comfort data
  customDivisionsForThreeMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.ThreeMonthSiteDates, 10).map((d) => new Date(d).toLocaleDateString()));
        console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        day: 'numeric',
        month: '2-digit'
      })
      : '';
    };
  })();

  customDivisionsForSixMonths = (() => {
    let hasRun: boolean = false;
    let labels: string[] = [];

    return (e: any): string => {

      if(!hasRun) {
        hasRun = true;
        labels = this.checkGeneratedLabels(
          this.generateSmartLabels(this.SixMonthSiteDates, 10).map(
          (d) => new Date(d).toLocaleDateString()));
        console.log(labels);
      }

      const currentDate = new Date(e.value).toLocaleDateString();

      const isMatch = labels.some(label => new Date(label).toLocaleDateString() === currentDate);

      return isMatch
        ? new Date(e.value).toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit'
      })
      : '';
    };
  })();

    public SiteComfortlabelContent = (e: AxisLabelVisualArgs): string => {
      return e.value + this.suffix;
    };

    checkGeneratedLabels(labels: string[]): string[] {
    const formattedLabels = labels.map((d) => this.formatDate(d));

    const matchingDates = formattedLabels.filter((label) =>
      this.categories.some(
        (cat) => new Date(cat).toDateString() === new Date(label).toDateString()
      )
    );

    const missingDates = formattedLabels.filter(
      (label) =>
        !this.categories.some(
          (cat) =>
            new Date(cat).toDateString() === new Date(label).toDateString()
        )
    );

    const filledDates = missingDates
      .map((missingDate) => {
        const target = new Date(missingDate);
        const availableDates = this.categories.filter((cat) => {
          const date = new Date(cat);
          return (
            date.getMonth() === target.getMonth() &&
            date.getFullYear() === target.getFullYear()
          );
        });

        return availableDates.sort(
          (a, b) => new Date(a).getTime() - new Date(b).getTime()
        )[0];
      })
      .filter(Boolean);

    const validLabels = [...matchingDates, ...filledDates];
    validLabels.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    return validLabels;
  }
    formatDate(date: string): string {
    const parts = date.split('/');
    const [day, month, year] = parts.map((p) => p.padStart(2, '0'));
    return new Date(year + '-' + month + '-' + day).toISOString().split('T')[0];
  }

    generateSmartLabels(categories: string[], maxDivisions: number): string[] {
    if (!categories?.length || maxDivisions <= 0) return [];

    const sortedDates = categories
      .map((d) => new Date(d))
      .sort((a, b) => a.getTime() - b.getTime());

    const min = sortedDates[0].getTime();
    const max = sortedDates[sortedDates.length - 1].getTime();
    const interval = (max - min) / maxDivisions;

    const timeIntervals = [
      { label: '1 day', ms: 1 * 24 * 60 * 60 * 1000 },
      { label: '1 week', ms: 7 * 24 * 60 * 60 * 1000 },
      { label: '2 weeks', ms: 14 * 24 * 60 * 60 * 1000 },
      { label: '1 month', ms: 30 * 24 * 60 * 60 * 1000 },
      { label: '2 months', ms: 60 * 24 * 60 * 60 * 1000 },
      { label: '3 months', ms: 90 * 24 * 60 * 60 * 1000 },
      { label: '6 months', ms: 180 * 24 * 60 * 60 * 1000 },
      { label: '1 year', ms: 365 * 24 * 60 * 60 * 1000 },
    ];

    const chosenInterval =
      timeIntervals.find((t) => t.ms >= interval) ||
      timeIntervals[timeIntervals.length - 1];

    const labels: string[] = [];
    let currentDate = sortedDates[0];

    labels.push(currentDate.toISOString());

    while (true) {
      const label = chosenInterval.label.toLowerCase();

      if (label.includes('day') || label.includes('week')) {
        const incrementDays = parseInt(label.split(' ')[0]) * (label.includes('week') ? 7 : 1);
        currentDate = new Date(currentDate.getTime() + incrementDays * 24 * 60 * 60 * 1000);
      } else if (label.includes('month')) {
        const incrementMonths = parseInt(label.split(' ')[0]);
        currentDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + incrementMonths, 1);
      } else if (label.includes('year')) {
        const incrementYears = parseInt(label.split(' ')[0]);
        currentDate = new Date(currentDate.getFullYear() + incrementYears, currentDate.getMonth(), 1);
      }

      if (currentDate.getTime() > max) break;

      labels.push(currentDate.toISOString());
    }

    return labels;
  }

    public labelContentOfOneMonth = (e: any) => {
    let days = ["S", "M", "T", "W", "T", "F", "S"]
    let month = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
    if (e.value.getDate() == 5 || e.value.getDate() == 10 || e.value.getDate() == 15 || e.value.getDate() == 20 || e.value.getDate() == 25 || e.value.getDate() == 30) {
      return `${days[e.value.getDay()]} \n ${month[(e.value.getMonth())]} ${e.value.getDate()}`;
    } else {
      return days[e.value.getDay()];
    }
  };
// Grid started from here
  isRowSelected(event: any): void {
    if (event != undefined) {
      this.SelectedRowData = event;
    }
  }

    getDetail() {
    if (this.SelectedRowData != undefined) {
      this.coreDataService.getQuoteDetail(this.SelectedRowData.selectedRows[0].dataItem.QuoteID).subscribe(response => {
        response.QuoteID = this.SelectedRowData.selectedRows[0].dataItem.QuoteID
        this.shareData.quoteDetail.next(response);
      },
        error => {
          if (error) {
            this.shareData.ErrorHandler(error);
          }
        });
      this.router.navigate(["/QuoteDetail"]);
    }
  }
    dataStateEquipmentComfort(state: DataStateChangeEvent) {
      this.EquipComfortState = state;
      this.equipComfortGrid = process(this.EquipmentThermalComfortdata, this.EquipComfortState);
    }


  GoToCompletedTable(dataItem: any) {

    const isSameRecord = this.lastTCompletedFilteredRecord?.Equipment === dataItem?.Equipment;

    if (isSameRecord) {

      this.IMDataDrivenTCompleteListState.filter = {
        logic: 'and',
        filters: []
      };
      this.gridDataForIMCompletedList = {
        data: this.getIMDataDrivenTaskCompleteList,
        total: this.getIMDataDrivenTaskCompleteList.length
      };
      this.lastTCompletedFilteredRecord = null;
    } else {
      this.IMDataDrivenTCompleteListState.filter = {
        logic: 'and',
        filters: [
          {
            logic: 'or',
            filters: [{
              field: 'Equipment',
              operator: 'eq',
              value: dataItem?.Equipment
            }]
          }
        ]
      };
      this.gridDataForIMCompletedList = {
        data: this.customFilterOnEquipment(this.getIMDataDrivenTaskCompleteList, this.IMDataDrivenTCompleteListState.filter.filters),
        total: this.getIMDataDrivenTaskCompleteList.length
      };
      this.lastTCompletedFilteredRecord = dataItem;
    }

    const tableElement = document.getElementById('completedTable');
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

    customFilterOnEquipment(data: any[], filters: any[]): any[] {
    return data.filter(item => {
      const fieldValue = item['Equipment']?.trim();
      const applyFilter = (filter) => {
        if (filter.field === 'Equipment' && filter.operator === 'eq') {
          return fieldValue === filter.value;
        }
        return false;
      };
      return filters.some(filterGroup => {
        if (filterGroup.filters) {
          if (filterGroup.logic === 'or') {
            return filterGroup.filters.some(subFilter => applyFilter(subFilter));
          } else if (filterGroup.logic === 'and') {
            return filterGroup.filters.every(subFilter => applyFilter(subFilter));
          }
        }
        return applyFilter(filterGroup);
      });
    });
  }

    GoToBacklogTable(dataItem: any) {

    const isSameRecord = this.lastTInBacklogFilteredRecord?.Equipment === dataItem?.Equipment;

    if (isSameRecord) {

      this.IMDataDrivenTBacklogListState.filter = {
        logic: 'and',
        filters: []
      };
      this.gridDataForIMBacklogList = {
        data: this.getIMDataDrivenTaskBacklogList,
        total: this.getIMDataDrivenTaskBacklogList.length
      };
      this.lastTInBacklogFilteredRecord = null;
    } else {
      this.IMDataDrivenTBacklogListState.filter = {
        logic: 'and',
        filters: [
          {
            logic: 'or',
            filters: [{
              field: 'Equipment',
              operator: 'eq',
              value: dataItem?.Equipment
            }]
          }
        ]
      };
      this.gridDataForIMBacklogList = {
        data: this.customFilterOnEquipment(this.getIMDataDrivenTaskBacklogList, this.IMDataDrivenTBacklogListState.filter.filters),
        total: this.getIMDataDrivenTaskBacklogList.length
      };
      this.lastTInBacklogFilteredRecord = dataItem;
    }
    const tableElement = document.getElementById('BacklogTable');
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }







}
