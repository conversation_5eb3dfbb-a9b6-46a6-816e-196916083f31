<admin-header (valueChange)='locationChange($event)'></admin-header>



<section class="margin-top-table-list">
    <div [ngStyle]="{'height.px': pageHeight}">
    <kendo-grid class="gridFontStyle" [data]="gridData" [skip]="state.skip" [sort]="state.sort" [filter]="state.filter"
        [sortable]="{allowUnsort: true, mode:'multiple'}" filterable="menu"(filterChange)="filterChange($event)" (dataStateChange)="dataStateChange($event)"
        [selectable]="true" [style.height]="'100%'" (scrollBottom)="loadMore()" [navigable]="true" [loading]="loading"
    >
        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
            <div class="container-fluid p-0">
                <div class="row">
                    <div class="col-md-6 col-6">
                        <h1>Sub Contractor</h1>
                    </div>
                    <div class="col-md-6 col-6 text-right">
                        <button type="button" [disabled]="subContractorList.length===0" class="ExportToExcelButtonCss" kendoGridExcelCommand icon="file-excel">
                            <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                            Export to Excel</button>
                    </div>
                </div>
            </div>
        </ng-template>
        <!-- <kendo-grid-column field="Location" title="Location" width="160">

        </kendo-grid-column> -->
        <kendo-grid-column field="LocationName" title="Location Name" width="210">

        </kendo-grid-column>
        <kendo-grid-column field="Date" title="Date" width="130" filter="date" operator="eq" format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="JobNumber" title="Service Call ID" width="170">

        </kendo-grid-column>
        <kendo-grid-column field="ServiceDesciption" title="Service Description" width="300">

        </kendo-grid-column>
        <kendo-grid-column field="Resolution" title="Resolution" width="160">
            <ng-template kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService"
            >
            <kendo-multiselect
                style="width:180px"
                [data]="resolutions"
                textField="Resolution"
                valueField="Resolution"
                [valuePrimitive]="true"
                [value]="dropdownFilters(filter)"
                (valueChange)="resolutionChange($event, filterService)"
            >
            </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="PurchaseOrder" title="Purchase Order" width="180">

        </kendo-grid-column>
        <kendo-grid-column field="CallStatus" title="Call Status" width="160">
            <ng-template kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService"
            >
            <kendo-multiselect
                style="width:170px"
                [data]="callStatuses"
                textField="CallStatus"
                valueField="CallStatus"
                [valuePrimitive]="true"
                [value]="dropdownFilters(filter)"
                (valueChange)="callStatusChange($event, filterService)"
            >
            </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="" title="" width="80">
            <ng-template kendoGridCellTemplate let-dataItem>
                <button [ngClass]="{'hideButton':dataItem.CallStatus==='OPEN'}" kendoButton
                    class="followupButton downloadButton" title="DownLoad Call Summary Report" [primary]="true"
                    (click)="DownloadSubContractorReport(dataItem)" primary="true"><span
                        class="k-icon k-i-download"></span></button>
            </ng-template>

        </kendo-grid-column>
        <kendo-grid-excel fileName="Sub Contractor.xlsx" [fetchData]="allData">
            <kendo-excelexport-column field="LocationName" title="Location Name" width="210">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Date" title="Date" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="JobNumber" title="Service Call ID" width="160">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="ServiceDesciption" title="Service Description" width="300">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Resolution" title="Resolution" width="140">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="PurchaseOrder" title="Purchase Order" width="170">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="CallStatus" title="Call Status" width="140">
            </kendo-excelexport-column>
        </kendo-grid-excel>
     </kendo-grid>
     <div *ngIf="isShow" style="text-align: center;">
         <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
             <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
         </button>
     </div>
    </div>
</section>
<admin-footer class="subContractorFooter"></admin-footer>