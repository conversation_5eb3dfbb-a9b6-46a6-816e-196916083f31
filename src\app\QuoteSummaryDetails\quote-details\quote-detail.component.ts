import { Component, OnInit, ViewChild, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { AuthenticationService } from "../../Services/authentication.service";
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { process, State } from '@progress/kendo-data-query';
import { ElementRef } from "@angular/core";
import { Title } from "@angular/platform-browser";
import { SelectableSettings } from '@progress/kendo-angular-grid';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { QuotePriorityClass, QuoteDetailsListModel, QuoteTypes, locationDateFilterEntity, ReasonEntity, QuoteDeclineEntity } from "./quote-detail.model";
import { SelectAllCheckboxState, GridDataResult, DataStateChangeEvent } from '@progress/kendo-angular-grid';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { saveAs } from '@progress/kendo-file-saver';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Location } from '@angular/common';
import { first } from 'rxjs/operators';
@Component({
  selector: 'admin-quote-detail',
  templateUrl: './quote-detail.component.html',
  styleUrls: ['./quote-detail.component.scss']
})
export class QuoteDetailComponent implements OnInit, OnDestroy {
  mySubscription: any;

  quoteDeclineForm: FormGroup;
  submitted = false;
  public selectAllState: SelectAllCheckboxState = 'unchecked';
  section = [];
  startDate: string;
  endDate: string;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  public range: any;
  priority = "PRIORITY";
  public showDeclinePopup = false;
  public declineReasonData: Array<ReasonEntity> = [];
  public quoteDeclineData: QuoteDeclineEntity = new QuoteDeclineEntity();
  public decline: any = {
    Reason: '',
    Description: ''
  }
  allowToApprove: boolean = false;
  isActivationDate: boolean = false;
  public selectableSettings: SelectableSettings;
  public checkboxOnly = false;
  originalDate = new Date();
  public state: State = {
    skip: 0,
    take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  TypeOfQuote: number = 1;
  TwelveMonthsWarranty: number = 3;
  quotesTypes: any = QuoteTypes;
  list: QuoteDetailsListModel = new QuoteDetailsListModel();
  QuoteID: string;
  dataItem: QuotePriorityClass = new QuotePriorityClass();
  gridData: GridDataResult;
  public listItems: Array<any>;
  IsRestricted: boolean = false;
  IsExpiredQuote: boolean = false;
  constructor(private _location: Location, private spinner: SpinnerVisibilityService, private fb: FormBuilder, private titleService: Title, private authService: AuthenticationService, private coreDataService: CoreDataService, private Share: SharedDataService,
    private route: ActivatedRoute, private router: Router) {

    this.Share.removeBacklogData();
    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };

    this.mySubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        // Trick the Router into believing it's last link wasn't previously loaded
        this.router.navigated = false;
      }
    });

    let pageTitle = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTitle);
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (this.locationDateFilterData === null) {
      this.range = {
        start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
        end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
      }
    }
    else {
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
  }

  @ViewChild('intro') div: ElementRef;
  @ViewChild('sow') sow: ElementRef;
  @ViewChild('exclusions') exclusions: ElementRef;
  public onSelectAllChange(checkedState: SelectAllCheckboxState) {
    if (checkedState === 'checked') {
      this.selectAllState = 'checked';
      this.list.sectionDetails.map((item) => {
        //MS: If client select any section, it will be treated as preferred & will be updated when approved.
        item.Preferred = true;
        this.section.push(item);
      });
    } else {
      this.section = [];
      this.selectAllState = 'unchecked';
    }
  }
  ngOnInit() {
    this.checkStatus();
    this.listItems = this.dataItem.QuotePriorityData;

    this.loadData(this.QuoteID);
    // FK: initialize quote decline form validation
    this.quoteDeclineForm = this.fb.group({
      reason: ['', Validators.required],
      description: ['']
    })
  }
  // convenience getter for easy access to form fields
  get f() { return this.quoteDeclineForm.controls; }

  convertDateToString() {
    return new Date(this.list.ApprovedDate).getFullYear() + "-" + (new Date(this.list.ApprovedDate).getMonth() + 1) + "-" + new Date(this.list.ApprovedDate).getDate()
  }
  loadData(id: any) {
    this.IsRestricted = false;
    this.IsExpiredQuote = false;
    this.Share.quoteDetail.subscribe(response => {
      this.QuoteID = response.QuoteID
      if (response != null && response != undefined) {
        if (response.StatusCode === 200) {
          if (response.response.QuoteStatus === "LOST") {
            this._location.back();
            this.Share.showInfo('#' + this.QuoteID + ' Quote has been lost');
          } else {
            this.list = response.response;
            this.div.nativeElement.innerHTML = this.list.Introduction;
            this.sow.nativeElement.innerHTML = this.list.SOW;
            this.exclusions.nativeElement.innerHTML = this.list.Exclusion;
            if (this.list.sectionDetails.length > 0) {
              this.allowToApprove = true;
            }
            if (this.list.QuoteTypeCd == this.quotesTypes.ProjectsQuote) {
              this.TwelveMonthsWarranty = 12;
            }
            this.gridData = process(this.list.sectionDetails, this.state);
            if(this.list.IsRestricted == true){
              this.IsRestricted = true;
            }else {
              this.IsRestricted = false;
            }

            if(this.list.QuoteStatus === "EXPIRED"){
              this.IsExpiredQuote = true;
            }
          }
        }
      }
    },
      error => {
        if (error) {
          this.Share.ErrorHandler(error);
        }
      });
  }

  getMainReport() {
    this.spinner.show();
    let payload = {
      QuoteReference: this.list.QuoteReference,
      Revision: this.list.Revision,
      TypeOfQuote: this.TypeOfQuote,
      TwelveMonthsWarranty: this.TwelveMonthsWarranty,
      ReportName: 'MainReport',
      HeaderFooter: this.list.HeaderFooter,
      CustNmbr: this.list.CustNmbr,
      UserID: this.list.AccountManagerID
    }
    this.coreDataService.generateMainReport(payload).pipe(first()).subscribe((res) => {
      if (res != null && res != undefined) {
        if (res.status === 200) {
          let result = res['_body'];
          // let fileType = result.type.split('/')[1];
          this.Share.showSuccess("Quote Document downloaded successfully");
          this.spinner.hide();
          var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
          let reportName = 'Airmaster-' + this.list.QuoteReference + ".pdf";
          saveAs(blob, reportName);
        }
        else if (res.status === 204) {
          this.Share.showWarning("Quote Document could not be found. Please Contact Support");
        }
      }
    },
      error => {
        this.Share.ErrorHandler(error);
        this.spinner.hide();
      });
  }

  handleWarrantyChange(evt) {
    var target = evt.target;
    this.TwelveMonthsWarranty = target.value;
  }

  handleTemplateChange(evt) {
    var typeOfQuote = evt.target;
    this.TypeOfQuote = typeOfQuote.value;
  }

  updateQuotes(form) {
    var filterObject = {
      QuoteID: parseInt(this.QuoteID),
      WorkOrderNo: form.WorkOrderNo,
      PriorityCd: this.list.PriorityCd,
    };
    //Y.A Checking for 6month older date
    var todayDate = new Date();
    todayDate = new Date(todayDate.setMonth(todayDate.getMonth() - 6));
    if (this.list.QuoteStatus == 'AWAITING APPROVAL') {
      if (new Date(this.list.ApprovedDate) < todayDate) {
        this.isActivationDate = true;
      }
      //Check whether minimum one Section is Checked
      else if (this.section.length > 0) {
        //MS--START
        this.coreDataService.ApproveQuote(filterObject, JSON.stringify(this.section)).subscribe(
          data => {
            if (data != null && data != undefined) {
              if (data.StatusCode === 200) {
                this.getMainReport();
                this.Share.showSuccess('Quote has been approved');
                this.loadData(this.QuoteID);
              }
            }

          },
          error => {
            this.Share.showError("Unable to Approve Quote. Please Contact Support");
          });
      } else {
        this.Share.showWarning("Please check at least one section to approve the Quote");
      }
    }
    //MS--END
  }

  public dataStateChange(state: DataStateChangeEvent) {
    this.state = state;
    this.gridData = process(this.list.sectionDetails, this.state);
  }

  UpdateDetail(event: any, idx: number) {
    if (event.srcElement.checked === true) {
      this.list.sectionDetails[idx].Preferred = true;
      this.section.splice(idx, 0, this.list.sectionDetails[idx]);
    }
    if (event.srcElement.checked === false) {
      if (this.section.length === 1) {
        this.section = [];
      }
      this.list.sectionDetails[idx].Preferred = false;
      this.section.splice(idx, 1);
    }
    this.checkStatus();
  }
  checkStatus() {
    if (this.section.length === 0) {

      this.selectAllState = 'unchecked';
    }
    else if (this.section.length > 0 && this.section.length < this.list.sectionDetails.length) {
      this.selectAllState = 'indeterminate';
    }
    else {
      this.selectAllState = 'checked';
    }
  }

  // FK: get code data for quote decline "reason"
  getCodeData() {
    this.coreDataService.getCodeData().subscribe(response => {
      if (response.response != undefined && response.response != null) {
        if (response.StatusCode == 200) {
          this.declineReasonData = response.response.filter(o => o.CodeName === "QUOTE DECLINED STATUS")
        }
      }
    },
      error => {
        if (error) {
          this.Share.ErrorHandler(error);
        }
      });
  }

  // FK: show decline popup
  declineQuoteShow() {
    this.submitted = false;
    this.getCodeData();
    this.showDeclinePopup = true;
  }

  //FK: close decline popup
  closeDeclinePopup() {
    this.submitted = false;
    this.decline.Reason = '';
    this.decline.Description = '';
    this.showDeclinePopup = false;
  }

  // FK: quote decline
  declineQoute() {
    this.submitted = true;
    if (this.quoteDeclineForm.invalid) {
      return;
    } else {
      this.submitted = false;
      this.quoteDeclineData.QuoteID = parseInt(this.QuoteID);
      this.quoteDeclineData.StatusReasonCd = parseInt(this.decline.Reason.CodeID);
      this.quoteDeclineData.ClosureNotes = this.decline.Description;
      this.coreDataService.DeclineQuote(this.quoteDeclineData).subscribe(response => {
        if (response != null && response != undefined) {
          if (response.StatusCode === 200) {
            this.decline.Reason = '';
            this.decline.Description = '';
            this.showDeclinePopup = false;
            this.Share.showSuccess('#' + this.QuoteID + ' Quote has been declined');
            this.router.navigate(["/Quotes", this.priority, "All"]);
          }
          else {
            this.Share.showError("Error has occured please contact support.");
          }
        }
      },
        error => {
          if (error) {
            this.spinner.hide();
            this.Share.ErrorHandler(error);
          }
        });
    }

  }

  ngOnDestroy() {
    if (this.mySubscription) {
      this.mySubscription.unsubscribe();
    }
  }


}
