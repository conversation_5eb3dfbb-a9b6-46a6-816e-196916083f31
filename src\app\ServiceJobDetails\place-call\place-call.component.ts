import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { JwtHelper } from 'angular2-jwt';
import { CoreDataService } from "../../Services/core-data.service";
import { PlaceCallModel, equipmentDropdownlistModel, CustomerLocationByName } from "./place-call.model";
import { SharedDataService } from "../../Services/shared-data.service";
import { AuthenticationService } from "../../Services/authentication.service";
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { Subscription } from 'rxjs';
import { SessionStorageService } from 'ngx-webstorage';
import { TokenDecodedEntities } from '../../Common/shared';
import { first } from 'rxjs/operators';
export let isBrowserRefreshed: boolean = false;
@Component({
  selector: 'app-place-call',
  templateUrl: './place-call.component.html',
  styleUrls: ['./place-call.component.css']
})
export class PlaceCallComponent implements OnInit, OnDestroy {
  @ViewChild('customerStatus') formRef: any;
  selectedLocations: any;
  public defaultItemLocation: { CustName: string, Name: string, CustomerID: number, LocationCode: string, LocationID: number } = { CustName: "Select Location", Name: "Select Location", CustomerID: null, LocationCode: " ", LocationID: null };
  userId: number;
  tokenData: TokenDecodedEntities = new TokenDecodedEntities();
  EquipmentfilterKeys = ["Code", "Make", "EquipmentType", "Model", "BarCode"];
  isCloseDisabled: boolean = true;
  selectedPlaceCallData: PlaceCallModel = new PlaceCallModel();
  public LocationList: Array<CustomerLocationByName>;
  subscription: Subscription;
  jwtHelper: JwtHelper = new JwtHelper();
  submitCallUrl: string;
  isBacklogData: boolean = false;
  BacklogID: number;
  workOrderLimitValue: number;
  equipmentList: Array<equipmentDropdownlistModel> = [];
  public LocationsDropdown: Array<CustomerLocationByName> = [];
  public equipmentDropDownList: Array<equipmentDropdownlistModel> = [];
  public defaultLocation: { Name: string, CustName: string, fullName: string, LocationID: number } = { Name: "Select ", CustName: 'Select', fullName: 'Select', LocationID: null };
  constructor(private session: SessionStorageService, private router: Router, private spinner: SpinnerVisibilityService, private route: ActivatedRoute, private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private authdata: AuthenticationService) {
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
    let tokenData = this.authdata.getTokenData();
    this.tokenData = tokenData;
    isBrowserRefreshed = !router.navigated;
    if (isBrowserRefreshed === false) {

      this.loadCustomerLocationDropdown(tokenData.UserID, undefined);
    }

    // this.loadPlaceCallUrl();
  }
  filterLocation(value) {
    if (value != null && value != undefined) {
      // this.LocationList = this.LocationsDropdown.filter((s) => s.Name.toLowerCase().indexOf(value.toLowerCase()) !== -1);
      this.LocationList = this.LocationsDropdown.filter((s) => {
        const lowerCaseValue = value.toLowerCase();
        return (
          s.Name.toLowerCase().indexOf(lowerCaseValue) !== -1 ||
          s.CustName.toLowerCase().indexOf(lowerCaseValue) !== -1 ||
          s.CustomerCode.toLowerCase().indexOf(lowerCaseValue) !== -1 ||
          s.LocationCode.toLowerCase().indexOf(lowerCaseValue) !== -1
        );
      });
    }
  }
  FilterEquipment(value) {
    if (value != null && value != undefined) {
      var lowSearch = value.toLowerCase();
      this.equipmentDropDownList = this.filterevalues(lowSearch, this.EquipmentfilterKeys);
    }
  }
  filterevalues(search, keys) {
    var lowSearch = search.toLowerCase();
    return this.equipmentList.filter(function (data) {
      return keys.some(key =>
        String(data[key]).toLowerCase().includes(lowSearch)
      );
    });
  }
  ngOnInit(): void {
    let backlogData = JSON.parse(localStorage.getItem('BacklogData'));
    if (backlogData != null && backlogData != undefined) {
      let locationsInSession = this.session.retrieve('locations');
      if (locationsInSession != null && locationsInSession != undefined) {
        let filterLocationData = locationsInSession.filter((obj) => {
          return obj.LocationCode == backlogData.ADRSCODE.trim();
        })
        this.selectedLocations = filterLocationData[0];
        this.selectedPlaceCallData.EquipmentID = backlogData.Equipment;
        this.selectedPlaceCallData.Description = backlogData.TaskType;
        this.BacklogID = backlogData.ID;
        this.isBacklogData = true;
      } else {
        this.isBacklogData = false;
        this.shareData.showWarning("Location Code not found");
      }
    }
  }

  locationSelectionChange(value) {
    if (value != null && value != undefined) {
      this.loadEquipmentDetails(value.LocationID);
    }
  }
  loadEquipmentDetails(location) {
    if (location != null && location != undefined) {
      this.coredata.getEquipmentDropdownList(location).subscribe(res => {
        if (res.StatusCode === 200) {
          if (res.response != null && res.response != undefined) {
            this.equipmentList = res.response;
            this.equipmentDropDownList = this.equipmentList.slice();
            this.formRef.form.controls.equipment.touched = false;
          }
        }
      },
        catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
          }
        }
      );
    }
  }
  loadPlaceCallUrl() {

    this.coredata.getPlaceCallUrl().subscribe(res => {
      if (res.StatusCode === 200) {
        if (res.response != null && res.response != undefined) {
          this.submitCallUrl = res.response;
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);

        }

      }
    );

  }
  loadCustomerLocationDropdown(id, locations) {
    if (locations === undefined) {
      let locationsInSession = this.session.retrieve('locations');
      if (locationsInSession != null) {
        this.LocationsDropdown = locationsInSession;
        this.LocationList = this.LocationsDropdown.slice();
      }
      else if (locationsInSession === null) {
        this.coredata.getCustomerLocation(id).pipe(first()).subscribe(res => {
          if (res.StatusCode === 200) {
            this.LocationsDropdown = res.response;
            this.LocationList = this.LocationsDropdown.slice();
          }
        },
          catchError => {
            if (catchError) {
              this.shareData.ErrorHandler(catchError);
              // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
            }
          });
      }
    }
    else if (locations != undefined) {
      this.LocationsDropdown = locations;
      this.LocationList = this.LocationsDropdown.slice();
    }


  }

  savePlaceCall(form: NgForm) {
    this.spinner.show();
    if (this.isBacklogData) {
      const data = {
        ID: this.BacklogID,
        PurchaseOrder: this.selectedPlaceCallData.PurchaseOrder,
        WorkOrderLimit: (this.workOrderLimitValue != null && this.workOrderLimitValue != undefined) ? this.workOrderLimitValue : 0
      }
      this.coredata.updateFTPlaceCallData(data).subscribe(res => {
        this.shareData.showSuccess("Call submitted successfully");
        this.router.navigate(["/IM-Dashboard"]);
        this.spinner.hide();
      },
        catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    } else {
      let token = this.authdata.getTokenValue();
      let customer = this.LocationList.find(element => element.LocationID === this.selectedLocations['LocationID']).CustomerCode.toString();
      let payload = {
        Customer: customer,
        Location: this.selectedLocations['LocationCode'],
        AssetID: this.selectedPlaceCallData.EquipmentID,
        ProblemType: "T&M",
        RequestedBy: token.Name,
        RequesterPhoneNo: "",
        Priority: "4",
        PurchaseOrder: this.selectedPlaceCallData.PurchaseOrder,
        WorkDescription: this.selectedPlaceCallData.Description,
      }
      this.spinner.show();
      this.coredata.PlaceACall(payload).subscribe(res => {
        this.shareData.showSuccess("Call submitted successfully");
        form.reset();
        this.equipmentDropDownList = [];
        this.spinner.hide();
      },
        catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    }

  }
  ngOnDestroy() {
    // this.subscription.unsubscribe();
  }

  addToArray() {

  }
  locationChange(event) {
    this.loadCustomerLocationDropdown(this.tokenData.UserID, event);
  }

}
