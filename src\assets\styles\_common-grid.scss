.k-panelbar > .k-item > .k-link {
  padding: 4px 13px;
  background: #0093d7 !important;
  color: white !important;
}

.bulkReportDownload .k-grid td {
  padding: 2px 23px;
  border-style: none;
  border-color: inherit;
  outline: 0;
  font-weight: inherit;
  text-align: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
}
.bulkReportDownload .k-grid th {
  padding: 6px 24px;
  border-width: 0 0 1px;
  white-space: nowrap;
  background: #343a40;
}

.bulkReportDownload .k-grid th .k-grid-filter {
  padding: 4px;
  width: calc(8px + 2em);
  height: calc(8px + 2em);
  box-sizing: border-box;
  outline: 0;
  line-height: 2;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  position: absolute;
  right: 13px;
  bottom: 0;
  z-index: 1;
}

.bulkReportDownload .k-grid-toolbar {
  padding: 3px 11px;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: inherit;
  text-overflow: clip;
  cursor: default;
  display: block;
}

.bulkDocDropDown .k-icon {
  color: black !important;
}

.datePickerBulkDoc .k-icon {
  color: black !important;
}
.treeViewUI .k-icon {
  color: #0291d5 !important;
}
 .k-menu.k-context-menu .k-item:hover {
  color: #0291d5 !important;
  background-color: #eeeeee !important;
}
.treeViewUI:hover{

  color: #0291d5 !important;
}


.contextMenuUI .k-menu {
  padding: 0px 0px !important;
}
//Original
//.k-popup{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch;margin:0;padding:0px 0;border-style:solid;font-size:14px;line-height:2;box-sizing:content-box}

section {
  margin-bottom: 9%; //default kendo grid section css
  display: block;
}
section .title-position {
  position: relative;
  bottom: -7px;

  max-width: 1227px;
}

kendo-grid .k-i-arrow-e,
.k-i-seek-e,
.k-i-arrow-w,
.k-i-seek-w {
  color: black !important;
}

.gridFontStyle {
  position: relative;
  cursor: pointer;
  font-size: 70%;
  font-family: calibri light , "Roboto";
  font-weight: 500;
}

.UserInfoGrid .k-grid td
{
  font-size: 11px;
  border: none;
  padding: 3px 22px !important;
  background: white;
}
.UserInfoGrid .k-grid td:nth-child(2),.UserInfoGrid .k-grid td:nth-child(3)
{
  position: relative !important;
  right: 9px !important;
}

.UserInfoGrid .k-grid-header .k-header:not(.k-widget) {
  font-size: 12px;
  padding: 3px 19px;
  background: white;
  color: black;
  border: none;
 }

 .ClientPerception .k-grid td
{
  font-weight: 500;
  font-size: 11px;
  padding: 3px 22px !important;
  background: white;
  border-right: 1px solid #e0e0e0 !important;
}
.ClientPerception .k-grid-header .k-header:not(.k-widget) {
  font-size: 12px;
  padding: 3px 19px;
  background: white;
  color: black;
  border: none;
 }

 @media only screen and (max-width: 5871px) {
   .UserInfoGrid .k-grid td:nth-child(2), .UserInfoGrid .k-grid td:nth-child(3) {
    position: relative !important;
    right: 0px !important;
}
}




.UserInfoGrid .k-grid-content{
  overflow-y: hidden !important;
}





