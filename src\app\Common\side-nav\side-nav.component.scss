#ftSidebar {
  width: 230px;
  background: #F5F3EF!important;
  height: 100vh;
  padding-top: 2vh;
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2vh;
  z-index: 999;

   .menu-card {
    width: 200px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 20px 10px 10px;
    box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.25);
    border: 1px solid #c4c5c7;

    .header {
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
        line-height: 1;
        letter-spacing: -1px;
        font-weight: 700;
        font-size: 1.5rem;
        font-family: Roboto !important;
      }
    }
   
    .menu-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      i {
        font-size: 1rem;
        color: #343a40;
        width: 20px;
      }

      a, span {
        font-size: 0.8rem;
        color: #343a40;
        font-weight: 500;
        text-decoration: none;
        font-family: Roboto !important;
        width: 55px;
      }

      small {
        font-size: 0.7rem;
        color: #888;
      }

      &.disabled {
        cursor: not-allowed;

        a {
          pointer-events: none;
        }
      }

      &.active-link {
        color: #007bff;
        font-weight: 600;

        i {
          color: #007bff;
        }
      }
    }
  }

  .toggle-card {
    width: 200px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 10px 30px 5px 10px;
    box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.25);
    border: 1px solid #c4c5c7;

    .toggle-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      label {
        font-size: 0.8rem;
        color: #343a40;
        font-family: Roboto !important;
        font-weight: 500;
      }
    }

    .switch {
      position: relative;
      display: inline-block;
      width: 26px;
      height: 12px;

      input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0; left: 0;
        right: 0; bottom: 0;
        background-color: #d9d9d9;
        transition: background-color 0.3s;
        border-radius: 16px;

        &::before {
          position: absolute;
          content: "";
          height: 13px;
          width: 13px;
          left: 0;
          bottom: 0;
          background-color: whitesmoke;
          transition: 0.4s;
          border-radius: 50%;
          transition: transform 0.3s, background-color 0.3s;
        }
      }

      input:checked + .slider:before {
        transform: translateX(12px);
        background-color: #575757;
      }
    }
  }
}

// Mobile Responsiveness
// @media (max-width: 768px) {
//   #ftSidebar {
//     width: 0;
//     overflow: hidden;
//   }
// }
@media (min-width: 1368px) and (max-width: 1454px) {

  #ftSidebar {
    width: 255px;
    padding-top: 2vh;

    .menu-card {
      width: 220px;

      .header h3 {
        font-size: 1.6rem;
      }

      .menu-item {
        gap: 14px;
        i {
          font-size: 1.1rem;
        }

        a, span {
          font-size: 0.9rem;
        }

        small {
          font-size: 0.8rem;
        }
      }
    }

    .toggle-card {
      width: 220px;
      
      .toggle-item label {
        font-size: 0.9rem;
      }
    }
  }
}

@media (min-width: 1454px) and (max-width: 1601px) {
  #ftSidebar {
    width: 290px;
    padding-top: 2vh;

    .menu-card {
      width: 245px;

      .header h3 {
        font-size: 1.7rem;
      }

      .menu-item {
        gap: 22px;
        i {
          font-size: 1.2rem;
        }

        a, span {
          font-size: 1rem;
        }

        small {
          font-size: 0.9rem;
        }
      }
    }

    .toggle-card {
      width: 245px;
      
      .toggle-item label {
        font-size: 1rem;
      }
    }
  }
}

@media only screen and (min-width: 1601px) and (max-width: 1800px)  {
  #ftSidebar {
    width: 305px;
    padding-top: 1vh;

    .menu-card {
      width: 260px;

      .header h3 {
        font-size: 1.8rem;
      }

      .menu-item {
        gap: 28px;
        i {
          font-size: 1.3rem;
        }

        a, span {
          font-size: 1.1rem;
        }

        small {
          font-size: 1rem;
        }
      }
    }

    .toggle-card {
      width: 260px;
      
      .toggle-item label {
        font-size: 1.1rem;
      }
    }
  }
}

@media only screen and (min-width: 1801px) and (max-width: 1920px) {
  #ftSidebar {
    width: 345px;
    padding-top: 0;

    .menu-card {
      width: 290px;

      .header h3 {
        font-size: 2rem;
      }

      .menu-item {
        gap: 34px;
        i {
          font-size: 1.5rem;
        }

        a, span {
          font-size: 1.3rem;
        }

        small {
          font-size: 1.2rem;
        }
      }
    }

    .toggle-card {
      width: 290px;
      
      .toggle-item label {
        font-size: 1.3rem;
      }
    }
  }
}
// @media only screen and (min-width: 1801px) and (max-width: 1920px) {
//   #ftSidebar {
//     width: 198px;
//   }
// }

// @media only screen and (min-width: 1921px) and (max-width: 2115px) {
//   #ftSidebar {
//     width: 218px;
//   }
// }

// @media only screen and (min-width: 2115px) {
//   #ftSidebar {
//     width: 274px;
//   }
// }