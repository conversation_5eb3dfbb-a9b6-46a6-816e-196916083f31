{"name": "customer-portal", "version": "2.0.1", "scripts": {"ng": "ng", "build": "ng build"}, "dependencies": {"3": "^2.1.0", "-": "0.0.1", "@angular/animations": "^10.2.5", "@angular/cdk": "^10.2.7", "@angular/common": "^10.2.5", "@angular/compiler": "^10.2.5", "@angular/core": "^10.2.5", "@angular/forms": "^10.2.5", "@angular/http": "^7.2.16", "@angular/localize": "^10.2.5", "@angular/material": "^10.2.7", "@angular/platform-browser": "^10.2.5", "@angular/platform-browser-dynamic": "^10.2.5", "@angular/router": "^10.2.5", "@angular/service-worker": "^10.2.5", "@fortawesome/angular-fontawesome": "^0.3.0", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@progress/kendo-angular-buttons": "^5.5.0", "@progress/kendo-angular-charts": "^5.3.0", "@progress/kendo-angular-common": "^2.0.0", "@progress/kendo-angular-dateinputs": "^4.3.1", "@progress/kendo-angular-dialog": "^4.2.3", "@progress/kendo-angular-dropdowns": "^4.4.1", "@progress/kendo-angular-excel-export": "^3.1.4", "@progress/kendo-angular-gauges": "^2.5.0", "@progress/kendo-angular-grid": "^4.7.3", "@progress/kendo-angular-icons": "^1.0.1", "@progress/kendo-angular-inputs": "^3.5.0", "@progress/kendo-angular-intl": "^2.0.2", "@progress/kendo-angular-l10n": "^2.0.2", "@progress/kendo-angular-label": "^2.3.2", "@progress/kendo-angular-layout": "^4.0.0", "@progress/kendo-angular-menu": "^2.0.5", "@progress/kendo-angular-navigation": "^0.2.0", "@progress/kendo-angular-pdf-export": "^2.0.4", "@progress/kendo-angular-popup": "^4.0.6", "@progress/kendo-angular-toolbar": "^2.2.0", "@progress/kendo-angular-tooltip": "^2.1.5", "@progress/kendo-angular-treeview": "^4.3.0", "@progress/kendo-angular-upload": "^6.0.2", "@progress/kendo-data-query": "^1.5.4", "@progress/kendo-drawing": "^1.9.0", "@progress/kendo-licensing": "^1.1.1", "@progress/kendo-support": "^0.1.0", "@progress/kendo-theme-material": "^3.24.0", "@progress/kendo-ui": "^2019.2.807", "@syncfusion/ej2-angular-heatmap": "^20.2.45-ngcc", "@types/highcharts": "^5.0.22", "@types/jasmine": "^2.8.6", "@types/jasminewd2": "^2.0.3", "angular-gauge-chart": "^0.7.2", "angular-highcharts": "^9.0.9", "angular-progress-bar": "1.0.11", "angular-progress-http": "1.0.0", "angular2-jwt": "^0.2.3", "angular2-moment": "^1.9.0", "angular2-recaptcha": "^1.1.0", "apexcharts": "^3.15.2", "bootstrap": "^4.5.0", "classlist": "^2.0.0", "classlist.js": "^1.1.20150312", "core-js": "^2.5.4", "file-saver": "^1.3.3", "font-awesome": "^4.7.0", "hammerjs": "^2.0.8", "highcharts": "^8.0.4", "install": "^0.12.2", "intl": "^1.2.5", "jquery": "^3.6.0", "jwthelper": "0.0.4", "moment": "^2.25.3", "ng-apexcharts": "^1.5.0", "ng-http-interceptor": "^4.0.0", "ng-http-loader": "^5.0.1", "ng-recaptcha": "^8.0.0", "ngx-bootstrap": "^3.0.1", "ngx-popover": "0.0.16", "ngx-textarea-autosize": "^2.0.0", "ngx-toastr": "^9.0.2", "ngx-webstorage": "^3.0.2", "polyfills": "^2.1.1", "popper.js": "^1.15.0", "rxjs": "^6.6.7", "rxjs-compat": "^6.0.0", "sass": "^1.62.0", "sass-loader": "^13.2.2", "underscore": "^1.10.2", "web-animations-js": "^2.3.1", "zingchart": "^2.9.9", "zingchart-angular": "^1.0.9", "zone.js": "~0.10.3"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1002.4", "@angular/cli": "^10.2.4", "@angular/compiler-cli": "^10.2.5", "@angular/language-service": "^10.2.5", "@types/file-saver": "0.0.1", "@types/node": "^12.7.2", "codelyzer": "^6.0.0", "protractor": "^5.3.0", "ts-node": "~5.0.1", "tslint": "~5.9.1", "typescript": "^4.0.2"}}