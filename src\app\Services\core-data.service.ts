import { Injectable } from "@angular/core";
import {
  Http,
  Response,
  URLSearchParams,
  RequestOptions,
  Headers,
  ResponseContentType,
} from "@angular/http";
import {
  HttpClient,
  HttpResponse,
  HttpHeaders,
  HttpParams,
  HttpRequest,
  HttpEvent,
  HttpProgressEvent,
  HttpEventType,
} from "@angular/common/http";
import { SharedDataService } from "../Services/shared-data.service";
import "rxjs/add/operator/catch";
import "rxjs/add/operator/map";
import { forkJoin } from "rxjs"; // RxJS 6 syntax
import { catchError } from "rxjs/operators";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import {
  GetCustomerServiceRequest,
  GetMomListRequest,
  GetMostExpensiveEquipmentRequest,
  GetOpenDebtRequest,
  GetOpenDebtsRequest,
  GetQuoteSummaryListRequest,
  GetQuoteSummaryRequest,
  GetTMMonthGraphRequest,
  GetTMMonthGraphRequest2,
} from "./models";
// import { PARAMETERS } from "@angular/core/src/util/decorators";

function isHttpResponse<T>(event: HttpEvent<T>): event is HttpResponse<T> {
  return event.type === HttpEventType.Response;
}
function isHttpProgressEvent(
  event: HttpEvent<unknown>
): event is HttpProgressEvent {
  return (
    event.type === HttpEventType.DownloadProgress ||
    event.type === HttpEventType.UploadProgress
  );
}
@Injectable()
export class CoreDataService {
  //Global Variables
  token: any;
  headers: any;
  progress: any;
  Client: string = "CP";
  orgName: string;

  //Endpoints
  baseUrl: string;
  oneDriveEndpoint: string;

  //APIs Urls
  GetTMMonthGraphuRL: string;
  GetTMMonthGraphByStatusCallsuRL: string;
  GetMostExpensiveEquipmentUrl: string;
  OpenDebtUrl: string;
  GetMostExpensiveEquipmentInvoicesUrl: string;
  QuoteDetailUrl: any;
  getAllQuote: string;
  updateQuoteUrl: string;
  QuoteDeclineUrl: string;
  SaveDateRangeUrl: string;
  GetDateFilterByUserIDUrl: string;
  GetServiceCallGraphData: string;
  GetFtCalculatedData: string;
  GetProblemCount: string;
  GetLocationByCustomerGP: string;
  UpdateCustomerRating: string;
  updateFTPlaceCall: string
  UpdateBacklogPriorityURL: string
  placeCall: string;
  GetFtPPAData: string;
  GetOutcomeData: string;
  GetIMDataDrivenTaskTypeData: string;
  GetSiteComfortData: string;
  EquipmentComfortDataURL: string
  GetPerformanceData: string;
  placeCallUrl: string;
  getChartData: string;
  OpenCallData: string;
  getMCCServiceCall: string;
  subContractorUrl: string;
  getjCCServiceCall: string;
  openDebtGraphUrl: string;
  dashboardCount: string;
  loginURL: any;
  challengeURL: string;
  changePasswordURL: string;
  getCustomersUrl: string;
  getLocationByUserID: string;
  getEquipmentsURL: any;
  getContactsUrl: string;
  getLocationUrl: string;
  getMoMListUrl: string;
  createMoMUrl: string;
  bulkReportBaseUrl: string;
  createMoMTaskUrl: string;
  getMoMByIdUrl: string;
  codeDataUrl: string;
  generateMonthlyReportUrl: string;
  generateMainReportUrl: string;
  DocLinkByIDUrl: string;
  bulkDocDownloadUrl: string;
  generateGoToEvaluateUrl: string;
  GetDocumentListUrl: string;
  GetPropertiesByDocIDUrl: string;
  getDocDetailUrl: string;
  getComplianceReportDataUrl: string;
  downloadComplianceDocumentUrl: string;
  TermsConditionPrivacyPolicyUrl: string;
  getTotalEquipments: string;
  SaveClientPerceptionReportUrl: string;
  UpdateEnergyWasteCostAvoidanceKwhUrl: string;
  AddUserNavigationLogUrl: string;

  //MS- One Drive Urls
  getAuthenticationUrl: string;
  getDriveDataUrl: string;
  getFileInfoUrl: string;
  downloadDriveFileUrl: string;
  deleteDriveFileUrl: string;
  createFolderUrl: string;
  uploadFileUrl: string;
  UpdateOutcomeUrl: string;
  UpdateFTLocationPriorityUrl: string
  GetFTLocationPriorityUrl: string
  GetFTAuxiliaryDataUrl: string

  //IAQ Data Urls
  GetIAQBoxWhiskerGraphDataURL: string;
  GetIAQHorizontalBarGraphDataURL: string;
  GetIAQLineGraphDataURL: string;

  constructor(
    //private httpprogress: ProgressHttp,
    private http: HttpClient,
    private simpleHttp: Http,
    private SharedData: SharedDataService
  ) {
    //setting up the api url based on whether it is working on flow url or location host url
    let origin: string = window.location.origin;

    if (origin.includes("localhost") || origin.includes("customerportal")) {
      //  this.baseUrl = "http://localhost:8080/";
      this.baseUrl = this.bulkReportBaseUrl = "https://cp.airmaster.com.au/API/";
      // this.baseUrl = this.bulkReportBaseUrl = "https://aetest.airmaster.com.au/API/";
      // this.baseUrl = this.bulkReportBaseUrl = "https://cp.optimumair.co.nz/api/";
      // this.baseUrl = this.bulkReportBaseUrl = "http://aetest.eudemonic.co.in/api/";

      // this.bulkReportBaseUrl=this.baseUrl;
      // this.oneDriveEndpoint = "http://localhost:51128/1D/";
      this.oneDriveEndpoint = "https://cp.airmaster.com.au/1DAPI/1D/";
      // this.oneDriveEndpoint = "https://flow.optimumair.co.nz/1DAPI/1D/";
    } else {
      if (origin.includes("airmaster.com.au")) {
        this.Client = "FLOW"; //there will be three instances
        this.orgName = "AIR";
      } else if (origin.includes("airmasterfire.com.au")) {
        this.Client = "FLOW";
        this.orgName = "AFS";
      } else if (
        origin.includes("optimumair.co.nz") ||
        origin.includes("controlco.nz")
      ) {
        this.Client = "FLOW";
        this.orgName = "OPT";
      } else if (origin.includes("cp")) {
        this.Client = "CP";
      }
      this.baseUrl = origin + "/API/";
      this.bulkReportBaseUrl = this.baseUrl;
      this.oneDriveEndpoint = origin + "/1DAPI/1D/";
    }
    //this.token = localStorage.getItem("CPToken");
    this.token = this.SharedData.getAccessToken();

    this.headers = new HttpHeaders({ Authorization: "Bearer " + this.token });
    this.QuoteDetailUrl = this.baseUrl + "api/QuoteSummary/GetQuoteSummaryByID";
    this.getAllQuote = this.baseUrl + "api/QuoteSummary/GetQuoteSummaryList";
    this.updateQuoteUrl = this.baseUrl + "api/QuoteSummary/ApproveQuote";
    this.QuoteDeclineUrl = this.baseUrl + "api/QuoteSummary/DeclineQuote";
    this.SaveDateRangeUrl = this.baseUrl + "api/User/SaveDateFilterForUser";
    this.GetDateFilterByUserIDUrl =
      this.baseUrl + "api/User/GetDateFilterByUserID";
    this.GetServiceCallGraphData = this.baseUrl + "api/FacilityTracker/GetServiceCallData";
    this.GetFtCalculatedData = this.baseUrl + "api/FacilityTracker/GetFtCalculatedData";
    this.GetProblemCount = this.baseUrl + "api/FacilityTracker/GetProblemCount";
    this.GetLocationByCustomerGP = this.baseUrl + "api/DocLink/GetLocationByCustomerGP";
    this.UpdateCustomerRating = this.baseUrl + "api/FacilityTracker/UpdateCustomerRating";
    this.updateFTPlaceCall = this.baseUrl + "api/ServiceCall/PlaceCall_FT";
    this.placeCall = this.baseUrl + "api/ServiceCall/PlaceCall";
    this.GetFtPPAData = this.baseUrl + "api/FacilityTracker/GetServiceCallList";
    this.GetOutcomeData = this.baseUrl + "api/FacilityTracker/GetOutcomesData"
    this.GetIMDataDrivenTaskTypeData = this.baseUrl + "api/FacilityTracker/GetAnalyticRuleCount"
    this.GetSiteComfortData = this.baseUrl + "api/FacilityTracker/GetSiteComfortData";
    this.GetPerformanceData = this.baseUrl + "api/FacilityTracker/GetPerformanceData";
    this.getChartData = this.baseUrl + "api/QuoteSummary/GetQuoteSummary";
    this.OpenCallData = this.baseUrl + "api/QuoteSummary/GetTMServiceCalls";
    this.getMCCServiceCall =
      this.baseUrl + "api/QuoteSummary/GetMCCServiceCalls";
    this.getjCCServiceCall =
      this.baseUrl + "api/QuoteSummary/GetJCServiceCalls";
    this.getLocationByUserID = this.baseUrl + "api/Quote/GetLocationByUser";
    this.OpenDebtUrl = this.baseUrl + "api/QuoteSummary/GetOpenDebts";
    this.GetMostExpensiveEquipmentInvoicesUrl =
      this.baseUrl + "api/QuoteSummary/GetMostExpEquipInvoices";
    this.subContractorUrl = this.baseUrl + "api/QuoteSummary/GetSubContractors";
    this.openDebtGraphUrl = this.baseUrl + "api/QuoteSummary/GetOpenDebtGraph";
    this.GetTMMonthGraphuRL = this.baseUrl + "api/QuoteSummary/GetTMMonthGraph";
    this.GetTMMonthGraphByStatusCallsuRL =
      this.baseUrl + "api/QuoteSummary/GetTMMonthGraphByStatusCall";
    this.GetMostExpensiveEquipmentUrl =
      this.baseUrl + "api/QuoteSummary/GetMostExpensiveEquipment";
    this.getCustomersUrl = this.baseUrl + "api/Customer/GetAll";
    this.getContactsUrl = this.baseUrl + "api/Common/GetContacts";
    this.getLocationUrl = this.baseUrl + "api/Common/GetLocations";
    this.createMoMUrl = this.baseUrl + "api/QuoteSummary/AddUpdateMoM";
    this.loginURL = this.baseUrl + "oauth/token";
    this.challengeURL = this.baseUrl + "api/account/Challenge";
    this.changePasswordURL = this.baseUrl + "api/account/ChangePassword";
    this.dashboardCount = this.baseUrl + "api/QuoteSummary/GetCustomerSummary";
    this.getEquipmentsURL = this.baseUrl + "api/Common/GetEquipments";
    this.generateMonthlyReportUrl =
      this.baseUrl + "api/QuoteSummary/GetMonthlyReport";
    this.generateMainReportUrl =
      this.baseUrl + "api/QuoteSummary/GetMainReport";
    this.getMoMListUrl = this.baseUrl + "api/QuoteSummary/GetMoMList";
    this.createMoMTaskUrl = this.baseUrl + "api/QuoteSummary/AddUpdateMoMTask";
    this.getMoMByIdUrl = this.baseUrl + "api/QuoteSummary/GetMoMByMeetingID";
    this.codeDataUrl = this.baseUrl + "api/Common/GetCodeData";
    this.DocLinkByIDUrl = this.baseUrl + "api/DocLink/GetDocumentByType";
    this.generateGoToEvaluateUrl =
      this.baseUrl + "api/CPCommon/GenerateGoToEvaluate";
    this.UpdateEnergyWasteCostAvoidanceKwhUrl = this.baseUrl + "api/FacilityTracker/UpdateEnergyWasteCostAvoidanceKwh";
    this.AddUserNavigationLogUrl = this.baseUrl + "api/User/AddUserNavigationLog";
    //  <doc url>
    this.GetDocumentListUrl =
      this.bulkReportBaseUrl + "api/DocLink/GetDocumentList";
    this.GetPropertiesByDocIDUrl =
      this.bulkReportBaseUrl + "api/DocLink/GetPropertiesByDocID";
    this.getDocDetailUrl =
      this.bulkReportBaseUrl + "api/DocLink/SearchBulkDocuments";
    this.bulkDocDownloadUrl =
      this.bulkReportBaseUrl + "api/DocLink/GetDocumentByID";
    //  <doc url>
    this.getComplianceReportDataUrl =
      this.baseUrl + "api/DocLink/GetComplianceReportStructre";
    this.downloadComplianceDocumentUrl =
      this.baseUrl + "api/DocLink/GetComplianceReport";
    this.placeCallUrl = this.baseUrl + "api/CPCommon/JobRequestEndpoint";
    this.getTotalEquipments =
      this.baseUrl + "api/CPCommon/GetEquipmentCountByUserLocations";
    this.TermsConditionPrivacyPolicyUrl =
      this.baseUrl + "api/CPCommon/GetTermsConditionPrivacyPolicyEndpoints";
    this.SaveClientPerceptionReportUrl =
      this.baseUrl + "api/CPCommon/SaveClientPerceptionReport";

    //MS- One Drive APIs Urls
    this.getAuthenticationUrl = this.oneDriveEndpoint + "Authenticate";
    this.getDriveDataUrl = this.oneDriveEndpoint + "GetDriveData";
    this.getFileInfoUrl = this.oneDriveEndpoint + "GetFileInfo";
    this.downloadDriveFileUrl = this.oneDriveEndpoint + "DownloadFile";
    this.deleteDriveFileUrl = this.oneDriveEndpoint + "DeleteFile";
    this.createFolderUrl = this.oneDriveEndpoint + "CreateFolder";
    this.uploadFileUrl = this.oneDriveEndpoint + "UploadFile";


    this.UpdateOutcomeUrl = this.baseUrl + "api/FacilityTracker/UpdateOutcome";
    this.UpdateFTLocationPriorityUrl = this.baseUrl + "api/FacilityTracker/UpdateFTLocationPriority";
    this.GetFTLocationPriorityUrl = this.baseUrl + "api/FacilityTracker/GetFTLocationPriority";
    this.GetFTAuxiliaryDataUrl = this.baseUrl + "api/FacilityTracker/GetAuxiliaryData";
    this.UpdateBacklogPriorityURL = this.baseUrl + "api/FacilityTracker/UpdateBacklogPriority";
    this.EquipmentComfortDataURL = this.baseUrl + "api/FacilityTracker/EquipmentComfortData";

    //IAQ DATA URLs
    this.GetIAQBoxWhiskerGraphDataURL = this.baseUrl + "api/FacilityTracker/GetIAQBoxWhiskerGraphData";
    this.GetIAQHorizontalBarGraphDataURL = this.baseUrl + "api/FacilityTracker/GetIAQHorizontalBarGraphData";
    this.GetIAQLineGraphDataURL = this.baseUrl + "api/FacilityTracker/GetIAQLineGraphData"
  }

  SaveClientPerceptionReport(payload): Observable<any> {
    return this.http
      .post(this.SaveClientPerceptionReportUrl, payload, {
        headers: this.headers,
      })
      .map((response: HttpResponse<any>) => response);
  }

  createAuthorizationHeader(headers: Headers) {
    var token = this.SharedData.getAccessToken();
    headers.append("Authorization", "Bearer " + token);
  }

  getCustomers(userid): Observable<any> {
    return this.http
      .get(
        this.getCustomersUrl + "?UserID=" + userid + "&Favoriteflag =" + null
      )
      .map((response: HttpResponse<any>) => response);
  }

  getCodeData(): Observable<any> {
    return this.http
      .get(this.codeDataUrl)
      .map((response: HttpResponse<any>) => response);
  }

  getPrivacyPolicyTermCondition(): Observable<any> {
    return this.http
      .get(this.TermsConditionPrivacyPolicyUrl)
      .map((response: HttpResponse<any>) => response);
  }

  getContacts(CustomerID): Observable<any> {
    // let params: URLSearchParams = new URLSearchParams();
    // params.set('CustomerID', CustomerID);
    // params.set('SearchData', SearchString);
    // let headers = new Headers();
    return this.http
      .get(this.getContactsUrl + "?CustomerID=" + CustomerID)
      .map((response: HttpResponse<any>) => response);
  }

  generateReport(payload) {
    let params: URLSearchParams = new URLSearchParams();
    params.append("Customer", payload.Customer);
    params.append("Location", payload.Location);
    params.append("Contact", payload.Contact);
    params.append("State", payload.State);
    params.append("StartDate", payload.StartDate);
    params.append("EndDate", payload.EndDate);
    params.append("AccountManager", payload.AccountManager);
    params.append("FireContract", payload.FireContract);
    params.append("ReportName", payload.ReportName);
    let myheaders = new Headers();
    this.createAuthorizationHeader(myheaders);
    let options = new RequestOptions({
      headers: myheaders,
      params: params,
      responseType: ResponseContentType.Blob,
    });
    return this.simpleHttp
      .get(this.generateMonthlyReportUrl, options)
      .map((resp) => {
        return resp;
      })
      .catch((error) => Observable.throw(error));
  }

  getDocLinkByID(payload): Observable<any> {
    let params: URLSearchParams = new URLSearchParams();
    params.append("DocumentType", payload.DocumentType);
    params.append("PropertyType", payload.PropertyType);
    params.append("PropertyValue", payload.PropertyValue);
    let myheaders = new Headers();
    this.createAuthorizationHeader(myheaders);
    let options = new RequestOptions({
      headers: myheaders,
      params: params,
      responseType: ResponseContentType.Blob,
    });
    //here http rreturns obserbavle which can be handle using map operator and can also handle the error
    //in catch function
    return this.simpleHttp
      .get(this.DocLinkByIDUrl, options)
      .map((resp) => {
        return resp;
      })
      .catch((error) => Observable.throw(error));
  }

  downloadBulkDocuments(payload): Observable<any> {
    var payloadCount = 0;
    payloadCount = payload.split(",").length;
    let data = {
      DocumentIDs: payload,
    };
    let myheaders = new HttpHeaders({
      "Content-Type": "application/json",
    });
    let token = this.SharedData.getAccessToken();
    myheaders.append("Authorization", "Bearer " + token);
    var percentageLoaded = 0;
    return this.http
      .post(this.bulkDocDownloadUrl, data, {
        headers: myheaders,
        reportProgress: true,
        responseType: "blob",
        observe: "events",
      })
      .pipe(
        map((dt) => {
          //if (isHttpProgressEvent(dt)) {
          if (dt["type"] == HttpEventType.DownloadProgress) {
            //this.SharedData.setProgressEvent(Math.round((100 * dt['loaded']) / dt['total']));
            if (payloadCount == 1) {
              if (percentageLoaded < 80) {
                percentageLoaded = percentageLoaded + 20;
              }
            } else if (payloadCount > 1 && payloadCount <= 10) {
              if (percentageLoaded < 98) {
                percentageLoaded = percentageLoaded + 5;
              }
            } else {
              if (percentageLoaded < 99) {
                percentageLoaded = percentageLoaded + 1;
              }
            }
            this.SharedData.setProgressEvent(percentageLoaded);
          }
          if (isHttpResponse(dt)) {
            this.SharedData.setProgressEvent(100);
            return dt;
          }
        }),
        catchError((error) => Observable.throw(error))
      );

    // ------------OLD ------------//
    // return this.httpprogress.withDownloadProgressListener(progress => {
    //     this.progress = progress.percentage;
    //     if (this.progress != undefined) {
    //         this.SharedData.setProgressEvent(progress.percentage);
    //     }
    // })
    //     .post(this.bulkDocDownloadUrl, data, options)
    //     .map(resp => { return resp })
    //     .catch(error => Observable.throw(error))
  }

  generateMainReport(payload) {
    let params: URLSearchParams = new URLSearchParams();
    params.append("AccountManagerID", payload.UserID)
    params.append("QuoteReference", payload.QuoteReference);
    params.append("Revision", payload.Revision);
    params.append("TypeOfQuote", payload.TypeOfQuote);
    params.append("TwelveMonthsWarranty", payload.TwelveMonthsWarranty);
    params.append("ReportName", payload.ReportName);
    params.append("CustNmbr", payload.CustNmbr)
    params.append("HeaderFooter", payload.HeaderFooter)
    let myheaders = new Headers();
    this.createAuthorizationHeader(myheaders);
    let options = new RequestOptions({
      headers: myheaders,
      params: params,
      responseType: ResponseContentType.Blob,
    });
    return this.simpleHttp
      .get(this.generateMainReportUrl, options)
      .map((resp) => {
        return resp;
      })
      .catch((error) => Observable.throw(error));
  }

  getLocationByCustomerID(customerID): Observable<any> {
    return this.http
      .get(this.getLocationUrl + "?CustomerID=" + customerID)
      .map((response: HttpResponse<any>) => response);
  }
  generateGoToEaluateToken(): Observable<any> {
    return this.http
      .get(this.generateGoToEvaluateUrl + '?ExternalLoginFrom="CP"')
      .map((response: HttpResponse<any>) => response);
  }

  getEquipmentDropdownList(locationid: number): Observable<any> {
    return this.http
      .get(this.getEquipmentsURL + "?LocationID=" + locationid)
      .map((response: HttpResponse<any>) => response);
  }
  getPlaceCallUrl(): Observable<any> {
    return this.http
      .get(this.placeCallUrl)
      .map((response: HttpResponse<any>) => response);
  }

  getQuoteDetail(id): Observable<any> {
    // this.coredataservice.QuoteDetailUrl + "=" + id
    return this.http
      .get(this.QuoteDetailUrl + "?QuoteID=" + id)
      .map((response: HttpResponse<any>) => response);
  }
  //mom list
  getMoMList(ids, start: Date, end: Date): Observable<any> {
    let idsaSsTRING = ids.toString().length > 0 ? ids.toString() : null;

    var payload: GetMomListRequest = {
      EndDate:
        end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate(),
      LocationIDs: idsaSsTRING,
      StartDate:
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1) +
        "-" +
        start.getDate(),
    };
    // return this.http.get(this.getMoMListUrl + "?LocationIDs=" + ids + '&startDate=' + start.getFullYear() + "-" + (start.getMonth() + 1) + "-" + start.getDate() + "&endDate=" + end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate())
    //     .map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.getMoMListUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }

  getOutstandingQuotes(
    ids,
    startDate: Date,
    endDate: Date,
    status
  ): Observable<any> {
    let Locations = ids.toString().length > 0 ? ids.toString() : null;

    var payload: GetQuoteSummaryListRequest = {
      LocationIDs: Locations,
      Statuses: status,
      startDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      endDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    // return this.http.get(this.getAllQuote + "?Statuses" + "=" + status + "&LocationIDs=" + idsaSsTRING + '&startDate=' + startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate() + "&endDate=" + endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()
    // ).map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.getAllQuote, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getBulkDocuments(): Observable<any> {
    return this.http
      .get(this.GetDocumentListUrl)
      .map((response: HttpResponse<any>) => response);
  }
  getPropertiesByDocID(ids): Observable<any> {
    return this.http
      .get(this.GetPropertiesByDocIDUrl + "?DocumentTypeID=" + ids)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => Observable.throw(error));
  }
  getDocumentDetails(docData: string): Observable<any> {
    // let params = new HttpParams().set('searchDocument', docData);
    let payload = {
      searchDocument: docData,
    };
    return this.http
      .post(this.getDocDetailUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }
  ApproveQuote(payload, section): Observable<any> {
    return this.http
      .post(this.updateQuoteUrl + "?sectionDetails=" + section, payload, {
        headers: this.headers,
      })
      .map((response: HttpResponse<any>) => response);
  }

  DeclineQuote(declineData): Observable<any> {
    return this.http
      .post(this.QuoteDeclineUrl, declineData, { headers: this.headers })
      .map((response: HttpResponse<any>) => response);
  }

  createMoM(payload): Observable<any> {
    return this.http
      .post(this.createMoMUrl, payload, { headers: this.headers })
      .map((response: HttpResponse<any>) => response);
  }

  createMoMTask(payload): Observable<any> {
    return this.http
      .post(this.createMoMTaskUrl, payload, { headers: this.headers })
      .map((response: HttpResponse<any>) => response);
  }
  getMoMById(meeingID): Observable<any> {
    return this.http
      .get(this.getMoMByIdUrl + "?MeetingID=" + meeingID)
      .map((response: HttpResponse<any>) => response);
  }

  PlaceACall(payload): Observable<any> {
    return this.http
      .post(this.placeCall, payload)
  }

  GetTMMonthGraph(locations, date1: Date): Observable<any> {
    let location =
      locations.toString().length > 0 ? locations.toString() : null;

    var date = new Date();
    var startingDate = new Date(date.getFullYear(), date.getMonth() - 12, 1);
    var endingDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );
    // MS : Previous 13 months staring and ending date
    let StrtDate =
      startingDate.getFullYear() +
      "-" +
      (startingDate.getMonth() + 1) +
      "-" +
      startingDate.getDate();
    let EndDate =
      endingDate.getFullYear() +
      "-" +
      (endingDate.getMonth() + 1) +
      "-" +
      endingDate.getDate();

    var payload1: GetTMMonthGraphRequest = {
      LocationIDs: location,
      StartDate: StrtDate,
      EndDate: EndDate,
    };
    var preStartingDate = new Date(date.getFullYear(), date.getMonth() - 24, 1);
    var preEndingDate = new Date(date.getFullYear(), date.getMonth() - 12);

    // MS : Previous 24 month staring and ending date
    let preStrtDate =
      preStartingDate.getFullYear() +
      "-" +
      (preStartingDate.getMonth() + 1) +
      "-" +
      preStartingDate.getDate();
    let preEndDate =
      preEndingDate.getFullYear() +
      "-" +
      (preEndingDate.getMonth() + 1) +
      "-" +
      preEndingDate.getDate();

    var payload2: GetTMMonthGraphRequest = {
      LocationIDs: location,
      StartDate: preStrtDate,
      EndDate: preEndDate,
    };

    // let response1 = this.http.get(this.GetTMMonthGraphuRL + "?LocationIDs="
    //     + location + '&StartDate=' + StrtDate +
    //     "&EndDate=" + EndDate).map((response: HttpResponse<any>) => response);

    // let response2 = this.http.get(this.GetTMMonthGraphuRL + "?LocationIDs="
    //     + location + '&StartDate=' + preStrtDate +
    //     "&EndDate=" + preEndDate).map((response: HttpResponse<any>) => response);

    let response1 = this.http
      .post(this.getAllQuote, payload1)
      .map((response: HttpResponse<any>) => response);
    let response2 = this.http
      .post(this.getAllQuote, payload2)
      .map((response: HttpResponse<any>) => response);

    return forkJoin([response1, response2]);
  }
  // FK: Get Month Graph By StatusCall
  GetTMMonthGraphByStatusCall(locations): Observable<any> {
    let location =
      locations.toString().length > 0 ? locations.toString() : null;
    let date = new Date();
    let startingDate = new Date(date.getFullYear(), date.getMonth() - 12, 1);
    let endingDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );
    // MS : Previous 13 months staring and ending date
    let StrtDate =
      startingDate.getFullYear() +
      "-" +
      (startingDate.getMonth() + 1) +
      "-" +
      startingDate.getDate();
    let EndDate =
      endingDate.getFullYear() +
      "-" +
      (endingDate.getMonth() + 1) +
      "-" +
      endingDate.getDate();

    var payload1: GetTMMonthGraphRequest2 = {
      LocationIDs: location,
      // StartDate: StrtDate,
      // EndDate: EndDate,
    };

    let preStartingDate = new Date(date.getFullYear(), date.getMonth() - 24, 1);
    let preEndingDate = new Date(date.getFullYear(), date.getMonth() - 12);

    // MS : Previous 24 month staring and ending date
    let preStrtDate =
      preStartingDate.getFullYear() +
      "-" +
      (preStartingDate.getMonth() + 1) +
      "-" +
      preStartingDate.getDate();
    let preEndDate =
      preEndingDate.getFullYear() +
      "-" +
      (preEndingDate.getMonth() + 1) +
      "-" +
      preEndingDate.getDate();

    var payload2: GetTMMonthGraphRequest2 = {
      LocationIDs: location,
      // StartDate: preStrtDate,
      // EndDate: preEndDate,
    };

    // let response1 = this.http.get(this.GetTMMonthGraphByStatusCallsuRL + "?LocationIDs="
    //     + location + '&StartDate=' + StrtDate +
    //     "&EndDate=" + EndDate).map((response: HttpResponse<any>) => response);

    // let response2 = this.http.get(this.GetTMMonthGraphByStatusCallsuRL + "?LocationIDs="
    //     + location + '&StartDate=' + preStrtDate +
    //     "&EndDate=" + preEndDate).map((response: HttpResponse<any>) => response);

    let response1 = this.http
      .post(this.GetTMMonthGraphByStatusCallsuRL, payload1)
      .map((response: HttpResponse<any>) => response);
    // let response2 = this.http
    //   .post(this.GetTMMonthGraphByStatusCallsuRL, payload2)
    //   .map((response: HttpResponse<any>) => response);

    // return forkJoin([response1, response2]);
    return response1;
  }
  // FK: save starting date for filter range
  saveDateRange(date: Date): Observable<any> {
    let startingDate =
      date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    let fromDate = new HttpParams();
    fromDate = fromDate.set("FromDateFilter", startingDate);
    return this.http
      .post(this.SaveDateRangeUrl, fromDate, { headers: this.headers })
      .map((response: HttpResponse<any>) => response);
  }


  getOpenDebtGraph(locations): Observable<any> {
    let idsaSsTRING =
      locations.toString().length > 0 ? locations.toString() : null;
    let payload: GetOpenDebtRequest = {
      LocationIDs: idsaSsTRING,
    };
    //return this.http.get(this.openDebtGraphUrl + "?LocationIDs=" + location).map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.openDebtGraphUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getQuotesSummary(
    locations,
    startDate: Date,
    endDate: Date,
    status
  ): Observable<any> {
    let idsaSsTRING =
      locations.toString().length > 0 ? locations.toString() : null;
    let payload: GetQuoteSummaryRequest = {
      LocationIDs: idsaSsTRING,
      Statuses: status,
      SummaryType: null,
      startDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      endDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };

    // return this.http.get(this.getChartData + "?Statuses=" + status + "&SummaryType=" + quotetype + "&LocationIDs=" + location + '&startDate=' + startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate() + "&endDate=" + endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()
    // ).map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.getChartData, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getPiechartData(
    quotetype,
    locations,
    startDate: Date,
    endDate: Date,
    status
  ): Observable<any> {
    let idsaSsTRING =
      locations.toString().length > 0 ? locations.toString() : null;
    let payload: GetQuoteSummaryRequest = {
      LocationIDs: idsaSsTRING,
      Statuses: status,
      SummaryType: quotetype,
      startDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      endDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };

    // return this.http.get(this.getChartData + "?Statuses=" + status + "&SummaryType=" + quotetype + "&LocationIDs=" + location + '&startDate=' + startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate() + "&endDate=" + endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()
    // ).map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.getChartData, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getDashbaordCount(location, startDate: Date, endDate: Date): Observable<any> {
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: GetCustomerServiceRequest = {
      LocationIDs: idsaSsTRING,
      startDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      endDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    //return this.http.get(this.dashboardCount + "?LocationIDs=" + idsaSsTRING + '&startDate=' + startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate() + "&endDate=" + endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()).map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.dashboardCount, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getBarchartData(
    quotetype: string,
    location,
    startDate: Date,
    endDate: Date,
    status
  ): Observable<any> {
    //let idsaSsTRING = location.toString();
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: GetQuoteSummaryRequest = {
      LocationIDs: idsaSsTRING,
      Statuses: status,
      SummaryType: quotetype,
      startDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      endDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };

    // return this.http.get(this.getChartData + "?Statuses=" + status + "&SummaryType=" + quotetype + "&LocationIDs=" + idsaSsTRING + '&startDate=' + startDate.getFullYear() + "-" + (startDate.getMonth() + 1) + "-" + startDate.getDate() + "&endDate=" + endDate.getFullYear() + "-" + (endDate.getMonth() + 1) + "-" + endDate.getDate()
    // ).map((response: HttpResponse<any>) => response);

    return this.http
      .post(this.getChartData, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getOpenDebtApi(ids): Observable<any> {
    let idsaSsTRING = ids.toString().length > 0 ? ids.toString() : null;
    let payload: GetOpenDebtsRequest = {
      LocationIDs: idsaSsTRING,
      startDate: null,
      endDate: null,
    };
    //return this.http.get(this.OpenDebtUrl + "?LocationIDs=" + ids + '&startDate=' + "&endDate=").map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.OpenDebtUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getMostExpEquipmentInvoices(ServiceCallIDs: string): Observable<any> {
    return this.http
      .get(
        this.GetMostExpensiveEquipmentInvoicesUrl +
        "?ServiceCallIDs=" +
        ServiceCallIDs
      )
      .map((response: HttpResponse<any>) => response);
  }
  getSubContractorApi(ids, start: Date, end: Date): Observable<any> {
    let idsaSsTRING = ids.toString().length > 0 ? ids.toString() : null;
    let payload: GetOpenDebtsRequest = {
      LocationIDs: idsaSsTRING,
      startDate:
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1) +
        "-" +
        start.getDate(),
      endDate:
        end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate(),
    };
    //return this.http.get(this.subContractorUrl + "?LocationIDs=" + ids + '&startDate=' + start.getFullYear() + "-" + (start.getMonth() + 1) + "-" + start.getDate() + "&endDate=" + end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate()).map((response: HttpResponse<any>) => response).catch(this.handleError);
    return this.http
      .post(this.subContractorUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }

  getOpenCallsData(locations, payload): Observable<any> {
    let payload1: GetOpenDebtsRequest = {
      LocationIDs:
        locations.toString().length > 0 ? locations.toString() : null,
      startDate:
        payload.start.getFullYear() +
        "-" +
        (payload.start.getMonth() + 1) +
        "-" +
        payload.start.getDate(),
      endDate:
        payload.end.getFullYear() +
        "-" +
        (payload.end.getMonth() + 1) +
        "-" +
        payload.end.getDate(),
    };
    //return this.http.get(this.OpenCallData + "?LocationIDs=" + locations + '&startDate=' + payload.start.getFullYear() + "-" + (payload.start.getMonth() + 1) + "-" + payload.start.getDate() + "&endDate=" + payload.end.getFullYear() + "-" + (payload.end.getMonth() + 1) + "-" + payload.end.getDate()).map((response: HttpResponse<any>) => response).catch(this.handleError);
    return this.http
      .post(this.OpenCallData, payload1)
      .map((response: HttpResponse<any>) => response);
  }
  GETMCCServiceCalls(ids, start: Date, end: Date): Observable<any> {
    let idsaSsTRING = ids.toString().length > 0 ? ids.toString() : null;
    let payload: GetOpenDebtsRequest = {
      LocationIDs: idsaSsTRING,
      startDate:
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1) +
        "-" +
        start.getDate(),
      endDate:
        end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate(),
    };
    // return this.http.get(this.getMCCServiceCall + "?LocationIDs=" + ids + '&startDate=' + start.getFullYear() + "-" + (start.getMonth() + 1) + "-" + start.getDate() + "&endDate=" + end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate()
    // ).map((response: HttpResponse<any>) => response);

    return this.http
      .post(this.getMCCServiceCall, payload)
      .map((response: HttpResponse<any>) => response);
  }

  GetJCServiceCalls(ids, start: Date, end: Date): Observable<any> {
    let idsaSsTRING = ids.toString().length > 0 ? ids.toString() : null;
    let payload: GetOpenDebtsRequest = {
      LocationIDs: idsaSsTRING,
      startDate:
        start.getFullYear() +
        "-" +
        (start.getMonth() + 1) +
        "-" +
        start.getDate(),
      endDate:
        end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate(),
    };

    // return this.http.get(this.getjCCServiceCall + "?LocationIDs=" + ids + '&startDate=' + start.getFullYear() + "-" + (start.getMonth() + 1) + "-" + start.getDate() + "&endDate=" + end.getFullYear() + "-" + (end.getMonth() + 1) + "-" + end.getDate()
    // ).map((response: HttpResponse<any>) => response);

    return this.http
      .post(this.getjCCServiceCall, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getCustomerLocation(userid): Observable<any> {
    return this.http
      .get(this.getLocationByUserID + "?userID=" + userid)
      .map((response: HttpResponse<any>) => response);
  }
  getcustomerLocationWithTotalEquipments(userid): Observable<any> {
    let responseCustomerLocations = this.http
      .get(this.getLocationByUserID + "?userID=" + userid)
      .map((response: HttpResponse<any>) => response);
    let responseTotalEquipments = this.http
      .get(this.getTotalEquipments + "?userID=" + userid)
      .map((response: HttpResponse<any>) => response);
    return forkJoin([responseCustomerLocations, responseTotalEquipments]);
  }
  getComplianceReportData(): Observable<any> {
    return this.http
      .get(this.getComplianceReportDataUrl)
      .map((response: HttpResponse<any>) => response);
  }
  downloadComplianceDocument(payload): Observable<any> {
    let params: URLSearchParams = new URLSearchParams();
    params.append("documentPath", payload);
    let myheaders = new Headers();
    this.createAuthorizationHeader(myheaders);
    let options = new RequestOptions({
      headers: myheaders,
      params: params,
      responseType: ResponseContentType.Blob,
    });

    return this.simpleHttp
      .get(this.downloadComplianceDocumentUrl, options)
      .map((resp) => {
        return resp;
      })
      .catch((error) => Observable.throw(error));
  }
  getLogin(object: any): Observable<any> {
    object.OTP = object.OTP == null ? "" : object.OTP;
    var data;
    if (this.orgName === undefined) {
      data =
        "grant_type=password&username=" +
        object.loginUsername +
        "&password=" +
        object.password +
        "&OTP=" +
        object.OTP +
        "&Client=" +
        this.Client;
    } else {
      data =
        "grant_type=password&username=" +
        object.loginUsername +
        "&password=" +
        object.password +
        "&OTP=" +
        object.OTP +
        "&Client=" +
        this.Client;

    }
    //here we have extracted the data from the payload object
    return this.http
      .post(this.loginURL, data, {
        headers: new HttpHeaders().set(
          "Content-Type",
          "application/x-www-form-urlencoded"
        ),
      })
      .map((response: HttpResponse<any>) => response);
  }
  SendPasscode(data) {
    // const parameters: HttpParams = new HttpParams().set("userName", userName);
    return this.http
      .post(this.challengeURL, data)
      .map((res) => {
        return res;
      })
      .catch((error) => Observable.throw(error));
  }

  ChangePassword(userName, OTP, new_password) {

    var data;
    data = "userName=" + userName + "&OTP=" + OTP + "&new_password=" + new_password;

    const parameters: HttpParams = new HttpParams()
    parameters.set("userName", userName);
    parameters.set("OTP", OTP);
    parameters.set("new_password", new_password);
    return this.http
      .post(this.changePasswordURL + '?userName=' + userName + '&OTP=' + OTP + '&new_password=' + new_password, { params: parameters })
      .map((res) => {
        return res;
      })
      .catch((error) => Observable.throw(error));
    //.post(this.changePasswordURL,{params:parameters}





  }
  getMostExpensiveEquipment(
    DateFilter: Date,
    LocationIDs: string
  ): Observable<any> {
    let idsaSsTRING =
      LocationIDs.toString().length > 0 ? LocationIDs.toString() : null;
    let payload: GetMostExpensiveEquipmentRequest = {
      LocationIDs: idsaSsTRING,
      DateFilter:
        DateFilter.getFullYear() +
        "-" +
        (DateFilter.getMonth() + 1) +
        "-" +
        DateFilter.getDate(),
    };
    // return this.http.get(this.GetMostExpensiveEquipmentUrl + "?DateFilter=" + DateFilter.getFullYear() + "-" + (DateFilter.getMonth() + 1) + "-" + DateFilter.getDate() + "&LocationIDs=" + LocationIDs)
    //     .map((response: HttpResponse<any>) => response);
    return this.http
      .post(this.GetMostExpensiveEquipmentUrl, payload)
      .map((response: HttpResponse<any>) => response);
  }
  // FK: get starting date for filter by user id
  getFilterDateByUserID() {
    return this.http
      .get(this.GetDateFilterByUserIDUrl)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }

  private handleError(error: Response | any) {
    //  will use a remote logging infrastructure
    let errMsg: string;
    if (error instanceof Response) {
      const body = error.json() || "";
      const err = body.error || JSON.stringify(body);
      errMsg = `${error.status} - ${error.statusText || ""} ${err}`;
      if (error.status == 0) {
        window.alert("Network error. Please contact support");
      }
    } else {
      errMsg = error.message ? error.message : error.toString();
    }
    return Observable.throw(errMsg);
  }
  createStringParams(obj) {
    const params = new URLSearchParams();
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        params.set(key, JSON.stringify(obj[key]));
      }
    }
    return params;
  }
  createStringParamsHttpClient(obj) {
    const params = new HttpParams();
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        params.set(key, JSON.stringify(obj[key]));
      }
    }
    return params;
  }

  // MS - One Drive APIs

  // this.getAuthenticationUrl
  // this.getDriveDataUrl
  // this.getFileInfoUrl
  // this.downloadFileUrl
  // this.createFolderUrl
  // this.uploadFileUrl
  getAuthentication(): Observable<any> {
    return this.http
      .get(this.getAuthenticationUrl)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }

  getDriveData(Id: string, Level: string, UserId: string): Observable<any> {
    let myheaders = new HttpHeaders();
    myheaders = myheaders.append(
      "GraphToken",
      localStorage.getItem("GraphAccessToken")
    );
    let token = this.SharedData.getAccessToken();
    myheaders = myheaders.append("UserToken", token);
    myheaders = myheaders.append("UserID", UserId);

    return this.http
      .get(this.getDriveDataUrl + "?Id=" + Id + "&Level=" + Level, {
        headers: myheaders,
      })
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }
  downloadDriveData(Id: string): Observable<any> {
    let myheaders = new HttpHeaders({
      "Content-Type": "application/json",
    });

    myheaders = myheaders.append(
      "GraphToken",
      localStorage.getItem("GraphAccessToken")
    );

    return this.http
      .get(this.downloadDriveFileUrl + "?Id=" + Id, {
        headers: myheaders,
        responseType: "blob",
        observe: "events",
      })
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }
  deletedDriveData(Id: string): Observable<any> {
    let myheaders = new HttpHeaders({
      "Content-Type": "application/json",
    });

    myheaders = myheaders.append(
      "GraphToken",
      localStorage.getItem("GraphAccessToken")
    );

    return this.http
      .delete(this.deleteDriveFileUrl + "?Id=" + Id, { headers: myheaders })
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }

  uploadDriveData(file: File, parentId, filename): Observable<any> {
    const formData: FormData = new FormData();
    formData.append("Files", file, file.name);
    formData.append("ParentID", "" + parentId + "");

    let myheaders = new HttpHeaders();
    myheaders.append("Content-Type", "multipart/form-data");
    myheaders.append("Accept", "application/json");
    myheaders = myheaders.append(
      "GraphToken",
      localStorage.getItem("GraphAccessToken")
    );

    return this.http
      .put(this.uploadFileUrl, formData, { headers: myheaders })
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }
  createDriveFolder(payload): Observable<any> {
    let myheaders = new HttpHeaders();
    myheaders = myheaders.append(
      "GraphToken",
      localStorage.getItem("GraphAccessToken")
    );

    return this.http
      .post(this.createFolderUrl, payload, { headers: myheaders })
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }

  getServiceCallGraphData(locations): Observable<any> {
    let idsaSsTRING =
      locations.toString().length > 0 ? locations.toString() : null;
    // return this.http
    //   .get(this.GetServiceCallGraphData)
    //   .map((response: HttpResponse<any>) => response)
    //   .catch((error) => this.handleError(error));
    return this.http.get(this.GetServiceCallGraphData + "?LocationIDs=" + idsaSsTRING).map((response: HttpResponse<any>) => response);

  }
  getFtCalculatedData(): Observable<any> {
    return this.http
      .get(this.GetFtCalculatedData)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }
  getProblemCount(status): Observable<any> {
    return this.http
      .get(this.GetProblemCount + "?status=" + status)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }
  getFtPPAData(
    Status,
    location,
    startDate: Date,
    endDate: Date,
  ): Observable<any> {
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: any = {
      Status: Status,
      LocationIDs: idsaSsTRING,
      StartDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      EndDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    // return this.http
    //   .get(this.GetFtPPAData + "?status=" + status)
    //   .map((response: HttpResponse<any>) => response)
    //   .catch((error) => this.handleError(error));
    return this.http
      .post(this.GetFtPPAData, payload)
      .map((response: HttpResponse<any>) => response);
  }
  getSiteComfortData(locations): Observable<any> {
    let idsaSsTRING =
      locations.toString().length > 0 ? locations.toString() : null;
    // return this.http
    //   .get(this.GetSiteComfortData)
    //   .map((response: HttpResponse<any>) => response)
    //   .catch((error) => this.handleError(error));
    return this.http.get(this.GetSiteComfortData + "?LocationID=" + idsaSsTRING).map((response: HttpResponse<any>) => response);

  }


  GetEquipmentComfortData(
    locationIds: any,
    StartDate: Date,
    EndDate: Date
  ): Observable<any> {
    locationIds = locationIds.toString().length > 0 ? locationIds.toString() : null;
    return this.http
      .get(this.EquipmentComfortDataURL + "?locationIDs=" + locationIds + "&StartDate=" + (StartDate.getFullYear() +
        "-" +
        (StartDate.getMonth() + 1) +
        "-" +
        StartDate.getDate()) + "&EndDate=" + (EndDate.getFullYear() +
          "-" +
          (EndDate.getMonth() + 1) +
          "-" +
          EndDate.getDate()))
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }

  getPerformanceData(
    location,
    startDate: Date,
    endDate: Date,
  ): Observable<any> {
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: any = {
      LocationIDs: idsaSsTRING,
      StartDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      EndDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    // return this.http
    //   .get(this.GetPerformanceData)
    //   .map((response: HttpResponse<any>) => response)
    //   .catch((error) => this.handleError(error));
    return this.http
      .post(this.GetPerformanceData, payload)
      .map((response: HttpResponse<any>) => response);
  }


  getIMDataDrivenTaskTypeData(
    Status,
    location,
    startDate: Date,
    endDate: Date,
  ): Observable<any> {
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: any = {
      Status: Status,
      LocationIDs: idsaSsTRING,
      StartDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      EndDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    return this.http
      .post(this.GetIMDataDrivenTaskTypeData, payload)
      .map((response: HttpResponse<any>) => response);
  }

  getLocatationByCustomerGP(CustNmbr): Observable<any> {
    return this.http
      .get(this.GetLocationByCustomerGP + "?CustNmbr=" + CustNmbr)
      .map((response: HttpResponse<any>) => response)
      .catch((error) => this.handleError(error));
  }


  getOutcomeData(
    location,
    startDate: Date,
    endDate: Date,
  ): Observable<any> {
    let idsaSsTRING =
      location.toString().length > 0 ? location.toString() : null;
    let payload: any = {
      LocationIDs: idsaSsTRING,
      StartDate:
        startDate.getFullYear() +
        "-" +
        (startDate.getMonth() + 1) +
        "-" +
        startDate.getDate(),
      EndDate:
        endDate.getFullYear() +
        "-" +
        (endDate.getMonth() + 1) +
        "-" +
        endDate.getDate(),
    };
    return this.http
      .post(this.GetOutcomeData, payload)
      .map((response: HttpResponse<any>) => response);
  }

  updateCustomerRating(Dex_ID, Rating, RatingType, isBacklogProcessedRecord: boolean = false): Observable<any> {
    return this.http
      .get(this.UpdateCustomerRating + "?ID=" + Dex_ID + "&Rating=" + Rating + "&RatingType=" + RatingType + "&isBacklogProcessedRecord=" + isBacklogProcessedRecord)
  }
  GetFTLocationPriority(CUSTNMBR: string, ADRSCODE: string): Observable<any> {
    return this.http
      .get(this.GetFTLocationPriorityUrl + "?CUSTNMBR=" + CUSTNMBR + "&ADRSCODE=" + ADRSCODE)
  }
  GetFTAuxiliaryData(ADRSCODE: string): Observable<any> {
    const encodedADRSCODE = encodeURIComponent(ADRSCODE);
    return this.http
      .get(this.GetFTAuxiliaryDataUrl + "?ADRSCODE=" + encodedADRSCODE)
  }

  updateFTPlaceCallData(FtData): Observable<any> {
    return this.http
      .post(this.updateFTPlaceCall, FtData);
  }
  UpdateBacklogPriority(data): Observable<any> {
    return this.http
      .post(this.UpdateBacklogPriorityURL, data);
  }

  updateOutcome(outcomeData): Observable<any> {
    return this.http
      .post(this.UpdateOutcomeUrl, outcomeData)
  }

  UpdateFTLocationPriority(data: any): Observable<any> {
    return this.http
      .post(this.UpdateFTLocationPriorityUrl, data);
  }

  updateEnergyWasteCostAvoidanceKwh(Dex_ID, EnergyWasteCostAvoidanceKwh, isBacklogProcessedRecord): Observable<any> {
    return this.http
      .get(this.UpdateEnergyWasteCostAvoidanceKwhUrl + "?ID=" + Dex_ID + "&EnergyWasteCostAvoidanceKwh=" + EnergyWasteCostAvoidanceKwh + "&isBacklogProcessedRecord=" + isBacklogProcessedRecord)
  }

  AddUserNavigationLog(logData: any): Observable<any> {
    return this.http
      .post(this.AddUserNavigationLogUrl, logData, {
        headers: new HttpHeaders({
          'X-Fire-And-Forget': 'true'
        })
      });
  }

  GetIAQBoxWhiskerGraphData(LocationIDs: string[], MetricColumn: string): Observable<any> {
    let locationIdsAsString: string = 
          LocationIDs.toString().length > 0 ? LocationIDs.toString() : null;

    let payload: any = {
      LocationIDs: locationIdsAsString,
      MetricColumn: MetricColumn
    };

    return this.http
      .post(this.GetIAQBoxWhiskerGraphDataURL, payload)
      .map((response: HttpResponse<any>) => response);
  }
  
  GetIAQHorizontalBarGraphData(LocationIDs: string[], MetricColumn: string): Observable<any> {
    let locationIdsAsString: string = 
          LocationIDs.toString().length > 0 ? LocationIDs.toString() : null;

    let payload: any = {
      LocationIDs: locationIdsAsString,
      MetricColumn: MetricColumn
    };

    return this.http
      .post(this.GetIAQHorizontalBarGraphDataURL, payload)
      .map((response: HttpResponse<any>) => response);
  }

  GetIAQLineGraphData(LocationIDs: string[], MetricColumn: string): Observable<any> {
    let locationIdsAsString: string = 
          LocationIDs.toString().length > 0 ? LocationIDs.toString() : null;

    let payload: any = {
      LocationIDs: locationIdsAsString,
      MetricColumn: MetricColumn
    };

    return this.http
      .post(this.GetIAQLineGraphDataURL, payload)
      .map((response: HttpResponse<any>) => response);
  }
}
