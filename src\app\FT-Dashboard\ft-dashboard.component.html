<admin-header (valueChange)='locationChange($event)' (equipmentsData)='getDataOnEquipmentFilter($event)'></admin-header>
<app-side-nav></app-side-nav>
<section class="dashboard-header section-padding section-top-dashboard"
    [style]="collapsedSections.assetPerformance && collapsedSections.workflow && collapsedSections.backlog && collapsedSections.tuning
      && collapsedSections.awaitingApproval ? 'background-color: #F5F3EF;' : 'height: auto; background-color: #F5F3EF;'">
  <div class="container-fluid" style="padding: 0 1rem">
    <ng-container *ngFor="let panel of panels">
      <div *ngIf="panel.key !== 'tuning' || (panel.key === 'tuning' && isTuningModuleActive)" class="panel-container">
        <button kendoButton look="outline" class="panel-btn"
                    [style]="collapsedSections[panel.key] ? 'border-radius: 6px' : 'border-bottom: 0'"
                    (click)="toggleCollapse(panel.key)">
                    <span>{{ panel.title }}</span>
                    <label class="switch">
                      <input type="checkbox" [checked]="!collapsedSections[panel.key]" (change)="toggleCollapse(panel.key)"/>
                      <span class="slider"></span>
                    </label>
        </button>

        <div [@expandCollapse]="collapsedSections[panel.key] ? 'collapsed' : 'expanded'" class="collapse-panel">
          <ng-container *ngIf="panel.key === 'assetPerformance'">
            <div class="row align-items-md-stretch section_1">

              <!-- <div class="col-md-4 service_call_data">
                <div class="card bar-chart-example screenScroll service_call">
                  <div class="service_call_content">
                    <h3 style="font-weight: 400; color: #888; font-size: 20px !important;">Service Calls</h3>
                    <kendo-chart [seriesColors]="['rgba(0,17,50)','rgb(125,221,255)']" style="height: 370px;">
                      <kendo-chart-value-axis>
                        <kendo-chart-value-axis-item  [majorUnit]="serviceCallMarginUnit" [majorGridLines]="{ visible: true }" [minorTicks]="{ visible: false }">
                        </kendo-chart-value-axis-item>
                      </kendo-chart-value-axis>
                      <kendo-chart-category-axis>
                        <kendo-chart-category-axis-item [majorGridLines]="{ visible: false }"
                          [categories]="ServiceCallCategory" >
                          <kendo-chart-category-axis-item-labels [rotation]="-30" >
                          </kendo-chart-category-axis-item-labels>
                        </kendo-chart-category-axis-item>
                      </kendo-chart-category-axis>
                      <kendo-chart-legend position="bottom" orientation="horizontal" [markers]="legendMarker">
                      </kendo-chart-legend>
                      <kendo-chart-series>
                        <kendo-chart-series-item *ngFor="let item of ServiceCallGraphData" type="area"
                          [data]="item.data" [name]="item.name" [markers]="{ visible: false }" style="stroke: none !important;" [opacity]="0.5" [line]="{width: 3}">
                          <kendo-chart-series-item-tooltip>
                            <ng-template let-value="value" let-category="category" let-name="name">
                              {{category}}: {{value }}
                            </ng-template>
                          </kendo-chart-series-item-tooltip>
                        </kendo-chart-series-item>
                      </kendo-chart-series>
                    </kendo-chart>
                  </div>
                </div>
              </div> -->
              <div class="col-md-12 total-problems col-12" style="display: flex; flex-direction: column;">
                <div class="problem-addressed" style="border: 1px solid #b3b3b3; padding: 20px !important;">
                  <div style="position: relative; height: 100%;">
                    <div style="display: flex;justify-content: space-between;">
                      <h3 style="    font-weight: bold;
                      color: black;
                      font-family:Roboto !important;
                      font-size: 1.2vw;
                      margin-left: 0.3vw;">IM Data-Driven Task Summary</h3>
                      <div style="display: flex;justify-content: space-between;width: 30vw; margin-right: 0.3vw;">
                        <button class="k-button" style="border-radius: 7px;
                          text-transform: capitalize;
                          font-family: Calibri Light !important;
                          font-size: 1vw;
                          line-height: 1.5;
                          box-shadow: none;
                          border: 1px solid #888888;" routerLink="/MonthlyIMReport">Report Generator</button>
                        <button class="k-button" style="border-radius: 7px;
                          text-transform: capitalize;
                          font-family: Calibri Light !important;
                          font-size: 1vw;
                          line-height: 1.5;
                          box-shadow: none;
                          border: 1px solid #888888;" (click)="openPrioritization()">Priority & Focus</button>
                        <button class="k-button" style="border-radius: 7px;
                          text-transform: capitalize;
                          font-family: Calibri Light !important;
                          font-size: 1vw;
                          line-height: 1.5;
                          box-shadow: none;
                          border: 1px solid #888888;" (click)="openEquipments()">Equipment</button>
                      </div>
                    </div>
                    <div class="col-md-12 main-widget" style="font-family: Calibri Light !important;">
                      <div class="col-md-2"
                        style="display: flex; align-items: center; justify-content: center; padding-left: 0; padding-right: 0;">
                        <div class="col-md-8 widgets" [class.IM-task-element]="IMTaskQTyStyling" (click)="toggleIMTaskQty()">
                          <h2
                            style="text-decoration: underline; text-underline-offset: 0.1em; text-decoration-thickness: 2px; font-family: Roboto !important;">
                            {{(ImTaskQtyDataCount + getAwaitingListCount.length) || 0}}</h2>
                          <span>Completed IM </span>
                          <span>Tasks</span>


                        </div>

                        <div class="col-md-4" style="padding-left: 0; padding-right: 0;">
                          <i class="fa fa-caret-left" aria-hidden="true"
                            style="color: #f2f2f2; font-size: 150px; height: 100px;"></i>
                        </div>
                      </div>
                      <div class="col-md-10">
                        <div class="col-md-12 main-widget">
                          <div class="col-md-2 widgets" [class.selected]="resolved.isSelected"
                            (click)="toggleSelected(resolved)">
                            <h2 style="font-family: Roboto !important;">{{resolvedOutcomeDataCount || 0}}</h2>
                            <span>Resolved</span>
                          </div>
                          <div class="col-md-2 widgets" [class.selected]="improved.isSelected"
                            (click)="toggleSelected(improved)">
                            <h2 style="font-family: Roboto !important;">{{improvedOutcomeDataCount || 0}}</h2>
                            <span>Improved</span>
                          </div>
                          <div class="col-md-2 widgets" [class.selected]="escalated.isSelected"
                            (click)="toggleSelected(escalated)">
                            <h2 style="font-family: Roboto !important;">{{escalatedOutcomeDataCount || 0}}</h2>
                            <span>Escalated</span>
                          </div>
                          <div class="col-md-2 widgets" [class.selected]="noAccess.isSelected"
                            (click)="toggleSelected(noAccess)">
                            <h2 style="font-family: Roboto !important;">{{NoAccessOutcomeDataCount || 0}}</h2>
                            <span>No Access</span>
                          </div>
                          <div class="col-md-2 widgets quote-req" [class.selected]="quotesRequired.isSelected"
                            (click)="toggleSelected(quotesRequired)">
                            <h2 style="font-family: Roboto !important;">{{quoteRequiredOutcomeDataCount || 0}}</h2>
                            <span>Quotes Required</span>
                          </div>
                        </div>

                        <div class="col-md-12 main-widget" style="margin-top: 10px; margin-bottom: 10px;">
                          <div class="col-md-2 widgets" style="margin-top: 0;" [class.selected]="awaitingApproval.isSelected"
                            (click)="toggleSelected(awaitingApproval)">
                            <h2 style="font-family: Roboto !important;">{{(AwaitingApprovalDataCount + getAwaitingListCount.length) || 0}}</h2>
                            <span>Quotes</span>
                            <span>Awaiting Approval</span>
                          </div>
                          <div class="col-md-2 widgets" style="margin-top: 0;" [class.selected]="quotesAccepted.isSelected"
                            (click)="toggleSelected(quotesAccepted)">
                            <h2 style="font-family: Roboto !important;">{{WonOutcomeDataCount || 0}}</h2>
                            <span>Quotes</span>
                            <span>Accepted</span>
                          </div>
                          <div class="col-md-2 widgets" style="margin-top: 0;" [class.selected]="quotesExpired.isSelected"
                            (click)="toggleSelected(quotesExpired)">
                            <h2 style="font-family: Roboto !important;">{{ExpiredOutcomeDataCount || 0}}</h2>
                            <span>Quotes</span>
                            <span>Expired</span>
                          </div>
                          <div class="col-md-2 widgets quote-not-accepted" style="margin-top: 0;"
                            [class.selected]="quotesNotAccepted.isSelected" (click)="toggleSelected(quotesNotAccepted)">
                            <h2 style="font-family: Roboto !important;">{{LostOutcomeDataCount || 0}}</h2>
                            <span>Quotes Not</span>
                            <span>Accepted</span>
                          </div>
                          <div class="col-md-2 widgets quote-req" style="margin-top: 0;"
                            [class.selected]="openIMTasks.isSelected" (click)="toggleSelected(openIMTasks)">
                            <h2 style="font-family: Roboto !important;">
                              {{FT2_Total || 0}}</h2>
                            <span>Open IM </span>
                            <span>Tasks</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>

                      <kendo-chart style="width: 100%; height: 100px"
                        [seriesColors]="['rgb(0, 17, 50)','rgb(13, 192, 255)','rgb(137, 224, 255)']">
                        <kendo-chart-value-axis>
                          <kendo-chart-value-axis-item [majorUnit]="1" [labels]="{visible: false}"
                            [majorGridLines]="{ visible: false }" [minorGridLines]="{ visible: false }"
                            [minorTicks]="{ visible: false }" [line]="{visible:false}">
                          </kendo-chart-value-axis-item>
                        </kendo-chart-value-axis>

                        <kendo-chart-axis-defaults [line]="{visible: false}">
                        </kendo-chart-axis-defaults>

                        <kendo-chart-category-axis>
                          <kendo-chart-category-axis-item [majorGridLines]="{ visible: false }"
                            [minorGridLines]="{ visible: false }">
                            <kendo-chart-category-axis-item-labels [visible]="false">
                            </kendo-chart-category-axis-item-labels>
                          </kendo-chart-category-axis-item>
                        </kendo-chart-category-axis>
                        <kendo-chart-legend position="left" orientation="vertical" [markers]="legendMarker"
                          [labels]="{color: '#888888', font: '13px Calibri Light !important'}">
                        </kendo-chart-legend>
                        <kendo-chart-series>
                          <kendo-chart-series-item *ngFor="let d of TotalProblemAddressedData" type="bar"
                            [stack]="{type: '100%'}" [data]="[d.value]" [name]="d.name" [labels]="seriesLabelsForBarGraph">
                            <kendo-chart-series-item-tooltip>
                              <ng-template let-value="value" let-category="name" let-name="name">
                                {{value }}
                              </ng-template>
                            </kendo-chart-series-item-tooltip>
                          </kendo-chart-series-item>
                        </kendo-chart-series>
                      </kendo-chart>

                    </div>
                  </div>
                </div>
                <!-- <div style="height: 141px !important; margin-top: 10px;">
                  <div class="energy_waste">
                    <h2 class="energy_waste_heading">{{Co2Reduction}} {{Co2ReductionUnit}}</h2>
                    <span class="desc energy_waste_content">Avoided Carbon Emissions</span>
                  </div>
                </div> -->
              </div>




              <!-- <div class="col-md-3 p-0 realised">
                <div class="col-md-12 realised_data">
                  <div class="col-md-12 total-cost col-12" style="padding: 0;">
                    <div class="problems">
                      <div class="realised_energy">
                        <div class="digit">
                          <h2 class="realised_energy_heading" data-title="This is dummy text">
                            {{TotalRealisedEnergyWaste ?
                            (TotalRealisedEnergyWaste | currency : 'USD' : 'symbol' : '1.0-0') : (0 | currency : 'USD' :
                            'symbol'
                            :
                            '1.0-0')}}</h2>
                        </div>
                        <div class="digit1">
                          <span class="realised_energy_content"> Energy Waste Cost Avoided</span>
                        </div>
                      </div>
                      <div class="unrealised_energy">
                        <div class="digit">
                          <h2 class="unrealised_energy_heading" data-title="This is dummy text">
                            {{TotalUnRealisedEnergyWaste ?
                            (TotalUnRealisedEnergyWaste | currency : 'USD' : 'symbol' : '1.0-0') : (0 | currency : 'USD' :
                            'symbol'
                            :
                            '1.0-0')}}</h2>
                        </div>
                        <div class="digit1">
                          <span class="unrealised_energy_content">Unrealised Energy Waste Cost Avoidance</span>
                        </div>
                      </div>
                    </div>

                    <div class="problems">
                      <div class="equipment_reliability">
                        <div class="digit">
                          <h2 class="equipment_reliability_heading">
                            {{EquipmentReliabilityCount ? (EquipmentReliabilityCount | number:'1.0-0') : ( 0 | number:'1.0-0')}}
                          </h2>
                        </div>
                        <div class="digit1">
                          <span class="desc equipment_reliability_content">Energy Waste Avoided - kWh
                          </span>
                        </div>
                      </div>
                      <div class="thermal_comfort">
                        <div class="digit">
                          <h2 class="thermal_comfort_heading">
                            {{ThermalComfortCount ? (ThermalComfortCount | number:'1.0-0') : ( 0 | number:'1.0-0')}}</h2>
                        </div>
                        <div class="digit1">
                          <span class="desc thermal_comfort_content">Unrealised Energy Waste Avoidance-kWh
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="col-md-12 realised_data" style="margin-top: 10px">
                  <div class="col-md-12 total-cost col-12" style="padding: 0;">


                    <div class="problems">
                      <div class="realised_energy">
                        <div class="digit">
                          <h2 class="realised_energy_heading" style="color: rgba(175,171,171);">
                            {{ ReductionInServiceCalls | number}}%</h2>
                        </div>
                        <div class="digit1">
                          <span class="realised_energy_content" style="color: rgba(175,171,171);"> Reduction in <br>Service Calls</span>
                        </div>
                      </div>
                      <div class="unrealised_energy">
                        <div class="digit">
                          <h2 class="unrealised_energy_heading" style="color: rgba(175,171,171);">
                            {{lessServiceCallCount}}</h2>
                        </div>
                        <div class="digit1">
                          <span class="unrealised_energy_content" style="color: rgba(175,171,171);">Less Service <br>Calls</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

              </div> -->

            </div>

            <div  class="row align-items-md-stretch section_1">
              <div class="col-md-12 HVAC_data">
                <div class="card bar-chart-example screenScroll"
                  style="border: 1px solid #b3b3b3; position: relative; height: 100%;">
                  <p
                    style="font-size: 1.2vw;font-family: 'Roboto'; font-weight: bold; position: absolute; left: 2%; color: black !important;">
                    HVAC
                    Asset Performance Score</p>
                  <div class="HVAC-main-div" style="margin: 40px 0 0 0;">
                    <h4 *ngIf="HVACAssetPerformanceScore == 0" style="color: #888;
                    text-align: center;
                    font-size: 30px;
                    margin-top: 13%;">Not Available</h4>

                    <div style="position:relative;" *ngIf="HVACAssetPerformanceScore">
                      <kendo-radialgauge [pointer]="[]" class="radial-chart">
                        <kendo-radialgauge-scale [majorTicks]="{ visible: false, color: ticksColor }"
                          [minorTicks]="{ visible: false, color: ticksColor }" [startAngle]="startAngle" [endAngle]="endAngle"
                          [rangeSize]="rangeSize" [min]="0" [max]="101" [labels]="labels" [majorUnit]="1" [rangeDistance]="-7">
                          <kendo-radialgauge-scale-ranges>
                            <kendo-radialgauge-scale-range *ngFor="let data of HVACColors" [from]="data.from" [to]="data.to"
                              [color]="data.color"> </kendo-radialgauge-scale-range>
                          </kendo-radialgauge-scale-ranges>
                        </kendo-radialgauge-scale>
                      </kendo-radialgauge>
                      <div class="HVAC-count">
                        <div style="text-align: center; font-weight: 600; font-family: Roboto;">
                          <span style="font-size: 1.7vw; color: #545454;" [attr.data-guage-title]="HVACAssetPerformanceScore">{{
                            HVACAssetPerformanceScore }} <span style="font-size: 0.9vw;">/ 100</span> </span>
                          <br>
                          <span style="font-size: 1.4vw; color: #545454;"
                            [attr.data-guage-title]="HVACStatus">{{HVACStatus}}</span>
                        </div>
                      </div>
                    </div>

                    <p *ngIf="HVACAssetPerformanceScore && target > 0"
                      style="font-size: 0.9vw; position: absolute; left: 5%; bottom: 5px; color: #212529; font-family: Roboto;">
                      <span style="font-weight: bolder;">
                        {{target}}
                      </span>
                      =
                      <span style="color: #888888; font-family: Calibri Light !important">
                        Original Score
                      </span>
                    </p>
                    <p *ngIf="HVACAssetPerformanceScore"
                      style="font-size: 0.9vw; position: absolute; right: 5%; bottom: 5px; color: #888888; font-family: Calibri Light;">
                      <span style="font-weight: bolder; color: #212529; font-family: Roboto !important;">
                        {{HitsPerAssetOccurences | number:'1.0-2'}}
                      </span>
                      Occurrences of
                      <span style="font-weight: bolder; color: #212529; font-family: Roboto !important;">
                        {{HitsPerAssetRatio | number:'1.0-2'}}
                      </span>
                      Issues/Equip Avg
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="row align-items-md-stretch section_1">
              <div class="col-md-12">
                <div class="card bar-chart-example screenScroll" style="border: 1px solid #b3b3b3;">
                  <h3 style="    font-weight: bold;
                      color: black;
                      font-family:Roboto !important;
                      font-size: 1.2vw;
                      margin-left: 0.3vw;">Maintenance Outcomes</h3>

                  <div class="row d-flex align-items-md-stretch section_1"
                    style="flex-wrap: wrap; font-family: Calibri Light !important;">
                    <div class="col-md-4 col-12 card-div" style="padding-right: 0;">
                      <div class="card bar-chart-example screenScroll "
                        style="border: 1px solid #b3b3b3; height: 100%;">
                        <div class="ag-courses_item">
                          <a class="ag-courses-item_link">
                            <div class="ag-courses-item_title" style="color: #7dddff;">
                              {{Co2Reduction | number:'1.0-2'}}
                            </div>
                            <div class="ag-courses-item_date-box">
                              <span class="ag-courses-item_date">
                                Avoided Carbon <br> Emissions -{{Co2ReductionUnit}}
                              </span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 col-12 card-div" style="padding-right: 0;">
                      <div class="card bar-chart-example screenScroll " style="border: 1px solid #b3b3b3;">
                        <div class="ag-courses_item">
                          <a class="ag-courses-item_link">
                            <div class="ag-courses-item_title" style="color: #7dddff;">
                              {{EquipmentReliabilityCount ? (EquipmentReliabilityCount | number:'1.0-0') : ( 0 | number:'1.0-0')}}
                            </div>
                            <div class="ag-courses-item_date-box">
                              <span class="ag-courses-item_date">
                                Energy Waste Avoided -<br> kWh
                              </span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 col-12 card-div" style="padding-right: 0;">
                      <div class="card bar-chart-example screenScroll " style="border: 1px solid #b3b3b3;">
                        <div class="ag-courses_item">
                          <a class="ag-courses-item_link">
                            <div class="ag-courses-item_title" style="color: #7dddff !important;">
                              {{TotalRealisedEnergyWaste ?
                              (TotalRealisedEnergyWaste | currency : 'USD' : 'symbol' : '1.0-0') : (0 | currency : 'USD' :
                              'symbol'
                              :
                              '1.0-0')}}
                            </div>
                            <div class="ag-courses-item_date-box">
                              <span class="ag-courses-item_date">
                                Energy Waste <br> Cost Avoided
                              </span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 col-12 card-div" style="padding-right: 0;">
                      <div class="card bar-chart-example screenScroll " style="border: 1px solid #b3b3b3;">
                        <div class="ag-courses_item">
                          <a class="ag-courses-item_link">
                            <div class="ag-courses-item_title" style="color: #545454;">
                              {{ThermalComfortCount ? (ThermalComfortCount | number:'1.0-0') : ( 0 | number:'1.0-0')}}
                            </div>
                            <div class="ag-courses-item_date-box">
                              <span class="ag-courses-item_date">
                                Unrealised Energy Waste Avoidance-kWh
                              </span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-4 col-12 card-div" style="padding-right: 0;">
                      <div class="card bar-chart-example screenScroll " style="border: 1px solid #b3b3b3;">
                        <div class="ag-courses_item">
                          <a class="ag-courses-item_link">
                            <div class="ag-courses-item_title" style="color: #545454;">
                              {{TotalUnRealisedEnergyWaste ?
                              (TotalUnRealisedEnergyWaste | currency : 'USD' : 'symbol' : '1.0-0') : (0 | currency : 'USD' :
                              'symbol'
                              :
                              '1.0-0')}}
                            </div>
                            <div class="ag-courses-item_date-box">
                              <span class="ag-courses-item_date">
                                Unrealised Energy Waste Cost Avoidance
                              </span>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row align-items-md-stretch section_2">

                    <div class="col-md-9 service_call_data">
                      <div class="card bar-chart-example screenScroll service_call"
                        style="height: 400px !important; padding: 28px !important;">
                        <div class="service_call_content">
                          <h3 style="font-weight: bold;
                          color: black;
                          font-family: Roboto;
                          margin-left: 0px;
                          font-size: 1.2vw;
                          margin-top: -1vh;
                          margin-bottom: 1.2vh;">Service Call Trend</h3>
                          <kendo-chart [seriesColors]="['rgba(0,17,50)','rgb(125,221,255)']" style="height: 350px;">
                            <kendo-chart-value-axis>
                              <kendo-chart-value-axis-item [majorUnit]="serviceCallMarginUnit" [majorGridLines]="{ visible: true }"
                                [minorTicks]="{ visible: false }" [labels]="{font: '13px Calibri Light !important'}">
                              </kendo-chart-value-axis-item>
                            </kendo-chart-value-axis>
                            <kendo-chart-category-axis>
                              <kendo-chart-category-axis-item [majorGridLines]="{ visible: false }" [categories]="ServiceCallCategory"
                                [labels]="{font: '13px Calibri Light !important', rotation: -30 }">
                                <!-- <kendo-chart-category-axis-item-labels [rotation]="-30">
                                </kendo-chart-category-axis-item-labels> -->
                              </kendo-chart-category-axis-item>
                            </kendo-chart-category-axis>
                            <kendo-chart-legend position="bottom" orientation="horizontal" [markers]="legendMarker"
                              [labels]="{color: '#888888', font: '13px Calibri Light !important'}">
                            </kendo-chart-legend>
                            <kendo-chart-series>
                              <kendo-chart-series-item *ngFor="let item of ServiceCallGraphData" type="area" [data]="item.data"
                                [name]="item.name" [markers]="{ visible: false }" style="stroke: none !important;" [opacity]="0.5"
                                [line]="{width: 3}">
                                <kendo-chart-series-item-tooltip>
                                  <ng-template let-value="value" let-category="category" let-name="name">
                                    {{category}}: {{value }}
                                  </ng-template>
                                </kendo-chart-series-item-tooltip>
                              </kendo-chart-series-item>
                            </kendo-chart-series>
                          </kendo-chart>
                        </div>

                      </div>
                    </div>

                    <div class="col-md-3" style="margin-top: 8vh;">
                      <div class="col-md-10 col-12" style="padding-right: 0; margin-bottom: 15px;">
                        <div class="card bar-chart-example screenScroll " style="border: 1px solid #b3b3b3;">
                            <div class="ag-courses_item">
                                <a class="ag-courses-item_link">
                                  <div class="ag-courses-item_title" style="color: rgba(175,171,171);">
                                    {{ ReductionInServiceCalls | number}}%
                                  </div>
                                  <div class="ag-courses-item_date-box">
                                    <span class="ag-courses-item_date">
                                      Reduction in <br>Service Calls
                                    </span>
                                  </div>
                                </a>
                              </div>
                            </div>
                      </div>
                      <div class="col-md-10 col-12" style="padding-right: 0;">
                            <div class="card bar-chart-example screenScroll "
                              style="border: 1px solid #b3b3b3; height: 100%;">
                              <div class="ag-courses_item">
                                <a class="ag-courses-item_link">
                                  <div class="ag-courses-item_title" style="color: rgba(175,171,171);">
                                    {{lessServiceCallCount}}
                                  </div>
                                  <div class="ag-courses-item_date-box">
                                    <span class="ag-courses-item_date">
                                      Less Service <br>Calls
                                    </span>
                                  </div>
                                </a>
                              </div>
                            </div>
                      </div>
                    </div>

                  </div>
                </div>
              </div>
            </div>

          </ng-container>

          <ng-container *ngIf="panel.key === 'workflow'">
              <div class="row align-items-md-stretch section_3">

                <div class="col-md-12 problem_proactively" style="border-radius: 0;">
                  <div class="card bar-chart-example"
                      style="border-radius: 7px;">
                    <div style="height: 450px; margin-top: 2vh;">
                      <h3 style="font-weight: bold;
                                        color: black;
                                        margin-left: 0px;
                                        font-family:Roboto;
                                        font-size: 1.2vw;
                                        margin-top: -1vh;
                            margin-bottom: 1.2vh;">Completed/Open Data-Driven Task Types</h3>
                      <div style="display: flex; align-items: center;">
                      <div id="container" style="width: 100%"></div>
                    </div>
                  </div>
                </div>
              </div>
              </div>
            <div class="row align-items-md-stretch section_3" id="completedTable" style="margin-top: 2vh; ">
              <div class="col-md-12 problem_proactively" style="border-radius: 0; padding: 0 0 0 15px">
                <div class="card bar-chart-example" style="border-radius: 7px;">
                  <div class="table_div" >
                    <div class="mt-3 data-table" style="height: 550px; margin-top: 0 !important;">
                      <kendo-grid style="height: 100%;" [data]="gridDataForIMCompletedList"
                        class="gridFontStyle scroll-container" [sortable]="{allowUnsort: true, mode:'multiple'}"
                        [skip]="IMDataDrivenTCompleteListState.skip" [sort]="IMDataDrivenTCompleteListState.sort"
                        [filter]="IMDataDrivenTCompleteListState.filter" filterable="menu" [resizable]="true"
                        (dataStateChange)="dataStateChangeCompleted($event)" [navigable]="true">
                        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
                          <div class="container-fluid p-0">
                            <div class="row">
                              <div class="col-md-5 col-5">
                                <h3 style="color: black;
                                font-size: 1.2vw;
                                font-family: 'Roboto';
                                font-weight: bold !important;
                                margin-left: -8px;
                                margin-top: -1.4vh;">Data-Driven Tasks Workflow</h3>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                        <kendo-grid-column field="Building" title="Building" width="160" [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div [ngStyle]="{
                                'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                              }" style="font-family: Calibri Light !important;">
                              {{ dataItem?.Building }}
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="buildingFilterIMCompleteList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="ServiceCallID" title="Service Call ID" width="170" [minResizableWidth]="170">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div [ngStyle]="{
                                'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                              }" style="font-family: Calibri Light !important;">
                              {{ dataItem?.ServiceCallID }}
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="Equipment" title="Equipment" width="160" [minResizableWidth]="160">
                          <!-- <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                            let-filterService="filterService">
                                            <kendo-grid-string-filter-menu
                                [column]="column"
                                [filter]="filter"
                                [filterService]="filterService"
                                operator="contains"
                            >
                            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                            <kendo-filter-eq-operator></kendo-filter-eq-operator>
                            <kendo-filter-contains-operator></kendo-filter-contains-operator>
                            <kendo-filter-neq-operator></kendo-filter-neq-operator>
                            <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field"
                                              [currentFilter]="filter" [filterService]="filterService" textField="value"
                                              valueField="code" [data]="equipmentFilterIMCompleteList"></dropdownlist-filter>
                                        </ng-template> -->
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <a style="font-family: Calibri Light !important; font-weight: bold; text-decoration: underline;"
                              (click)="getUrl(dataItem)"
                              [ngStyle]="{'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'}">{{dataItem?.Equipment}}</a>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="equipmentFilterIMCompleteList">
                            </multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="Notes" title="Task Type & Trending Data" width="200"
                          [minResizableWidth]="200">

                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopupTaskDescription(dataItem?.TaskDescription)" [ngStyle]="{
                              'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                            }" style="font-family: Calibri Light !important;">
                              {{ splitTextWithIcon(dataItem?.Notes).textWithoutLastWord }}
                              <span>{{ splitTextWithIcon(dataItem?.Notes).lastWord }}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;margin-left: 5px;"
                                  [ngStyle]="{'color':dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '#00ccff'}"></i></span>
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="taskTypeFilterIMCompleteList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="ThermalComfort" title="Thermal Comfort" width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="thermal-background" style="font-family: Calibri Light !important;"
                              [ngClass]="'thermal-' + dataItem.ThermalComfort" [ngStyle]="{'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '',
                                        'backgroundColor': dataItem.IsLastHitGreater === 1 ? '#dfdfdf' : ''}">
                              <!-- {{dataItem?.ThermalComfort}} -->
                              {{dataItem?.ThermalComfort >= 0 && dataItem?.ThermalComfort <= 3 ? 'Minor' :
                                (dataItem?.ThermalComfort>= 4 && dataItem?.ThermalComfort <= 6 ? 'Moderate' :
                                  (dataItem?.ThermalComfort>= 7 && dataItem?.ThermalComfort <= 10 ? 'Severe' : '' ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="EquipmentReliability" title="Equipment Reliability" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="thermal-background" style="font-family: Calibri Light !important;"
                              [ngClass]="'thermal-' + dataItem.EquipmentReliability" [ngStyle]="{'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '',
                              'backgroundColor': dataItem.IsLastHitGreater === 1 ? '#dfdfdf' : ''}">
                              <!-- {{dataItem?.EquipmentReliability}} -->
                              {{dataItem?.EquipmentReliability >= 0 && dataItem?.EquipmentReliability <= 3 ? 'Minor' :
                                (dataItem?.EquipmentReliability>= 4 && dataItem?.EquipmentReliability <= 6 ? 'Moderate' :
                                  (dataItem?.EquipmentReliability>= 7 && dataItem?.EquipmentReliability <= 10 ? 'Severe' : ''
                                    ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="EnergyWasteCostAvoidanceKWH" title="Energy Waste Avoided kWh ($)" width="200"
                          [minResizableWidth]="200">
                          <ng-template kendoGridCellTemplate let-dataItem>

                            <div *ngIf="editItemEnergyWaste !== dataItem" (click)="editEnergyWaste(dataItem)" style="padding: 15px;">
                              <div style="text-align: right; width: 60%; font-weight: bolder; font-family: Calibri Light !important;" [ngStyle]="{
                                                  'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                                                }">
                                {{dataItem?.EnergyWasteCostAvoidanceKWH | number:'1.0-0'}} (${{dataItem?.EnergyWasteCostAvoidance$
                                | number:'1.0-0'}})
                              </div>
                            </div>

                            <div *ngIf="editItemEnergyWaste === dataItem">
                              <input type="number" step="0.1" min="0" [(ngModel)]="dataItem.EnergyWasteCostAvoidanceKWH"
                                (input)="onInputChangeEnergyWaste($event, dataItem)"
                                (keydown.enter)="enterEnergyWaste(dataItem)"
                                (blur)="enterEnergyWaste(dataItem)"
                                style="width: 100px;
                                        padding-left: 10px;
                                        font-family: Calibri Light !important;
                                        border: 0.1px solid lightgray;
                                        border-radius: 5px;">
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <!-- <kendo-grid-column field="EnergyWasteCostAvoidanceKWH" title="Energy Waste Avoided kWh" width="160" [minResizableWidth]="160">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <div style="text-align: right; width: 60%; font-weight: bolder;">
                      {{dataItem?.EnergyWasteCostAvoidanceKWH | number:'1.0-0'}}
                      </div>
                  </ng-template>
                  </kendo-grid-column> -->
                        <kendo-grid-column field="WS_Response_String" title="Outcome" width="170" [minResizableWidth]="170">
                          <!-- <ng-template kendoGridCellTemplate let-dataItem>

                      <div *ngIf="editOutcomeItem !== dataItem" (dblclick)="editOutcome(dataItem)">
                        <div title="Double click to edit outcome">{{ dataItem?.WS_Response_String}}</div>
                      </div>


                      <div *ngIf="editOutcomeItem === dataItem">
                          <kendo-dropdownlist class="outcomeDrop" style="width: 120px;" [data]="outcomeDropData"
                          textField="key"
                          valueField="value" [popupSettings]="{ width: 'auto' }" [(ngModel)]="dataItem.WS_Response_String" (valueChange)="outcomeValueChange($event.value, dataItem)"></kendo-dropdownlist>
                          <button class="cross-icon" style="background: none;
                          color: red;
                          border: none;
                          cursor: pointer;" (click)="cancelEdit(dataItem)">
                            &#10006;
                          </button>
                      </div>

                    </ng-template> -->

                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openOutcomePopup(dataItem?.WS_Response_String, dataItem)" [ngStyle]="{
                              'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                            }" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? 'OPEN TASK' : dataItem?.WS_Response_String }}
                              <span style="margin-left: 10px;"><i class="fa fa-pencil" title="Edit outcome" aria-hidden="true"
                                  style="font-size: 16px;"
                                  [ngStyle]="{'color':dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '#00ccff'}"></i>
                                  <!-- <i *ngIf="dataItem.IsLastHitGreater === 1" class="fa fa-exclamation-triangle" aria-hidden="true" style="color: rgb(247, 17, 0); margin-left: 10px;"></i> -->
                              </span>
                            </div>
                          </ng-template>

                          <!-- <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                            let-filterService="filterService">
                                            <kendo-grid-string-filter-menu
                                [column]="column"
                                [filter]="filter"
                                [filterService]="filterService"
                                operator="contains"
                            >
                            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                            <kendo-filter-eq-operator></kendo-filter-eq-operator>
                            <kendo-filter-contains-operator></kendo-filter-contains-operator>
                            <kendo-filter-neq-operator></kendo-filter-neq-operator>
                            <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field"
                                              [currentFilter]="filter" [filterService]="filterService" textField="value"
                                              valueField="code" [data]="outcomeFilterIMCompleteList"></dropdownlist-filter>
                                        </ng-template> -->

                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="outcomeFilterIMCompleteList">
                            </multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="Outcome_Summary" title="Outcome Summary" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopupOutcomeSummary(dataItem?.Outcome_Summary)" [ngStyle]="{
                              'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                            }" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Outcome_Summary)).textWithoutLastWord}}
                              <span *ngIf="dataItem?.Outcome_Summary && !dataItem.IsServiceCallOpen">{{dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Outcome_Summary)).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;margin-left: 5px;"
                                  [ngStyle]="{'color':dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '#00ccff'}"></i></span>
                            </div>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="Technician_Summary" title="Technician Summary" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopup(dataItem?.Technician_Summary)" [ngStyle]="{
                              'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                            }" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Technician_Summary)).textWithoutLastWord}}
                              <span *ngIf="!dataItem.IsServiceCallOpen">{{dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Technician_Summary)).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;margin-left: 5px;"
                                  [ngStyle]="{'color':dataItem.IsLastHitGreater === 1 ? 'darkgrey' : '#00ccff'}"></i></span>
                            </div>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column *ngIf="userRole?.RoleName == 'CUST'" field="CustomerRating" title="Rating"
                          width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>

                            <div *ngIf="editItem !== dataItem" class="parent_customer_div" (click)="editRating(dataItem)"
                              style="padding: 15px;">
                              <div style="font-size: 14.3px; font-family: Calibri Light !important;" [ngStyle]="{
                                'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                              }" title="Please enter the rating" class="ratingValue">{{
                                dataItem.CustomerRating || '--' }}</div>
                            </div>

                            <div *ngIf="editItem === dataItem">
                              <input type="number" step="0.1" min="0" max="100" [(ngModel)]="dataItem.CustomerRating"
                                (input)="onInputChange($event, dataItem)" (keydown.enter)="enterRating(dataItem)" style="width: 100px;
                      padding-left: 10px;
                      font-family: Calibri Light !important;
                      border: 0.1px solid lightgray;
                      border-radius: 5px;">
                            </div>
                          </ng-template>
                        </kendo-grid-column>



                        <kendo-grid-column *ngIf="userRole?.RoleName != 'CUST'" field="AccountManagerRating" title="Rating"
                          width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>

                            <div *ngIf="editItemAccountManager !== dataItem" class="parent_customer_div"
                              (click)="editRatingAccountManager(dataItem)" style="padding: 15px;">
                              <div style="font-size: 14.3px; font-family: Calibri Light !important;" [ngStyle]="{
                                'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'
                              }" title="Please enter the rating" class="ratingValue">{{
                                dataItem.AccountManagerRating || '--' }}</div>
                            </div>

                            <div *ngIf="editItemAccountManager === dataItem">
                              <input type="number" step="0.1" min="0" max="100" [(ngModel)]="dataItem.AccountManagerRating"
                                (input)="onInputChangeAccountManager($event, dataItem)"
                                (keydown.enter)="enterRatingAccountManager(dataItem)" style="width: 100px;
                      padding-left: 10px;
                      font-family: Calibri Light !important;
                      border: 0.1px solid lightgray;
                      border-radius: 5px;">
                            </div>
                          </ng-template>
                        </kendo-grid-column>


                        <kendo-grid-column field="AccountManagerRating" title="Service Report" width="120"
                          [minResizableWidth]="120">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <button [ngClass]="{'hideButton':dataItem.ServiceCallID==='',
                                          'disableButton': dataItem.IsServiceCallOpen || dataItem.IsLastHitGreater === 1}"
                              kendoButton class="followupButton downloadButton" [primary]="true"
                              (click)="DownloadSummaryReport(dataItem)" primary="true" [disabled]="dataItem.IsServiceCallOpen"
                              style="width: 40px !important; font-family: Calibri Light !important;">
                              <span class="k-icon k-i-download"></span>
                            </button>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="IsLastHitGreater" title="" width="160" [minResizableWidth]="150">
                          <ng-template kendoGridHeaderTemplate>
                            <span>
                              Not Resolved
                              <i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: rgb(247, 17, 0); margin-left: 10px;"></i>
                            </span>
                          </ng-template>
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div *ngIf="dataItem.IsLastHitGreater === 1" [ngStyle]="{
                                              'color': dataItem.IsLastHitGreater === 1 ? 'darkgrey' : 'inherit'}">
                              <span>
                                NOT RESOLVED
                                <i class="fa fa-exclamation-triangle" aria-hidden="true" style="color: rgb(247, 17, 0); margin-left: 10px;"></i>
                              </span>
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter" let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code" [data]="[{value: 'Not Resolved', code: 1}]"
                            ></multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>

                        <!-- <kendo-grid-column field="SkySparkURL" title="Trending Data" width="300">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      <div (click)="getUrl(dataItem)" class="sparkUrl">
                          {{dataItem?.SkySparkURL}}
                      </div>
                  </ng-template>
                  </kendo-grid-column> -->

                        <!-- <kendo-grid-column field="" title="" width="80">
                      <ng-template kendoGridCellTemplate let-dataItem>
                          <button [ngClass]="{'hideButton':dataItem.ServiceCallID===''}"  kendoButton class="followupButton downloadButton" title="DownLoad Call Summary Report" [primary]="true"
                              (click)="DownloadSummaryReport(dataItem)" primary="true"><span
                                  class="k-icon k-i-download"></span></button>
                      </ng-template>
                  </kendo-grid-column> -->
                      </kendo-grid>


                    </div>
                    <!-- <div class="row col-md-12 mt-2 sticky" style="padding: 10px">
                      <div class="col-md-12 d-flex">
                        <div class="col-md-2"></div>
                        <div class="col-md-10">
                          <p class="" style="margin-left: 60%; color: black;">*1 = Low Impact 5 = Moderate Impact 10 =
                            Significant Impact</p>
                        </div>
                      </div>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </ng-container>

          <ng-container *ngIf="panel.key === 'backlog'">
            <div class="row align-items-md-stretch section_3">
              <div class="col-md-12" style="padding-right: 0 !important;">
                <div class="row align-items-md-stretch section_3">

                  <div class="col-md-12 problem_proactively" style="border-radius: 0;">
                    <div class="card bar-chart-example"
                      style="border-radius: 7px;">
                      <div class="card bar-chart-example"
                        style="border-radius: 7px;">
                        <div style="height: 460px; margin-top: 2vh">
                          <h3 style="font-weight: bold;
                                    color: black;
                                    margin-left: 0px;
                                    font-family:Roboto;
                                    font-size: 1.2vw;
                                    margin-top: -1vh;
                                    margin-bottom: 1.9vh;">Backlogged Data-Driven Task Types
                          </h3>

                          <div style="display: flex; align-items: center;">
                            <div id="container1" style="width: 100%"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-12" style="padding-right: 0 !important;">
                <div class="card bar-chart-example screenScroll" style="height: 300px !important; padding: 20px 5px 0 0">
                  <h3 style="font-weight: bold;
                                    color: black;
                                    margin-left: 0px;
                                    font-family:Roboto;
                                    font-size: 1.2vw;
                                    margin-top: -0.5vh;
                                    margin-bottom: 1.2vh;">Backlogged Tasks & Impact On Building Performance
                  </h3>

                  <div style="position: relative; display: flex;">

                    <kendo-chart [seriesColors]="['rgb(0, 17, 50)','rgb(13, 192, 255)','rgb(137, 224, 255)']"
                      class="donutChart" style="width: 60%;">
                      <kendo-chart-legend [markers]="legendMarker"
                        [labels]="{color: '#888888', font: '13px Calibri Light !important'}"></kendo-chart-legend>
                      <kendo-chart-series>
                        <kendo-chart-series-item type="donut" [data]="TotalTaskBacklogData" categoryField="name" field="value"
                          [padding]=0 [margin]=0 [size]=50 [labels]="seriesLabels">
                          <kendo-chart-series-item-tooltip>
                            <ng-template let-value="value" let-category="category">
                              {{category}}: {{value }}
                            </ng-template>
                          </kendo-chart-series-item-tooltip>
                        </kendo-chart-series-item>
                      </kendo-chart-series>
                      <ng-template kendoChartDonutCenterTemplate>
                        <p style="font: bold 2.2rem 'Roboto' !important; color: black; margin-top: 15px;">{{ SumOfTasksBacklog
                          != 0 ?
                          SumOfTasksBacklog : '' }}</p>
                      </ng-template>
                      <kendo-chart-legend></kendo-chart-legend>
                    </kendo-chart>


                  <div class="backlog-ew">
                    <div class="card bar-chart-example screenScroll"
                      style="padding: 10px !important; height: 100% !important;  border: 1px solid #b3b3b3;">
                      <div class="imp-ew-container"
                        style="display: flex; flex-direction: column; align-items: center; gap: 10px; max-width: 336px; font-family: Calibri Light !important;">
                        <!-- Top: Value -->
                        <div class="imp-ew"
                          style="font-size: 2.6vw; font-weight: bold; color: #555 !important;">
                          {{UnrealisedEnergyWasteCostAvoidanceKWHBacklog ?
                          (UnrealisedEnergyWasteCostAvoidanceKWHBacklog | number:'1.0-0') : (0 | number:'1.0-0')}}
                        </div>
                        <!-- Bottom: Description -->
                        <div style="text-align: center; font-size: 1vw; color: #888 !important; font-weight: 440 !important;">
                          <div>Unrealised Energy Waste Avoidance-kWh Backlog</div>
                          <!-- <div></div> -->
                        </div>
                      </div>
                    </div>
                    <div class="card bar-chart-example screenScroll"
                      style="padding: 10px !important; height: 100% !important; border: 1px solid #b3b3b3;">
                      <div class="imp-ew-container"
                        style="display: flex; flex-direction: column; align-items: center; gap: 10px; max-width: 336px; font-family: Calibri Light !important;">
                        <!-- Top: Value -->
                        <div class="imp-ew"
                          style="font-size: 2.6vw; font-weight: bold; color: #555 !important;">
                          {{UnrealisedEnergyWasteCostAvoidanceBacklog ?
                          (UnrealisedEnergyWasteCostAvoidanceBacklog | currency : 'USD' : 'symbol' : '1.0-0') : (0 |
                          currency : 'USD' : 'symbol' : '1.0-0')}}
                        </div>
                        <!-- Bottom: Description -->
                        <div style="text-align: center; font-size: 1vw; color: #888 !important; font-weight: 440 !important;">
                          <div>Unrealised Energy Waste Cost Avoidance Backlog</div>
                          <!-- <div></div> -->
                        </div>
                      </div>
                    </div>
                  </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="row align-items-md-stretch section_3" style="margin-top: 2vh; margin-bottom: 2vh" id="BacklogTable">
              <div class="col-md-12 problem_proactively" style="border-radius: 0; padding: 0 0 0 15px;">
                <div class="card bar-chart-example"
                  style="border-radius: 7px;">
                  <div class="table_div">
                    <div class="mt-3 data-table" style="height: 550px;">
                      <kendo-grid style="height: 100%;" [data]="gridDataForIMBacklogList" class="gridFontStyle scroll-container"
                        [sortable]="{allowUnsort: true, mode:'multiple'}" [skip]="IMDataDrivenTBacklogListState.skip"
                        [sort]="IMDataDrivenTBacklogListState.sort" [filter]="IMDataDrivenTBacklogListState.filter"
                        filterable="menu" [resizable]="true" (dataStateChange)="dataStateChangeBacklog($event)"
                        [navigable]="true" id="Backlog-list" [rowClass]="rowCallback">
                        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
                          <div class="container-fluid p-0" style="width: 100%; margin-top: -2%;">
                            <div style="margin-top: 1vh;">
                              <div style="font-family: Calibri Light !important; font-size: 1vw; text-align: end;">
                                <p style="margin: 0px; font-style: italic; color: black;"> <b>New Task Cycle Qty & Date:</b>
                                  &nbsp;<b>{{ REFGEN_TASKS }}</b>&nbsp;Mechanical Tasks On The
                                  <b>&nbsp;{{ REFGEN_NEXTRUN }}</b>&nbsp;&&nbsp;
                                  <b>{{ DDC_TASKS }}</b>&nbsp;Controls Tasks On The
                                  <b>&nbsp;{{ DDC_NEXTRUN }}</b>
                                </p>
                              </div>
                            </div>
                          </div>
                        </ng-template>
                        <kendo-grid-column field="LOCATNNM" title="Building" width="160" [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;">
                              {{dataItem?.LOCATNNM}}
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="buildingFilterIMBacklogList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <!-- <kendo-grid-column field="ServiceCallID" title="Service Call ID" width="190" [minResizableWidth]="190">

                      </kendo-grid-column> -->

                        <kendo-grid-column field="Equipment" title="Equipment" width="160" [minResizableWidth]="160">
                          <!-- <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                                let-filterService="filterService">
                                                <kendo-grid-string-filter-menu
                                    [column]="column"
                                    [filter]="filter"
                                    [filterService]="filterService"
                                    operator="contains"
                                >
                                <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                                <kendo-filter-eq-operator></kendo-filter-eq-operator>
                                <kendo-filter-contains-operator></kendo-filter-contains-operator>
                                <kendo-filter-neq-operator></kendo-filter-neq-operator>
                                <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                                <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                                </kendo-grid-string-filter-menu>
                                                <dropdownlist-filter [isPrimitive]="true" [field]="column.field"
                                                  [currentFilter]="filter" [filterService]="filterService" textField="value"
                                                  valueField="code" [data]="equipmentFilterIMBacklogList"></dropdownlist-filter>
                                            </ng-template> -->

                          <ng-template kendoGridCellTemplate let-dataItem>
                            <a style="font-weight: bold; font-family: Calibri Light !important; text-decoration: underline;"
                              (click)="getUrlForBacklog(dataItem)">{{dataItem?.Equipment}}</a>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="equipmentFilterIMBacklogList">
                            </multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="TaskType" title="Task Type & Trending data" width="200"
                          [minResizableWidth]="200">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopupTaskDescription(dataItem?.TaskDescription)"
                              style="font-family: Calibri Light !important;">
                                {{splitTextWithIcon(dataItem?.TaskType).textWithoutLastWord}}
                                <span>{{splitTextWithIcon(dataItem?.TaskType).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px; color: #00ccff;margin-left: 5px;"></i></span>
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="taskTypeFilterIMBacklogList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="MechanicalControls" title="Mechanical Controls" width="190"
                          [minResizableWidth]="190">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;">
                              {{dataItem.MechanicalControls}}
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="ThermalComfort" title="Thermal Comfort" width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;" class="thermal-background"
                              [ngClass]="'thermal-' + dataItem.ThermalComfort">
                              <!-- {{dataItem?.ThermalComfort}} -->
                              {{dataItem?.ThermalComfort >= 0 && dataItem?.ThermalComfort <= 3 ? 'Minor' :
                                (dataItem?.ThermalComfort>= 4 && dataItem?.ThermalComfort <= 6 ? 'Moderate' :
                                  (dataItem?.ThermalComfort>= 7 && dataItem?.ThermalComfort <= 10 ? 'Severe' : '' ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="EquipmentReliability" title="Equipment Reliability" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;" class="thermal-background"
                              [ngClass]="'thermal-' + dataItem.EquipmentReliability">
                              <!-- {{dataItem?.EquipmentReliability}} -->
                              {{dataItem?.EquipmentReliability >= 0 && dataItem?.EquipmentReliability <= 3 ? 'Minor' :
                                (dataItem?.EquipmentReliability>= 4 && dataItem?.EquipmentReliability <= 6 ? 'Moderate' :
                                  (dataItem?.EquipmentReliability>= 7 && dataItem?.EquipmentReliability <= 10 ? 'Severe' : ''
                                    ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="PotentialEnergyWasteCostAvoidanceKWH"
                          title="Potential Energy Waste Avoidance kWh ($)" width="200" [minResizableWidth]="200">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div
                              style="text-align: right; width: 70%; font-weight: bolder; font-family: Calibri Light !important;">
                              {{dataItem?.PotentialEnergyWasteCostAvoidanceKWH | number:'1.0-0'}}
                              (${{dataItem?.PotentialEnergyWasteCostAvoidance$ | number:'1.0-0'}})
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <!-- <kendo-grid-column field="PotentialEnergyWasteCostAvoidanceKWH" title="Potential Energy Waste Avoidance kWh" width="250" [minResizableWidth]="250">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <div style="text-align: right; width: 70%; font-weight: bolder;">
                          {{dataItem?.PotentialEnergyWasteCostAvoidanceKWH | number:'1.0-0'}}
                        </div>
                      </ng-template>
                      </kendo-grid-column> -->


                        <kendo-grid-column field="DateLastHit" title="Last Occurrence" filter="date" format="{0:dd-MM-yyyy}"
                          operator="eq" [width]="180" [minResizableWidth]="180">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <span style="font-family: Calibri Light !important;"
                              [hidden]="dataItem.DateLastHit.getFullYear()!=1"> -</span>
                            <span style="font-family: Calibri Light !important;"
                              [hidden]="dataItem.DateLastHit.getFullYear()==1">
                              {{dataItem.DateLastHit | date: 'dd-MM-yyyy'}}</span>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-filter let-column="column"
                            let-filterService="filterService">
                            <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="eq">
                            </kendo-grid-date-filter-menu>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="PotentialEnergyWasteCostAvoidanceKWH" title="Complete Task" width="170"
                          [minResizableWidth]="170">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openBacklogOutcomePopup(dataItem)">
                              <span style="margin-left: 10px; font-family: Calibri Light !important;"><i class="fa fa-pencil"
                                  title="Edit outcome" aria-hidden="true" style="font-size: 16px; color: #00ccff;"></i></span>
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="PotentialEnergyWasteCostAvoidanceKWH" title="Place Call" width="160"
                          [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <button kendoButton title="Place Call" class="followupButton downloadButton" [primary]="true"
                              primary="true" style="width: 70px !important;
                            padding: 0;
                            font-size: 14px;
                            font-family: Calibri Light !important;
                            vertical-align: middle;
                            border-radius: 0px;
                            display: flex;
                            text-transform: capitalize !important;
                        " (click)="raiseServiceCall(dataItem)">Place</button>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="Priority" title="Prioritisation" width="160" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <kendo-switch [onLabel]="'Priority'" [offLabel]="'Normal'" [checked]="dataItem.Priority"
                              (valueChange)="togglePriority(dataItem, $event)">
                            </kendo-switch>
                          </ng-template>
                        </kendo-grid-column>

                        <!-- <kendo-grid-column field="TrendingData" title="Trending Data" width="300">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <div (click)="getUrlForBacklog(dataItem)" class="sparkUrl">
                              {{dataItem?.TrendingData}}
                          </div>
                      </ng-template>
                      </kendo-grid-column> -->

                        <!-- <kendo-grid-column field="" title="" width="80" [resizable]="false">
                        <ng-template kendoGridCellTemplate let-dataItem>
                            <button [ngClass]="{'hideButton':dataItem.ServiceCallID===''}"  kendoButton class="followupButton downloadButton" title="DownLoad Call Summary Report" [primary]="true"
                                (click)="DownloadSummaryReport(dataItem)" primary="true"><span
                                    class="k-icon k-i-download"></span></button>
                        </ng-template>
                    </kendo-grid-column> -->

                      </kendo-grid>
                    </div>
                    <!-- <div class="row col-md-12 mt-2 sticky" style="padding: 10px">
                          <div class="col-md-12 d-flex">
                            <div class="col-md-2"></div>
                            <div class="col-md-10">
                              <p class="" style="margin-left: 45%;">*1 = Low Impact 5 = Moderate Impact 10 = Significant Impact</p>
                            </div>
                          </div>
                        </div> -->
                  </div>
                </div>
              </div>
            </div>

          </ng-container>

          <ng-container *ngIf="panel.key === 'tuning' && isTuningModuleActive">
            <div *ngIf="isTuningModuleActive" class="row align-items-md-stretch section_3" id="tuningTable">
              <div class="col-md-12 problem_proactively" style="border-radius: 0; padding: 0 0 0 15px;">
                <div class="card bar-chart-example" style="border-radius: 7px;">
                  <div class="table_div">
                    <div class="mt-3 data-table" style="height: 550px; margin-top: 0 !important;">
                      <kendo-grid style="height: 100%;" [data]="gridDataForIMTuningList"
                        class="gridFontStyle scroll-container" [sortable]="{allowUnsort: true, mode:'multiple'}"
                        [skip]="IMDataDrivenTTuningListState.skip" [sort]="IMDataDrivenTTuningListState.sort"
                        [filter]="IMDataDrivenTTuningListState.filter" filterable="menu" [resizable]="true"
                        (dataStateChange)="dataStateChangeTuning($event)" [navigable]="true">
                        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
                        </ng-template>
                        <kendo-grid-column field="Building" title="Building" width="160" [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;">
                              {{ dataItem?.Building }}
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="buildingFilterIMTuningList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="ServiceCallID" title="Service Call ID" width="170" [minResizableWidth]="170">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div style="font-family: Calibri Light !important;">
                              {{ dataItem?.ServiceCallID }}
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="Equipment" title="Equipment" width="160" [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <a style="font-family: Calibri Light !important; font-weight: bold; text-decoration: underline;"
                              (click)="getUrl(dataItem)">{{dataItem?.Equipment}}</a>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="equipmentFilterIMTuningList">
                            </multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="Notes" title="Task Type & Trending Data" width="200"
                          [minResizableWidth]="200">

                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopupTaskDescription(dataItem?.TaskDescription)"
                              style="font-family: Calibri Light !important;">
                                {{splitTextWithIcon(dataItem?.Notes).textWithoutLastWord}}
                                <span>{{splitTextWithIcon(dataItem?.Notes).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px; color: #00ccff;margin-left: 5px"></i></span>
                            </div>
                          </ng-template>
                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                              operator="contains">
                              <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                              <kendo-filter-eq-operator></kendo-filter-eq-operator>
                              <kendo-filter-contains-operator></kendo-filter-contains-operator>
                              <kendo-filter-neq-operator></kendo-filter-neq-operator>
                              <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                              <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="taskTypeFilterIMTuningList"></dropdownlist-filter>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="ThermalComfort" title="Thermal Comfort" width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="thermal-background" style="font-family: Calibri Light !important;"
                              [ngClass]="'thermal-' + dataItem.ThermalComfort" >
                              {{dataItem?.ThermalComfort >= 0 && dataItem?.ThermalComfort <= 3 ? 'Minor' :
                                (dataItem?.ThermalComfort>= 4 && dataItem?.ThermalComfort <= 6 ? 'Moderate' :
                                  (dataItem?.ThermalComfort>= 7 && dataItem?.ThermalComfort <= 10 ? 'Severe' : '' ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="EquipmentReliability" title="Equipment Reliability" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div class="thermal-background" style="font-family: Calibri Light !important;"
                              [ngClass]="'thermal-' + dataItem.EquipmentReliability" >
                              {{dataItem?.EquipmentReliability >= 0 && dataItem?.EquipmentReliability <= 3 ? 'Minor' :
                                (dataItem?.EquipmentReliability>= 4 && dataItem?.EquipmentReliability <= 6 ? 'Moderate' :
                                  (dataItem?.EquipmentReliability>= 7 && dataItem?.EquipmentReliability <= 10 ? 'Severe' : ''
                                    ))}} </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="EnergyWasteCostAvoidanceKWH" title="Energy Waste Avoided kWh ($)" width="200"
                          [minResizableWidth]="200">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div
                              style="text-align: right; width: 60%; font-weight: bolder; font-family: Calibri Light !important;"
                              >
                              {{dataItem?.EnergyWasteCostAvoidanceKWH | number:'1.0-0'}} (${{dataItem?.EnergyWasteCostAvoidance$
                              | number:'1.0-0'}})
                            </div>
                          </ng-template>
                        </kendo-grid-column>
                        <kendo-grid-column field="WS_Response_String" title="Outcome" width="170" [minResizableWidth]="170">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openOutcomePopup(dataItem?.WS_Response_String, dataItem)" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? 'OPEN TASK' : dataItem?.WS_Response_String }}
                              <span style="margin-left: 10px;"><i class="fa fa-pencil" title="Edit outcome" aria-hidden="true"
                                  style="font-size: 16px;color: #00ccff"></i>
                              </span>
                            </div>
                          </ng-template>

                          <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                            let-filterService="filterService">
                            <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                              [filterService]="filterService" textField="value" valueField="code"
                              [data]="outcomeFilterIMTuningList">
                            </multiselect-filter>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="Outcome_Summary" title="Outcome Summary" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopupOutcomeSummary(dataItem?.Outcome_Summary)" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Outcome_Summary)).textWithoutLastWord }}
                              <span *ngIf="dataItem?.Outcome_Summary && !dataItem.IsServiceCallOpen">{{dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Outcome_Summary)).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;color: #00ccff; margin-left: 5px;"
                                ></i></span>
                            </div>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column field="Technician_Summary" title="Technician Summary" width="160"
                          [minResizableWidth]="160">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <div (click)="openPopup(dataItem?.Technician_Summary)" style="font-family: Calibri Light !important;">
                              {{ dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Technician_Summary)).textWithoutLastWord }}
                              <span *ngIf="!dataItem.IsServiceCallOpen">{{dataItem.IsServiceCallOpen ? '' : splitTextWithIcon(getTecSummaryCodeWithDate(dataItem?.Technician_Summary)).lastWord}}<i
                                  class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px;color: #00ccff; margin-left: 5px;"
                                ></i></span>
                            </div>
                          </ng-template>
                        </kendo-grid-column>

                        <kendo-grid-column *ngIf="userRole?.RoleName == 'CUST'" field="CustomerRating" title="Rating"
                          width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>

                            <div *ngIf="editItem !== dataItem" class="parent_customer_div" (click)="editRating(dataItem)"
                              style="padding: 15px;">
                              <div style="font-size: 14.3px; font-family: Calibri Light !important;" title="Please enter the rating" class="ratingValue">{{
                                dataItem.CustomerRating || '--' }}</div>
                            </div>

                            <div *ngIf="editItem === dataItem">
                              <input type="number" step="0.1" min="0" max="100" [(ngModel)]="dataItem.CustomerRating"
                                (input)="onInputChange($event, dataItem)" (keydown.enter)="enterRating(dataItem)" style="width: 100px;
                      padding-left: 10px;
                      font-family: Calibri Light !important;
                      border: 0.1px solid lightgray;
                      border-radius: 5px;">
                            </div>
                          </ng-template>
                        </kendo-grid-column>



                        <kendo-grid-column *ngIf="userRole?.RoleName != 'CUST'" field="AccountManagerRating" title="Rating"
                          width="150" [minResizableWidth]="150">
                          <ng-template kendoGridCellTemplate let-dataItem>

                            <div *ngIf="editItemAccountManager !== dataItem" class="parent_customer_div"
                              (click)="editRatingAccountManager(dataItem)" style="padding: 15px;">
                              <div style="font-size: 14.3px; font-family: Calibri Light !important;" title="Please enter the rating" class="ratingValue">{{
                                dataItem.AccountManagerRating || '--' }}</div>
                            </div>

                            <div *ngIf="editItemAccountManager === dataItem">
                              <input type="number" step="0.1" min="0" max="100" [(ngModel)]="dataItem.AccountManagerRating"
                                (input)="onInputChangeAccountManager($event, dataItem)"
                                (keydown.enter)="enterRatingAccountManager(dataItem)" style="width: 100px;
                      padding-left: 10px;
                      font-family: Calibri Light !important;
                      border: 0.1px solid lightgray;
                      border-radius: 5px;">
                            </div>
                          </ng-template>
                        </kendo-grid-column>


                        <kendo-grid-column field="AccountManagerRating" title="Service Report" width="120"
                          [minResizableWidth]="120">
                          <ng-template kendoGridCellTemplate let-dataItem>
                            <button [ngClass]="{'hideButton':dataItem.ServiceCallID==='',
                                          'disableButton': dataItem.IsServiceCallOpen }"
                              kendoButton class="followupButton downloadButton" [primary]="true"
                              (click)="DownloadSummaryReport(dataItem)" primary="true" [disabled]="dataItem.IsServiceCallOpen"
                              style="width: 40px !important; font-family: Calibri Light !important;">
                              <span class="k-icon k-i-download"></span>
                            </button>
                          </ng-template>
                        </kendo-grid-column>
                      </kendo-grid>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </ng-container>

          <ng-container *ngIf="panel.key === 'awaitingApproval'">
            <div class="row align-items-md-stretch section_3">
              <div class="col-md-12" style="padding-right: 0 !important;">
                <div class="card bar-chart-example screenScroll" style="height: 300px !important; margin-top: 2vh; padding: 0 5px 0 0;">
                  <h3 style="font-weight: bold;
                                    color: black;
                                    margin-left: 0px;
                                    font-family:Roboto;
                                    font-size: 1.2vw;
                                    margin-top: -0.5vh;
                                    margin-bottom: 1.2vh;">Proposed Data-Driven Tasks By Category
                  </h3>
                  <div style="position: relative; display: flex;">
                    <kendo-chart [seriesColors]="['rgb(0, 17, 50)','rgb(13, 192, 255)','rgb(137, 224, 255)']"
                      class="donutChart" style="width: 60%;">
                      <!-- <kendo-chart-title text="Proposed Data-Driven Tasks By Category" font="bold 1.2vw Roboto!important"
                        color="black" align="left" [margin]="{top: -8}">
                      </kendo-chart-title> -->
                      <kendo-chart-legend [markers]="legendMarker"
                        [labels]="{color: '#888888', font: '13px Calibri Light !important'}"></kendo-chart-legend>
                      <kendo-chart-series>
                        <kendo-chart-series-item type="donut" [data]="TotalProblemIdentifiedData" categoryField="name"
                          field="value" [padding]=0 [margin]=0 [size]=50 [labels]="seriesLabels">
                          <kendo-chart-series-item-tooltip>
                            <ng-template let-value="value" let-category="category">
                              {{category}}: {{value }}
                            </ng-template>
                          </kendo-chart-series-item-tooltip>
                        </kendo-chart-series-item>
                      </kendo-chart-series>
                      <ng-template kendoChartDonutCenterTemplate>
                        <p style="font: bold 2.2rem 'Roboto' !important; color: black; margin-top: 15px;">{{ SumOfTasksProposed
                          != 0 ?
                          SumOfTasksProposed : '' }}</p>
                      </ng-template>
                      <kendo-chart-legend></kendo-chart-legend>
                    </kendo-chart>


                  <div class="backlog-ew">
                    <div class="card bar-chart-example screenScroll"
                      style="padding: 10px !important; height: 100% !important;  border: 1px solid #b3b3b3;">
                      <div class="imp-ew-container"
                        style="display: flex; flex-direction: column; align-items: center; gap: 10px; max-width: 336px; font-family: Calibri Light !important;">
                        <!-- Top: Value -->
                        <div class="imp-ew"
                          style="font-size: 2.6vw; font-weight: bold; color: #555 !important;">
                          {{AATotalUnrealisedEnergyWasteAvoidanceKWH ?
                          (AATotalUnrealisedEnergyWasteAvoidanceKWH | number:'1.0-0') : (0 | number:'1.0-0')}}
                        </div>
                        <!-- Bottom: Description -->
                        <div style="text-align: center; font-size: 1vw; color: #888 !important; font-weight: 440 !important;">
                          <div>Unrealised Energy Waste Avoidance-kWh</div>
                        </div>
                      </div>
                    </div>
                    <div class="card bar-chart-example screenScroll"
                      style="padding: 10px !important; height: 100% !important; border: 1px solid #b3b3b3;">
                      <div class="imp-ew-container"
                        style="display: flex; flex-direction: column; align-items: center; gap: 10px; max-width: 336px; font-family: Calibri Light !important;">
                        <!-- Top: Value -->
                        <div class="imp-ew"
                          style="font-size: 2.6vw; font-weight: bold; color: #555 !important;">
                          {{AATotalUnrealisedEnergyWasteog ?
                          (AATotalUnrealisedEnergyWaste | currency : 'USD' : 'symbol' : '1.0-0') : (0 |
                          currency : 'USD' : 'symbol' : '1.0-0')}}
                        </div>
                        <!-- Bottom: Description -->
                        <div style="text-align: center; font-size: 1vw; color: #888 !important; font-weight: 440 !important;">
                          <div>Unrealised Energy Waste Cost Avoidance</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  </div>
                </div>
              </div>
              <div class="col-md-12 section_4_data" style="margin-bottom: 2vh;">
                <div class="row d-flexalign-items-md-stretch">

                  <div class="col-md-12 problem_proactively" style="border-radius: 0; padding-right: 0;">
                    <div class="card bar-chart-example"
                      style="border-radius: 7px;">
                      <div class="table_div">
                        <div class="mt-3 data-table" style="height: 550px;">
                          <kendo-grid style="height: 100%;" [data]="gridDataForIMAwaitingList" [selectable]="true"
                            class="gridFontStyle awaiting-grid scroll-container" (selectionChange)="isRowSelected($event)"
                            [sortable]="{allowUnsort: true, mode:'multiple'}" (dblclick)="getDetail()"
                            [skip]="IMDataDrivenTAwaitingListState.skip" [sort]="IMDataDrivenTAwaitingListState.sort"
                            [filter]="IMDataDrivenTAwaitingListState.filter" [resizable]="true" filterable="menu"
                            (dataStateChange)="dataStateChangeAwaiting($event)" [navigable]="true">
                            <ng-template style="text-align: right;" kendoGridToolbarTemplate>
                              <div class="container-fluid p-0">
                                <div class="row">
                                  <div>
                                    <h3 style="    font-weight: bold;
                                    color: black;
                                    margin-left: 6px;
                                    font-family: Roboto;
                                    font-size: 1.2vw;
                                    margin-top: -2.4vh;">Proposed Data-Driven Tasks Awaiting
                                      Approval</h3>
                                  </div>
                                </div>
                              </div>
                            </ng-template>
                            <kendo-grid-column field="Building" title="Building" width="160" [minResizableWidth]="160">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div style="font-family: Calibri Light !important;">
                                  {{dataItem?.Building}}
                                </div>
                              </ng-template>
                              <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                let-filterService="filterService">
                                <kendo-grid-string-filter-menu [column]="column" [filter]="filter"
                                  [filterService]="filterService" operator="contains">
                                  <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                                  <kendo-filter-eq-operator></kendo-filter-eq-operator>
                                  <kendo-filter-contains-operator></kendo-filter-contains-operator>
                                  <kendo-filter-neq-operator></kendo-filter-neq-operator>
                                  <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                                  <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                                </kendo-grid-string-filter-menu>
                                <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                                  [filterService]="filterService" textField="value" valueField="code"
                                  [data]="buildingFilterIMAwaitingList"></dropdownlist-filter>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="QuoteReference" title="Quote #" width="160" [minResizableWidth]="160">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div style="font-family: Calibri Light !important;">
                                  {{dataItem.QuoteReference}}
                                </div>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="Equipment" title="Equipment" width="160" [minResizableWidth]="160">
                              <!-- <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                            let-filterService="filterService">
                                            <kendo-grid-string-filter-menu
                                [column]="column"
                                [filter]="filter"
                                [filterService]="filterService"
                                operator="contains"
                            >
                            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                            <kendo-filter-eq-operator></kendo-filter-eq-operator>
                            <kendo-filter-contains-operator></kendo-filter-contains-operator>
                            <kendo-filter-neq-operator></kendo-filter-neq-operator>
                            <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                            </kendo-grid-string-filter-menu>
                                            <dropdownlist-filter [isPrimitive]="true" [field]="column.field"
                                              [currentFilter]="filter" [filterService]="filterService" textField="value"
                                              valueField="code" [data]="equipmentFilterIMAwaitingList"></dropdownlist-filter>
                                        </ng-template> -->
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <a style="font-weight: bold; font-family: Calibri Light !important; text-decoration: underline;"
                                  (click)="getUrl(dataItem)">{{dataItem?.Equipment}}</a>
                              </ng-template>
                              <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                let-filterService="filterService">
                                <multiselect-filter [isPrimitive]="false" [field]="column.field" [currentFilter]="filter"
                                  [filterService]="filterService" textField="value" valueField="code"
                                  [data]="equipmentFilterIMAwaitingList">
                                </multiselect-filter>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="Notes" title="Task Type & Trending Data" width="200"
                              [minResizableWidth]="200">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div (click)="openPopupTaskDescription(dataItem?.TaskDescription)"
                                  style="font-family: Calibri Light !important;">
                                    {{splitTextWithIcon(dataItem?.Notes).textWithoutLastWord}}
                                    <span>{{splitTextWithIcon(dataItem?.Notes).lastWord}}<i
                                      class="fa fa-info-circle" aria-hidden="true" style="font-size: 16px; color: #00ccff;margin-left: 5px"></i></span>
                                </div>
                              </ng-template>
                              <ng-template kendoGridFilterMenuTemplate let-column="column" let-filter="filter"
                                let-filterService="filterService">
                                <kendo-grid-string-filter-menu [column]="column" [filter]="filter"
                                  [filterService]="filterService" operator="contains">
                                  <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
                                  <kendo-filter-eq-operator></kendo-filter-eq-operator>
                                  <kendo-filter-contains-operator></kendo-filter-contains-operator>
                                  <kendo-filter-neq-operator></kendo-filter-neq-operator>
                                  <kendo-filter-not-contains-operator></kendo-filter-not-contains-operator>
                                  <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
                                </kendo-grid-string-filter-menu>
                                <dropdownlist-filter [isPrimitive]="true" [field]="column.field" [currentFilter]="filter"
                                  [filterService]="filterService" textField="value" valueField="code"
                                  [data]="taskTypeFilterAwaitingList"></dropdownlist-filter>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="ThermalComfort" title="Thermal Comfort" width="150"
                              [minResizableWidth]="150">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div style="font-family: Calibri Light !important;" class="thermal-background"
                                  [ngClass]="'thermal-' + dataItem.ThermalComfort">
                                  <!-- {{dataItem?.ThermalComfort}} -->
                                  {{dataItem?.ThermalComfort >= 0 && dataItem?.ThermalComfort <= 3 ? 'Minor' :
                                    (dataItem?.ThermalComfort>= 4 && dataItem?.ThermalComfort <= 6 ? 'Moderate' :
                                      (dataItem?.ThermalComfort>= 7 && dataItem?.ThermalComfort <= 10 ? 'Severe' : '' ))}}
                                        </div>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="EquipmentReliability" title="Equipment Reliability" width="160"
                              [minResizableWidth]="160">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div style="font-family: Calibri Light !important;" class="thermal-background"
                                  [ngClass]="'thermal-' + dataItem.EquipmentReliability">
                                  <!-- {{dataItem?.EquipmentReliability}} -->
                                  {{dataItem?.EquipmentReliability >= 0 && dataItem?.EquipmentReliability <= 3 ? 'Minor' :
                                    (dataItem?.EquipmentReliability>= 4 && dataItem?.EquipmentReliability <= 6 ? 'Moderate' :
                                      (dataItem?.EquipmentReliability>= 7 && dataItem?.EquipmentReliability <= 10 ? 'Severe'
                                        : '' ))}} </div>
                              </ng-template>
                            </kendo-grid-column>
                            <kendo-grid-column field="EnergyWasteCostAvoidanceKWH" title="Energy Waste Avoided kWh ($)"
                              width="200" [minResizableWidth]="200">
                              <ng-template kendoGridCellTemplate let-dataItem>
                                <div
                                  style="text-align: right; width: 60%; font-weight: bolder;font-family: Calibri Light !important;">
                                  {{dataItem?.EnergyWasteCostAvoidanceKWH | number:'1.0-0'}}
                                  (${{dataItem?.EnergyWasteCostAvoidance$ | number:'1.0-0'}})
                                </div>
                              </ng-template>
                            </kendo-grid-column>
                            <!-- <kendo-grid-column field="EnergyWasteCostAvoidanceKWH" title="Energy Waste Avoided kWhr" width="160" [minResizableWidth]="160">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <div style="text-align: right; width: 60%; font-weight: bolder;">
                          {{dataItem?.EnergyWasteCostAvoidanceKWH | number:'1.0-0'}}
                          </div>
                      </ng-template>
                      </kendo-grid-column> -->
                            <!-- <kendo-grid-column field="SkySparkURL" title="Trending Data" width="300">
                        <ng-template kendoGridCellTemplate let-dataItem>
                          <div (click)="getUrl(dataItem)" class="sparkUrl">
                              {{dataItem?.SkySparkURL}}
                          </div>
                      </ng-template>
                      </kendo-grid-column> -->
                          </kendo-grid>
                        </div>
                        <!-- <div class="row col-md-12 mt-2 sticky" style="padding: 10px">
                          <div class="col-md-12 d-flex">
                            <div class="col-md-2"></div>
                            <div class="col-md-10">
                              <p class="" style="margin-left: 45%;">*1 = Low Impact 5 = Moderate Impact 10 = Significant Impact</p>
                            </div>
                          </div>
                        </div> -->
                      </div>
                    </div>
                  </div>
                </div>

              </div>

            </div>

          </ng-container>
        </div>
      </div>
    </ng-container>

    <!-- Site Comfort Performance Trend -->
    <!-- <div class="row align-items-md-stretch section_3">

      <div class="col-md-12 problem_proactively" style="border-radius: 0;">

        <div class="card bar-chart-example screenScroll "
          style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6); height: 100%;">
          <div style="padding: 2px;" style="height: 500px;">
            <h3 style="font-weight: bold;
            color: black;
            font-family: 'Roboto';
            font-size: 1.2vw !important;
            margin-left: 3px">Site Comfort Performance Trend</h3>
            <h3 *ngIf="getSiteComfortData?.length == 0" style="position: absolute;
  z-index: 1;
  top: 40%;
  left: 40%;
  font-size: 30px;
  color: #888;">Not Available</h3>
            <div class="tabs" *ngIf="getSiteComfortData?.length > 0"
              style="width: 190px; font-family: Calibri Light !important;">
              <p style="margin-right: 5px;">Period:</p>
              <p (click)="changeMonthTabSiteComfort('1M')"
                [ngStyle]="{'border-bottom': IsOneMonthSiteComfort ? '2px solid #888' : 'none' }">
                1M</p>
              <p>|</p>
              <p (click)="changeMonthTabSiteComfort('3M')"
                [ngStyle]="{'border-bottom': IsThreeMonthSiteComfort ? '2px solid #888' : 'none' }">
                3M</p>
              <p>|</p>
              <p (click)="changeMonthTabSiteComfort('6M')"
                [ngStyle]="{'border-bottom': IsSixMonthSiteComfort ? '2px solid #888' : 'none' }">
                6M</p>
              <p>|</p>
              <p (click)="changeMonthTabSiteComfort('13M')"
                [ngStyle]="{'border-bottom': IsTwelveMonthComfort ? '2px solid #888' : 'none' }">
                13M</p>
            </div>

            <div style="position: absolute; top: 49px; left: 39%; font-family: Calibri Light !important;"
              *ngIf="getSiteComfortData?.length > 0">
              <div class="tabs" style="justify-content: flex-start; width: 240px;">
                <p style="margin-right: 5px;">Moving Trendline:</p>
                <p (click)="changeMovingAverageTrending('3D')" style="margin-right: 2px;"
                  [ngStyle]="{'border-bottom': IsThreeDayMovingAverage ? '2px solid #888' : 'none' }">3D</p>
                <p>|</p>
                <p (click)="changeMovingAverageTrending('1W')" style="margin-left: 2px; margin-right: 2px;"
                  [ngStyle]="{'border-bottom': IsOneWeekMovingAverage ? '2px solid #888' : 'none' }">1W</p>
                <p *ngIf="!IsOneMonthSiteComfort">|</p>
                <p *ngIf="!IsOneMonthSiteComfort" (click)="changeMovingAverageTrending('1M')" style="margin-left: 2px;"
                  [ngStyle]="{'border-bottom': IsOneMonthMovingAverage ? '2px solid #888' : 'none' }">1M</p>
                <p *ngIf="IsTwelveMonthComfort">|</p>
                <p *ngIf="IsTwelveMonthComfort" (click)="changeMovingAverageTrending('3M')" style="margin-left: 2px;"
                  [ngStyle]="{'border-bottom': IsThreeMonthMovingAverage ? '2px solid #888' : 'none' }">3M</p>
              </div>
            </div>

            <div *ngIf="getSiteComfortData?.length > 0" style="position: absolute; top: 46px; right: 25px; font-weight: bold;
    font-size: 0.98vw;">
              <span style="font-family: Roboto;"
                [ngStyle]="{'color': getTwelveMonthChangePercentage >= 0 ? 'rgb(13, 192, 255)' : 'rgb(13, 192, 255)'}">{{
                getTwelveMonthChangePercentage >= 0 ? '+' : '' }}{{ getTwelveMonthChangePercentage | number:'1.0-2' }}
                %</span><span style="margin-left: 10px; font-family: Roboto;">{{movingAverageCount}} Month Change</span>
            </div>

            <kendo-chart [seriesColors]="['#5a5a5a']" style="height: 388px" *ngIf="getSiteComfortData?.length == 0"
              class="site_comfort_chart">
              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                  [minorTicks]="{ visible: false }">
                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                  </kendo-chart-value-axis-item-labels>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item>
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                    [rotation]="-30">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-legend>
              </kendo-chart-legend>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="OneMonthSiteData" field="TotalPctComfort"
                  [missingValues]="'interpolate'" categoryField="AllDates">
                </kendo-chart-series-item>
              </kendo-chart-series>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item type="category" [labels]="{format: 'EEEEE', font: '5' , skip: '0'}"
                  [majorGridLines]="{ visible: false }">
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                  {{ category | date }}: {{ value }}
                </ng-template>
              </kendo-chart-tooltip>
            </kendo-chart>


            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 388px"
              *ngIf="IsOneMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                  [minorTicks]="{ visible: false }" [labels]="{font: '13px Roboto !important'}">
                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                  </kendo-chart-value-axis-item-labels>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item>
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                    [rotation]="-30">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-legend>
              </kendo-chart-legend>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="OneMonthSiteData" field="TotalPctComfort"
                  categoryField="AllDates">
                </kendo-chart-series-item>

                <kendo-chart-series-item type="line" [data]="getOneMonthMovingAverage" field="TotalPctComfort"
                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

              </kendo-chart-series>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item type="category" [labels]="{format: 'EEEEE', font: '5' , skip: '0'}"
                  [majorGridLines]="{ visible: false }">
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important"
                    [content]="labelContentOfOneMonth"></kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                  {{ category | date }}: {{ value }}
                </ng-template>
              </kendo-chart-tooltip>
            </kendo-chart>

            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 388px"
              *ngIf="IsThreeMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                  [minorTicks]="{ visible: false }">
                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                  </kendo-chart-value-axis-item-labels>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item>
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                    [rotation]="-30">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-legend>
              </kendo-chart-legend>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="ThreeMonthSiteData" field="TotalPctComfort"
                  [missingValues]="'interpolate'" categoryField="AllDates">
                </kendo-chart-series-item>

                <kendo-chart-series-item type="line" [data]="getThreeMonthMovingAverage" field="TotalPctComfort"
                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

              </kendo-chart-series>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                type="category" [majorGridLines]="{ visible: false }">
                <kendo-chart-category-axis-item-labels [content]="customDivisionsForThreeMonths"></kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>

              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                  {{ category | date }}: {{ value }}
                </ng-template>
              </kendo-chart-tooltip>
            </kendo-chart>


            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 388px"
              *ngIf="IsSixMonthSiteComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                  [minorTicks]="{ visible: false }">
                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                  </kendo-chart-value-axis-item-labels>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item>
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                    [rotation]="-30">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-legend>
              </kendo-chart-legend>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="SixMonthSiteData" field="TotalPctComfort"
                  [missingValues]="'interpolate'" categoryField="AllDates">
                </kendo-chart-series-item>

                <kendo-chart-series-item type="line" [data]="getSixMonthMovingAverage" field="TotalPctComfort"
                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

              </kendo-chart-series>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                  type="category" [majorGridLines]="{ visible: false }">
                  <kendo-chart-category-axis-item-labels [content]="customDivisionsForSixMonths"></kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>

              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                  {{ category | date }}: {{ value }}
                </ng-template>
              </kendo-chart-tooltip>
            </kendo-chart>

            <kendo-chart [seriesColors]="['#5a5a5a', 'rgb(125,221,255)']" style="height: 388px"
              *ngIf="IsTwelveMonthComfort && (getSiteComfortData?.length > 0)" class="site_comfort_chart">
              <kendo-chart-plot-area background="#F8F8F8"> </kendo-chart-plot-area>
              <kendo-chart-value-axis>
                <kendo-chart-value-axis-item [min]="0" [max]="100" [majorGridLines]="{ visible: true }"
                  [minorTicks]="{ visible: false }">
                  <kendo-chart-value-axis-item-labels font="13px Roboto !important" [content]="SiteComfortlabelContent">
                  </kendo-chart-value-axis-item-labels>
                </kendo-chart-value-axis-item>
              </kendo-chart-value-axis>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item>
                  <kendo-chart-category-axis-item-labels font="13px Calibri Light !important" format="p"
                    [rotation]="-30">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>
              <kendo-chart-legend>
              </kendo-chart-legend>
              <kendo-chart-series>
                <kendo-chart-series-item type="column" [data]="TwelveMonthSiteData" field="TotalPctComfort"
                  [missingValues]="'interpolate'" categoryField="AllDates">
                </kendo-chart-series-item>

                <kendo-chart-series-item type="line" [data]="getTwelveMonthMovingAverage" field="TotalPctComfort"
                  categoryField="AllDates" [style]="normal" [markers]="{ visible: false }" [width]="4">
                  <kendo-chart-series-item-tooltip>
                    <ng-template let-value="value" let-category="category">
                      {{ category | date }}, Moving Average : {{value | number:'1.0-2' }}
                    </ng-template>
                  </kendo-chart-series-item-tooltip>
                </kendo-chart-series-item>

              </kendo-chart-series>
              <kendo-chart-category-axis>
                <kendo-chart-category-axis-item [labels]="{font: '13px Calibri Light !important'}"
                  type="category" [majorGridLines]="{ visible: false }">
                  <kendo-chart-category-axis-item-labels [content]="customDivisionsForTwelveMonths">
                  </kendo-chart-category-axis-item-labels>
                </kendo-chart-category-axis-item>
              </kendo-chart-category-axis>

              <kendo-chart-tooltip>
                <ng-template kendoChartSeriesTooltipTemplate let-category="category" let-value="value">
                  {{ category | date }}: {{ value }}
                </ng-template>
              </kendo-chart-tooltip>
            </kendo-chart>

            <ul *ngIf="getSiteComfortData?.length > 0" class="legend" style="margin-top: 20px;">
              <li class="legend-item" (click)="toggleSeries()"
                style="font-size: 15px; color: #888888; font-family: Calibri Light;">
                <span class="legend-marker"></span>
                Daily building comfort during occupancy (zones within 2°c of set point, average across all zones)
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div> -->

    <!-- <div class="row align-items-md-stretch section_3" style="margin-top: 12px; ">

      <div class="col-md-12 problem_proactively" style="border-radius: 0;">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6); border-radius: 7px;">
          <div style="height: 990px; padding: 28px; display: flex;flex-direction: column;">
            <div style="height:100vh;margin-top:1vh;">
              <h3 style="font-weight: bold;
              color: black;
              margin-left: 6px;
              font-size: 1.2vw;
              font-family: 'Roboto';
              margin-top: -1.4vh;">Individual Equipment Thermal Comfort
                Performance</h3>
              <div style="display: flex; align-items: center; width:100%">

                <div id="EquipContainer" style="width: 100vw"></div>
              </div>
            </div>
            <div style="height: 472px;">
              <kendo-grid style="height: 100%;margin-top: -1vh" [data]="equipComfortGrid" [selectable]="true"
                class="gridFontStyle awaiting-grid scroll-container" (selectionChange)="isRowSelected($event)"
                [sortable]="{ allowUnsort: true, mode: 'multiple' }" [sortable]="{allowUnsort: true, mode:'multiple'}"
                (dblclick)="getDetail()" [skip]="EquipComfortState.skip" [sort]="EquipComfortState.sort"
                [filter]="EquipComfortState.filter" [resizable]="true" filterable="menu"
                (dataStateChange)="dataStateEquipmentComfort($event)" [navigable]="true">

                <kendo-grid-column field="Building" title="Building" width="160" [minResizableWidth]="160">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.Building}}
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="Equipment" title="Equipment" width="145" [minResizableWidth]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.Equipment }}
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="PerOfOccupancy" title="% of Occupancy" width="145" [minResizableWidth]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.PerOfOccupancy }}<span>&nbsp;%</span>
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="TotalHours" title="Total (Hrs)" width="115" [minResizableWidth]="110">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.TotalHours | number: '1.0-0'}}
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="TooHotHours" title="Too Hot (Hrs)" width="123" [minResizableWidth]="110">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.TooHotHours | number: '1.0-0'}}
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="TooColdHours" title="Too Cold (Hrs)" width="128" [minResizableWidth]="110">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div style="font-family: Calibri Light !important;">
                      {{dataItem.TooColdHours | number: '1.0-0'}}
                    </div>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="TasksCompleted" title="Tasks Completed" width="220" [minResizableWidth]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <ng-container *ngIf="dataItem?.TasksCompleted !== null; else noTasks">
                      <a class="taskCompleted"
                        (click)="GoToCompletedTable(dataItem)">{{'View Tasks Completed' + ' &ndash; ' + dataItem?.TasksCompleted }}</a>
                    </ng-container>
                    <ng-template #noTasks>--</ng-template>
                  </ng-template>
                </kendo-grid-column>
                <kendo-grid-column field="BacklogTasks" title="Tasks In Backlog" width="235" [minResizableWidth]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <ng-container *ngIf="dataItem?.BacklogTasks !== null; else noTasks">
                      <a class="taskBacklogged"
                        (click)="GoToBacklogTable(dataItem)">
                        {{'View Tasks In The Backlog' + ' &ndash; ' + dataItem.BacklogTasks }}
                      </a>
                    </ng-container>
                    <ng-template #noTasks>--</ng-template>
                  </ng-template>
                </kendo-grid-column>

              </kendo-grid>
            </div>
          </div>
        </div>
      </div>
    </div> -->

    <!-- <div class="row align-items-md-stretch section_3">
      <div class="col-md-12 problem_proactively" style="border-radius: 0;">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6);
        border-radius: 7px;
        width: 100%;
        height: 55px;
        background: rgb(0, 17, 50);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;">
          IM Data-Driven Task Backlog
        </div>
      </div>

    </div> -->


    <!-- <div class="row align-items-md-stretch section_3">

      <div class="col-md-12 problem_proactively" style="border-radius: 0;">
        <div class="card bar-chart-example" style="box-shadow: 0 8px 20px 0 rgba(218,224,235,.6); border-radius: 7px;">
          <div style="height: 500px; padding: 20px;">
            <h3 style="font-weight: 400;
            color: #888888;
            margin-left: 10px; font-size: 20px !important; margin-top: 10px;">Equipment Running Too Hot / Cold</h3>
            <div style="display: flex; align-items: center;">
              <div id="container3" style="width: 100%"></div>
            </div>
          </div>
        </div>
      </div>
    </div> -->

  </div>

</section>

<admin-footer class="dashboardFooterCss"></admin-footer>
<app-scroll-top></app-scroll-top>

<kendo-dialog *ngIf="showPopup" class="DasBoardDialog" (close)="closePopup('cancel')" [minWidth]="300">
  <kendo-dialog-titlebar>
    <div style="font-size: 18px; line-height: 1.3em;font-family: Roboto !important;">
      Technician Summary<br>
    </div>
  </kendo-dialog-titlebar>
  <div style="font-size: 14px; padding: 10px;font-family: Roboto !important;">{{popupContent}}</div>
</kendo-dialog>

<kendo-dialog *ngIf="showPopupTaskDescription" class="DasBoardDialog" (close)="closePopupTaskDescription('cancel')" [minWidth]="300">
  <kendo-dialog-titlebar>
    <div style="font-size: 18px; line-height: 1.3em;font-family: Roboto !important;">
      Task Description<br>
    </div>
  </kendo-dialog-titlebar>
  <div style="font-size: 14px; padding: 10px;font-family: Roboto !important;white-space: pre-line;">{{popupTaskDescriptionContent}}</div>
</kendo-dialog>


<kendo-dialog *ngIf="showOutcomePopup ||showBacklogOutcomePopup" class="DasBoardDialog"
  style="font-family: Roboto !important;" (close)="closeOutcomePopup('cancel')" [minWidth]="300">
  <kendo-dialog-titlebar>
    <div style="font-size: 18px; line-height: 1.3em;font-family: Roboto !important;">
      Change Outcome<br>
    </div>
  </kendo-dialog-titlebar>
  <div style="padding: 20px;">
    <div style="display: flex; flex-direction: column;">
      <label style="font-size: 14px;
  color: darkblue;
  font-family: Roboto !important;
  font-weight: 500;">Outcome</label>
      <kendo-dropdownlist class="outcomeDrop" style="width: 20vw;
  border: 1px solid lightgray;
  font-family: Roboto !important;
  outline: none;
  border-bottom-color: lightgray;
  border-radius: 4px;" [data]="outcomeDropData" textField="key" valueField="value" [popupSettings]="{ width: 'auto' }"
        #outcome name="outcome" [(ngModel)]="outcomeDropn" [valuePrimitive]="true"></kendo-dropdownlist>
    </div>


    <label style="font-size: 14px;
      color: darkblue;
      font-family: Roboto !important;
      font-weight: 500; margin-top: 20px;">Outcome Summary</label>
    <div style="display: flex; flex-direction: row; align-items: center; justify-content: space-between;">
      <textarea kendoTextArea class="k-textarea"
        style="width: 80%; height: 80px; overflow: scroll;font-family: Roboto !important; padding: 6px 7px !important"
        [autoSize]="false" [(ngModel)]="inputSummary"></textarea>
      <button title="Add Summary"
        style="width: 40px;
        padding: 5px;
        font-size: 20px;
        height: 40px;
        margin-left: 30px;
        color: white;
        font-family: Roboto !important;
        background: #00ccff !important;
        border: none;
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12); display: flex; align-items: center; justify-content: center; outline: 0;"
        (click)="addSummary()">
        <i class="fa fa-plus"></i>
      </button>

      <button title="Undo"
        style="width: 40px;
        padding: 5px;
        font-size: 20px;
        height: 40px;
        margin-left: 30px;
        color: white;
        background: #ff0000bd !important;
        border: none;
        font-family: Roboto !important;
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12); display: flex; align-items: center; justify-content: center; outline: 0;"
        (click)="undoLastSummary()">
        <i class="fa fa-reply"></i>
      </button>
    </div>


    <div style="display: flex; flex-direction: column; margin-top: 20px;">
      <textarea kendoTextArea class="k-textarea"
        style="width: 100%;font-family: Roboto !important; height: 160px; overflow: scroll; padding: 6px 7px !important; font-size: 14px;"
        [autoSize]="false" [(ngModel)]="outcomeSummary" readonly></textarea>
    </div>

    <div style="display: flex; justify-content: space-between; margin-top: 20px;">
      <button kendoButton class="buttonFooter"
        style="background-color: #00ccff !important;font-family: Roboto !important;" primary="true"
        (click)="closeOutcomePopup('cancel')">Cancel</button>
      <button kendoButton class="buttonFooter" style="font-family: Roboto !important;" [ngClass]="{
        'button-disabled': addedSummaries.length === 0,
        'button-enabled': addedSummaries.length > 0
      }" primary="true" (click)="saveOutcome(showBacklogOutcomePopup)"
        [disabled]="addedSummaries.length === 0">Save</button>
    </div>
  </div>
</kendo-dialog>


<kendo-dialog *ngIf="showPopupOutcome" class="DasBoardDialog" (close)="closePopupOutcome('cancel')" [minWidth]="500">
  <kendo-dialog-titlebar>
    <div style="font-size: 18px; line-height: 1.3em;font-family: Roboto !important;">
      Outcome Summary<br>
    </div>
  </kendo-dialog-titlebar>
  <div style="font-size: 14px; padding: 10px; white-space: pre-wrap;font-family: Roboto !important; height: 250px;">
    {{popupOutcomeContent}}</div>
</kendo-dialog>
<div *ngIf="isPrioritization" [ngClass]="'custom-dialog'">
  <kendo-dialog *ngIf="isPrioritization" (close)="closePriority('cancel')" [width]="372"
    style="height: 90vh; z-index:99999 !important; margin-top:20px">
    <kendo-dialog-titlebar style="width: 100.5% !important;
    border-width: 0 0 1px !important; background: #F2F2F2 !important;">
      <div style="font-size: 1vw; padding-top:0.6vh;color:black;line-height: 1.3em; font-family: Roboto !important;">
        Priority & Focus
      </div>
    </kendo-dialog-titlebar>
    <!-- <div class="priority-container">
    <div class="box" *ngFor="let item of priorityItems; let i = index">
      <img [src]="item.icon" [alt]="item.label + ' Icon'" class="priority-icon">

      <span class="item-label" [innerHTML]="item.label.split(' ').join('<br/>')"
        style="color: darkblue; font-weight: 400;"></span>

      <span class="priority"
        [ngClass]="{ 'high': item.priority === 'High', 'medium': item.priority === 'Medium', 'low': item.priority === 'Low' }"
        [attr.draggable]="true" (dragstart)="onDragStart($event, i)" (dragover)="onDragOver($event)"
        (drop)="onDrop($event, i)">
        <span
          [ngClass]="{ 'high-dot': item.priority === 'High', 'medium-dot': item.priority === 'Medium', 'low-dot': item.priority === 'Low' }"></span>
        {{ item.priority }}
      </span>
    </div>
    <button kendoButton style="width: 100%;" (click)="ApplyPriority()">Apply</button>
  </div> -->

    <!-- <div class="box" style="display: inline-block; padding: 0;">

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: -12vh;">

      <div style="position: relative; display: inline-block; height: 40vh; margin-bottom: 3vh;">
        <kendo-chart (seriesClick)="onSeriesClick($event)">
          <kendo-chart-series>
            <kendo-chart-series-item type="donut" [data]="data" categoryField="kind" field="share" [startAngle]="0">
              <kendo-chart-series-item-labels [content]="labelContent2" color="#fff" background="none">
              </kendo-chart-series-item-labels>
            </kendo-chart-series-item>
          </kendo-chart-series>
          <kendo-chart-legend [visible]="false"></kendo-chart-legend>
        </kendo-chart>

        <div class="arrow-container" [style.transform]="'rotate(' + selectedAngle + 'deg)'" style=" position: absolute;
        top: 64%;
        left: 44%;
        transform-origin: center center;
        width: 35px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        transform: rotate(0deg);">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M12.75 20C12.75 20.4142 12.4142 20.75 12 20.75C11.5858 20.75 11.25 20.4142 11.25 20L11.25 10.75H6.00002C5.69668 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75H12.75L12.75 20Z"
                fill="#1C274C"></path>
            </g>
          </svg>
        </div>

        <span class="item-label"
          style="position: absolute; top: 81%; left: 50%; transform: translate(-50%, -50%); color: darkblue; font-weight: 400; z-index:9999">
          Equipment Reliability
        </span>
      </div>


    </div>

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: -12vh;">


      <div style="position: relative; display: inline-block; height: 40vh; margin-bottom: 3vh;">
        <kendo-chart (seriesClick)="onSeriesClick($event)">
          <kendo-chart-series>
            <kendo-chart-series-item type="donut" [data]="data" categoryField="kind" field="share" [startAngle]="0">
              <kendo-chart-series-item-labels [content]="labelContent2" color="#fff" background="none">
              </kendo-chart-series-item-labels>
            </kendo-chart-series-item>
          </kendo-chart-series>
          <kendo-chart-legend [visible]="false"></kendo-chart-legend>
        </kendo-chart>

        <div class="arrow-container" [style.transform]="'rotate(' + selectedAngle + 'deg)'" style=" position: absolute;
        top: 64%;
        left: 44%;
        transform-origin: center center;
        width: 35px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        transform: rotate(0deg);">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M12.75 20C12.75 20.4142 12.4142 20.75 12 20.75C11.5858 20.75 11.25 20.4142 11.25 20L11.25 10.75H6.00002C5.69668 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75H12.75L12.75 20Z"
                fill="#1C274C"></path>
            </g>
          </svg>
        </div>

        <span class="item-label"
          style="position: absolute; top: 81%; left: 50%; transform: translate(-50%, -50%); color: darkblue; font-weight: 400;z-index:999;">
          Thermal Comfort
        </span>
      </div>

    </div>

    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: -12vh;">


      <div style="position: relative; display: inline-block; height: 40vh; margin-bottom: 3vh;">
        <kendo-chart (seriesClick)="onSeriesClick($event)">
          <kendo-chart-series>
            <kendo-chart-series-item type="donut" [data]="data" categoryField="kind" field="share" [startAngle]="0">
              <kendo-chart-series-item-labels [content]="labelContent2" color="#fff" background="none">
              </kendo-chart-series-item-labels>
            </kendo-chart-series-item>
          </kendo-chart-series>
          <kendo-chart-legend [visible]="false"></kendo-chart-legend>
        </kendo-chart>

        <div class="arrow-container" [style.transform]="'rotate(' + selectedAngle + 'deg)'" style=" position: absolute;
        top: 64%;
        left: 44%;
        transform-origin: center center;
        width: 35px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        transform: rotate(0deg);">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M12.75 20C12.75 20.4142 12.4142 20.75 12 20.75C11.5858 20.75 11.25 20.4142 11.25 20L11.25 10.75H6.00002C5.69668 10.75 5.4232 10.5673 5.30711 10.287C5.19103 10.0068 5.25519 9.68417 5.46969 9.46967L11.4697 3.46967C11.6103 3.32902 11.8011 3.25 12 3.25C12.1989 3.25 12.3897 3.32902 12.5304 3.46967L18.5304 9.46967C18.7449 9.68417 18.809 10.0068 18.6929 10.287C18.5768 10.5673 18.3034 10.75 18 10.75H12.75L12.75 20Z"
                fill="#1C274C"></path>
            </g>
          </svg>
        </div>

  <span class="item-label"
    style="position: absolute; top: 81%; left: 50%; transform: translate(-50%, -50%); color: darkblue; font-weight: 400;">
    Energy Waste
  </span>
  </div>

  </div>
  </div> -->

    <div>
      <h4 style="font-family: 'Roboto';
    font-weight: normal;
    font-size: 0.8vw;margin-top:10px">Align our technician's focus with your objectives by prioritising maintenance
        outcomes.</h4>
    </div>
    <div id="EQREVContainer" style="width: 100%; height: 34vh;"></div>
    <div id="ThermalContainer" style="width: 100%; height: 34vh;margin-top: -13vh;"></div>
    <div id="EnergyContainer" style="width: 100%; height: 34vh;margin-top: -13vh;"></div>
    <div>
      <button kendoButton style="width: 100%;font-family: Roboto !important; margin-top:-17vh"
        (click)="ApplyPriority()">Apply</button>
    </div>
  </kendo-dialog>
</div>


<kendo-dialog *ngIf="isEquipment" (close)="closeEquipment('cancel')" [width]="312">
  <kendo-dialog-titlebar style="width: 100.5% !important;
    border-width: 0 0 1px !important;">
    <div style="font-size: 19px; line-height: 1.3em;font-family: Roboto !important;">
      Equipment
    </div>
  </kendo-dialog-titlebar>
  <div class="equipment-container">
    <h2 style="font-size: 16px;
    color: darkblue;
    font-weight: 600;
    font-family: Roboto !important;
    margin-left: -10px;">Search Equipment</h2>

    <input type="text" placeholder="Search Equipment" [(ngModel)]="searchTerm" (input)="filterEquipment()">

    <div style="max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    margin-top: 10px;">
      <h2 style="font-size: 16px;
    color: darkblue;
    font-weight: 600;
    font-family: Roboto !important;
    margin-left: -10px;">Select Equipment</h2>
      <div class="select-all">
        <label>
          <hr />
          <input type="checkbox" [(ngModel)]="selectAll" (change)="toggleSelectAll()">
          {{ selectAll ? '(Unselect All)' : '(Select All)' }}
        </label>
      </div>

      <div class="equipment-list">
        <label *ngFor="let item of filteredEquipment">
          <hr /><input type="checkbox" [(ngModel)]="item.selected" (ngModelChange)="updateSelectAll()"> {{ item.name }}
        </label>
      </div>
    </div>

    <div style="font-family: Roboto !important;" class="equip-buttons">
      <button (click)="confirmSelection()">OK</button>
      <button (click)="cancelSelection()">Cancel</button>
    </div>
  </div>

</kendo-dialog>
