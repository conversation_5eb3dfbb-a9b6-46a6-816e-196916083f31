import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from "../../Services/authentication.service";
import { ActivatedRoute } from "@angular/router";
import { Title } from "@angular/platform-browser";
import { Router } from '@angular/router';
import { SessionStorageService } from 'ngx-webstorage';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { TotalEquipmentsModel } from "./userInformation.model";
import { CustomersEntities, CustomerLocationByName } from "../../Common/header/header.model";
 import {  State } from '@progress/kendo-data-query';
import { first } from 'rxjs/operators';
import { otpToken } from 'src/app/Common/shared';
import { ReCaptchaV3Service } from 'ng-recaptcha';
import { NavigationLoggerService } from 'src/app/Services/navigation-logger.service';

@Component({
  selector: 'admin-userInformation',
  templateUrl: './userInformation.component.html',
  styleUrls: ['./userInformation.component.scss']
})
export class UserInformationComponent implements OnInit {
  public state: State = {
    skip: 0,
    take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  hasAllValueSelected: boolean = false;
  customerLocation = [];
  CustomerName: string;
  hasOneCustomer: boolean;
  LoggedInUserName: string;
  totalEquiments: TotalEquipmentsModel[] = []
  locationList: CustomersEntities[] = [];
  // events
  obj = {};
  public listItems: CustomerLocationByName[] = [];
  public selectedLocationList: Array<any> = [];
  userId: number;
  public otpToken : otpToken = new otpToken();

  constructor(private sessionStorage: SessionStorageService, private titleService: Title, private activatedRoute: ActivatedRoute, private router: Router, private coredata: CoreDataService, private authservice: AuthenticationService, private shareData: SharedDataService,private recaptchaV3Service: ReCaptchaV3Service, private navigationLoggerService: NavigationLoggerService) {
    let Token = this.authservice.getTokenData();
    this.LoggedInUserName = Token.Name;
    this.userId = parseInt(Token.UserID);
    let pageTite = this.activatedRoute.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
    this.loadCustomerLocationDropdown(this.userId);
    this.shareData.removeBacklogData();
  }
  ngOnInit() {


  }
  loadCustomerLocationDropdown(id) {
   this.customerLocation = [];
    this.coredata.getcustomerLocationWithTotalEquipments(id).pipe(first()).subscribe(res => {
      if (res[1].StatusCode === 200) {
        this.totalEquiments = res[1].response;
      }
      if (res[0].StatusCode === 200) {
        this.locationList = res[0].response;

        this.listItems = this.locationList;
        // this.sessionStorage.store('locations', this.locationList);
        let haveMultipleCustomers = [];
        this.locationList.map(element => {
          let indexofElement = haveMultipleCustomers.findIndex(data => data.CustomerID === element.CustomerID);
          if (indexofElement === -1) {
            haveMultipleCustomers.push(element);
          }
        });
        if (haveMultipleCustomers.length === 1) {
          this.hasOneCustomer = true;
          this.CustomerName = haveMultipleCustomers[0].CustName;
          let filterLocation: Array<CustomersEntities> = this.locationList.filter(el => el.CustomerID === haveMultipleCustomers[0].CustomerID);
          filterLocation.map(element => {
            let equipments = this.totalEquiments.find(totalEquipment => totalEquipment.ADRSCODE === element.LocationCode);
            if (!!equipments) {
              element["totalEquipments"] = equipments.TotalEquipment;
            }
            else if (!!equipments === false) {
              element["totalEquipments"] = 0;
            }

          });
          this.customerLocation = [{
            customerName: this.CustomerName,
            locations: filterLocation
            // TotalEquipments
          }];
     }
        else {
          haveMultipleCustomers.map(element => {
            let filterLocation: Array<CustomersEntities> = this.locationList.filter(el => el.CustomerID === element.CustomerID);
            filterLocation.map(element => {
              let equipments = this.totalEquiments.find(totalEquipment => totalEquipment.ADRSCODE === element.LocationCode);
              if (!!equipments) {
                element["totalEquipments"] = equipments.TotalEquipment;
              }
              else if (!!equipments === false) {
                 element["totalEquipments"] = 0;
              }

            });
            this.customerLocation.push({
              customerName: element.CustName,
              locations: filterLocation
            })
          });
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }

  logout() {
    // Remove tokens and profile and update login status subject
    //localStorage.removeItem('CPToken');
    this.navigationLoggerService.logNavigation(this.authservice.getTokenData(), 'logout');
    this.shareData.deleteAccessToken();

    this.sessionStorage.clear();
    this.shareData.showInfo('You have been succesfully logged out');
    this.router.navigate(['/']);
    this.authservice.setLoggedIn(false);
    this.shareData.setCustomerIDS([]);
    this.shareData.changeDateDefault(undefined);
    this.shareData.defaultvalue([]);
    this.shareData.setCustomerSummary([]);
  }

  changePassword(){
    this.recaptchaV3Service.execute("importantAction").subscribe((token) => {
      if (token) {
      // console.log(this.shareData.loginUsername);
      
        this.otpToken.RecaptchaToken = token;
        this.otpToken.userName = this.shareData.loginUsername;
        this.coredata
    .SendPasscode(this.otpToken)
    .subscribe((res) => {
      if (res["StatusCode"] === 200) {
       
        
       
        this.shareData.ChangePassword=true;
        
        this.router.navigate(["/TwoFactorAuthentication"]);
     } else {
       this.shareData.showInfo("Unable to send OTP");
     }
   });

      }
    });
    
    
    
  }

}

