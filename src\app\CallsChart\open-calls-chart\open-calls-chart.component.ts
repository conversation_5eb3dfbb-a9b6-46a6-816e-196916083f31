import { Component, OnInit, Input } from '@angular/core';
 import { SharedDataService } from "../../Services/shared-data.service";
import { Router } from '@angular/router';
import { ServiceCallsChartValues } from "./open-calls-chart.model";
 @Component({
  selector: 'app-open-calls-chart',
  templateUrl: './open-calls-chart.component.html',
  styleUrls: ['./open-calls-chart.component.scss']
})
export class OpenCallsChartComponent implements OnInit {
  count: boolean = false;
  Locations: Array<number> = [];
  public AwaitingApprove: any = [""];
  totalTMOpenService: number;
  TotalTMCompletionServices: number;
  @Input() startDate: any;
  @Input() endDate: any;
  public ApproveData = [];
  constructor(private shareData: SharedDataService, private router: Router) {
    // this.shareData.Token$.subscribe()
    this.shareData.removeBacklogData();
    this.shareData.customeridsasObservables.subscribe(data => {
      this.Locations = data;
      this.ApproveData = [];
      this.loadCount();
    });

  }
  ngOnInit() {
    // this.loadCount();
  }
  onSeries(event) {
    if (event != null && event != undefined) {
      this.router.navigate(["/ServiceCalls", ServiceCallsChartValues[event.category]]);
    }
  }
  loadCount() {
    this.shareData.customerSummaryAsObservable.subscribe(response => {
      this.ApproveData = [];
      this.AwaitingApprove = [""];
      this.count = true;
      this.totalTMOpenService = response.TotalTMOpenServices;
      this.TotalTMCompletionServices = response.TotalTMCompletionServices;
      this.ApproveData.push(this.totalTMOpenService);
      this.AwaitingApprove.push(this.TotalTMCompletionServices);
   });
  }
}
