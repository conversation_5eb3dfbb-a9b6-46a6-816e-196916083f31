import { Injectable } from '@angular/core';
// import {Http, Response, RequestOptions, Headers} from "@angular/http"import
import { HttpHeaders, HttpClient } from "@angular/common/http";
import { BehaviorSubject } from 'rxjs';
import { Router } from "@angular/router";
import 'rxjs/Rx';
import { JwtHelper } from 'angular2-jwt';
import { tokenNotExpired } from 'angular2-jwt';
import { SharedDataService } from "../Services/shared-data.service";
import { NavigationLoggerService } from './navigation-logger.service';
@Injectable()
export class AuthenticationService {
  public loggedIn: boolean = false;
  public UserInfo: any = undefined;
  public userInfo$: BehaviorSubject<any> = new BehaviorSubject<any>(this.UserInfo);
  headers = new HttpHeaders();
  TokenValue: any;
  globals: any;
  // private loggedInSource = new Subject<string>();
  // loggedIn$ = this.loggedInSource.asObservable();
  public loggedIn$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(this.loggedIn);
  constructor(private http: HttpClient, private router: Router,
    private sharedDataService: SharedDataService, private navigationLoggerService: NavigationLoggerService) { }

  _setSession(authResult) {
    //localStorage.setItem('CPToken', authResult.access_token);
    this.sharedDataService.setAccessToken(authResult.access_token);

    this.setLoggedIn(true);
  }
  getTokenValue() {
    //let token = localStorage.getItem('CPToken');
    let token = this.sharedDataService.getAccessToken();

    return this.jwtHelper.decodeToken(token);
  }
  jwtHelper: JwtHelper = new JwtHelper();
  ExpiryTime: number = 0;
  useJwtHelper() {
    //var token = localStorage.getItem('CPToken');
    let token = this.sharedDataService.getAccessToken();

    let tokenVal = this.jwtHelper.decodeToken(token);
    let isTokenExpired = this.jwtHelper.isTokenExpired(token);
    let tokenExpirationDate: any = this.jwtHelper.getTokenExpirationDate(token);
    let currentDateTime: any = new Date();
    this.ExpiryTime = tokenExpirationDate - currentDateTime;
    let Token = {
      "UserInfo": tokenVal,
      "ExpirationTime": tokenExpirationDate,
      "TokenStatus": isTokenExpired

    };

    this.sharedDataService.broadcastToken(Token);
    this.sharedDataService.UserInfo = tokenVal;

    var handleToken = setInterval(() => {
      if (this.jwtHelper.isTokenExpired(token)) {
        this.router.navigate([""]).then(() => {
          //     this.sharedDataService.showInfo("Session Timed Out. Please Login Again!");
          clearInterval(handleToken);
          localStorage.clear();
          this.loggedIn$.next(false);
        });
      }
    }, (this.ExpiryTime + 200));
    this.userInfo$.next(Token);

  }
  setLoggedIn(value) {
    if (!value) {
      this.navigationLoggerService.reset();
      return;
    }
    this.useJwtHelper();
    // Update login status subject
    this.loggedIn$.next(value);
    this.loggedIn = value;

    // this.router.navigate(['dashboard']);
  }

  isAuthenticated() {

    //return !!localStorage.getItem('CPToken');
    let token = this.sharedDataService.getAccessToken();
    return !!token;
  }

  isLoggedIn() {
    return this.loggedIn$.asObservable();
  }


  get authenticated() {
    //return tokenNotExpired('CPToken');
    if (window.location.hostname == 'localhost') {
      return tokenNotExpired('CPToken');
    }
    if (window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTER") {
      return tokenNotExpired('CPTokenAIR');

    } else if (window.location.hostname.split(".")[1].toUpperCase() == "AIRMASTERFIRE") {
      return tokenNotExpired('CPTokenAFS');
    }
    else if (window.location.hostname.split(".")[1].toUpperCase() == "OPTIMUMAIR") {
      return tokenNotExpired('CPTokenOPT');
    }
    else if (window.location.hostname.split(".")[1].toUpperCase() == "CONTROLCO") {
      return tokenNotExpired('CPTokenControlco');
    }
  }
  getTokenData() {
    //var token = localStorage.getItem('CPToken');
    var token = this.sharedDataService.getAccessToken();
    let tokenVal = this.jwtHelper.decodeToken(token);
    return tokenVal;
  }
}


