import { Component, OnInit, HostListener } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import { Title } from "@angular/platform-browser";
import { OpenJCListModel, GridListModel, locationDateFilterEntity } from "./open-jc.model";
import * as moment from 'moment';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { ActivatedRoute } from "@angular/router";
import { GridDataResult, DataStateChangeEvent, FilterService } from '@progress/kendo-angular-grid';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'app-open-jc',
  templateUrl: './open-jc.component.html',
  styleUrls: ['./open-jc.component.scss']
})
export class OpenJCComponent implements OnInit {
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  listByCategory: string;
  locationsIds: string;
  originalDate = new Date();
  startDate: string;
  endDate: string;
  start: Date;
  end: Date;
  public range = {};
  OpenJCCCallsList: OpenJCListModel[] = [];
  gridView: GridListModel[] = [];
  public state: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridData: GridDataResult;
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public DataList: any = [];
  public filterGrid: any[];
  pagesize: number = 20;
  private dataFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  fromDateFilter: any;
  constructor(private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute) {
    this.allData = this.allData.bind(this);
    this.shareData.removeBacklogData();
    this.listByCategory = this.route.snapshot.paramMap.get('value');
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.locationDateFilterData === null) {
      if(this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == ""){
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else{
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locationsIds = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }

    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
  }
  public division: any[];
  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;

    }
    this.loadData(this.locationsIds, this.range);
  }
  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridData = process(this.OpenJCCCallsList, this.state);
  }

  setDataInLocalStorage() {
    // this.shareData.changeDateDefault({ start: this.range["start"], end: this.range["end"] });
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locationsIds
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }
  loadData(ids, range) {
    let id: string;
    if (ids === undefined) {
      id = "";
    }
    else {
      id = ids;
    }
    this.coredata.GetJCServiceCalls(id, range.start, range.end).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          this.dataGrid = res.response;
        
          this.division = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Divisions === x.Divisions) === idx);
          this.division = this.division.filter(function (el) {
            return el.Divisions != "";
          });
         
          this.dataGrid.map(element => {
            element.StartDate = this.GetFormattedDate(element.StartDate);
            element.EndDate = this.GetFormattedDate(element.EndDate);
            element.ExpectedContract = Number.parseFloat(element.ExpectedContract.toString()).toFixed(2);
          });
          this.DataList = this.dataGrid;
          this.filterGrid = [];
          this.OpenJCCCallsList = [];
          if (this.listByCategory === "All") {
            this.filterGrid = this.dataGrid;
            this.state.filter.filters = [];
            this.loadGridData();
          }
          else if (this.listByCategory === "Service") {
            // CallStatus: "COMPLETE"
            this.state.filter.filters = [{ field: 'Divisions', operator: 'contains', value: "SERVICE" }];
            this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            this.loadGridData();
          }
          else if (this.listByCategory === "Controls") {
            this.state.filter.filters = [{ field: 'Divisions', operator: 'contains', value: "CONTROLS" }];
            this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            this.loadGridData();
          }
          else if (this.listByCategory === "Install") {
            this.state.filter.filters = [{ field: 'Divisions', operator: 'contains', value: "INSTALL" }];
            this.filterGrid = filterBy(this.DataList, this.state.filter.filters[0]);
            this.loadGridData();
          }
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
          // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
        }
      });
  }
// FK: Auto Load Scrolling
loadGridData(){

  if(this.filterGrid.length <= this.pagesize){
    this.isShow = false;
  }
  else{
    this.isShow = true;
  }
  this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "EndDate" }]);
  const next = this.OpenJCCCallsList.length;
  this.OpenJCCCallsList = [
    ...this.OpenJCCCallsList,
    ...this.filterGrid.slice(next, next + this.pagesize)
  ];
  this.state.sort = [{ dir: "desc", field: "EndDate" }];
  this.gridData = process(this.OpenJCCCallsList, this.state);
}

loadMore(): void {
  if(this.OpenJCCCallsList.length >= this.filterGrid.length - this.pagesize){
    setTimeout(()=>{
      this.isShow = false;
    }, 1500);
  }
  else{
    this.isShow = true;
  }
  if(this.OpenJCCCallsList.length == this.filterGrid.length){
    this.loading = false;
  } else{
    this.loading = true;
    const next = this.OpenJCCCallsList.length;
  this.OpenJCCCallsList = [
      ...this.OpenJCCCallsList,
      ...this.filterGrid.slice(next, next + this.pagesize)
  ];
  setTimeout(()=>{
    this.loading = false;
    this.state.sort = [{ dir: "desc", field: "EndDate" }];
    this.gridData = process(this.OpenJCCCallsList, this.state);
  }, 1500);
  }
}

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }
  editdate() {
    this.OpenJCCCallsList = [];
    this.setDataInLocalStorage();

    // this.shareData.changeDateDefault({ start: this.range["start"], end: this.range["end"] });
    this.loadData(this.locationsIds, this.range);
  }
  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locationsIds = this.locationDateFilterData.locations;
    }
    else {
      this.locationsIds = "";
    }
    this.range["start"] = new Date(this.locationDateFilterData.start);
    this.range["end"] = new Date(this.locationDateFilterData.end);
    this.loadData(this.locationsIds, this.range);
    this.OpenJCCCallsList = [];
    this.setDataInLocalStorage();
  }
  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;

    const result: ExcelExportData = {

      data: process(this.DataList, state).data
    };
    return result;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    //FK: set page height
    this.pageHeight = event.target.innerHeight - 233;
  }
     //FK: all filter
     public filterChange(filter: CompositeFilterDescriptor): void {
      if(filter.filters.length >= 1){
        this.filterGrid = [];
        this.filterGrid = filterBy(this.DataList, filter);
        if(this.filterGrid.length <= this.pagesize){
          this.isShow = false;
        }
        else{
          this.isShow = true;
        }
        this.OpenJCCCallsList = [];
        this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "EndDate" }]);
        const next = this.OpenJCCCallsList.length;
        this.OpenJCCCallsList = [
            ...this.OpenJCCCallsList,
            ...this.filterGrid.slice(next, next + this.pagesize)
          ];
        this.state.sort = [{ dir: "desc", field: "InvoiceDate" }];
        this.gridData = process(this.OpenJCCCallsList, this.state);
      }
      else {
        this.DataList = [];
        this.filterGrid = [];
        this.DataList = this.dataGrid;
        this.filterGrid = this.dataGrid;
        if(this.filterGrid.length <= this.pagesize){
          this.isShow = false;
        }
        else{
          this.isShow = true;
        }
        this.OpenJCCCallsList = [];
        this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "EndDate" }]);
        const next = this.OpenJCCCallsList.length;
        this.OpenJCCCallsList = [
            ...this.OpenJCCCallsList,
            ...this.filterGrid.slice(next, next + this.pagesize)
        ];
        this.state.sort = [{ dir: "desc", field: "EndDate" }];
        this.gridData = process(this.OpenJCCCallsList, this.state);
      }
    }
     //FK: filter
     public dataFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
      this.dataFilter.splice(
        0, this.dataFilter.length,
        ...flatten(filter).map(({ value }) => value)
      );
      return this.dataFilter;
  }

  public quoteTypeChange(values: any[], filterService: FilterService): void {
    filterService.filter({
      filters: values.map(value => ({
        field: 'Divisions',
        operator: 'eq',
        value
      })),
      logic: 'or'
    });
  }

 

}
