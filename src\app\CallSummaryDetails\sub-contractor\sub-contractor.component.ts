import { Component, OnInit, ViewChild, ViewEncapsulation, HostListener } from '@angular/core';
import { Title } from "@angular/platform-browser";
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import * as moment from 'moment';
import { ActivatedRoute } from "@angular/router";
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { SubContractorModel, locationDateFilterEntity } from "./sub-contractor.model";
import { GridDataResult, DataStateChangeEvent, FilterService } from '@progress/kendo-angular-grid';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'app-sub-contractor',
  templateUrl: './sub-contractor.component.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./sub-contractor.component.scss']
})
export class SubContractorComponent implements OnInit {
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  locationsIds: string;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  startDate: string;
  endDate: string;
  originalDate = new Date();
  public range = {};
  subContractorList: SubContractorModel[] = [];
  gridView = [];
  public state: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridData: GridDataResult;
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public DataList: any = [];
  public filterGrid: any[];
  pagesize: number = 20;
  private dropdownFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  public resolutions: any[];
  public callStatuses: any[];
  fromDateFilter: any;
  constructor(private spinner: SpinnerVisibilityService, private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute) {
    this.allData = this.allData.bind(this);
    this.shareData.removeBacklogData();
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.locationDateFilterData === null) {
      if(this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == ""){
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else{
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locationsIds = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);

  }

  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;

    }
    this.loadData(this.locationsIds, this.range);
  }

  setDataInLocalStorage() {
    // this.shareData.changeDateDefault({ start: this.range["start"], end: this.range["end"] });
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locationsIds
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }
  @ViewChild('daterange') public service;
  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridData = process(this.subContractorList, this.state);
  }

  DownloadSubContractorReport(data) {
    if (data.JobNumber != null && data.JobNumber != undefined) {
      let payload = {
        DocumentType: "Call Summary Report",
        PropertyType: "Service Job Number",
        PropertyValue: data.JobNumber
      };
      this.spinner.show();
      this.coredata.getDocLinkByID(payload).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              let result = res['_body'];
              let fileType = result.type.split('/')[1];
              let ReportName = res.headers.get('x-filename') + "." + fileType;
              this.shareData.showSuccess("Call Summary Document downloaded successfully");
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
              saveAs(blob, ReportName);
            }
            else if (res.status === 204) {
              this.shareData.showWarning("No Call Summary Document found for Service Call : " + data.JobNumber);
            }
            this.spinner.hide();

          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    }
  }

  loadData(ids, range) {
    let id: string;
    if (ids === undefined) {
      id = "";
    }
    else {
      id = ids;
    }
    this.coredata.getSubContractorApi(id, range.start, range.end).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          this.dataGrid = res.response;
          this.dataGrid.map(element => {
            element.Date = this.GetFormattedDate(element.Date);
          });
          this.resolutions = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.Resolution === x.Resolution) === idx);
          this.resolutions = this.resolutions.filter(function(el){
             return el.Resolution != "";
          });
          this.callStatuses = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.CallStatus === x.CallStatus) === idx);
          this.callStatuses = this.callStatuses.filter(function(el){
            return el.CallStatus != "";
          })
          this.DataList = this.dataGrid;
          this.filterGrid = this.dataGrid;
          if(this.filterGrid.length <= this.pagesize){
            this.isShow = false;
          }
          else{
            this.isShow = true;
          }
          this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
          const next = this.subContractorList.length;
          this.subContractorList = [
              ...this.subContractorList,
              ...this.filterGrid.slice(next, next + this.pagesize)
          ];
          this.state.filter.filters = [];
          this.state.sort = [{ dir: "desc", field: "Date" }];
          this.gridData = process(this.subContractorList, this.state);
        }
      }
    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
          // this.toastrService.error("error " + catchError.status + " " + catchError.statusText)
        }
      });
  }

// FK: auto load scrolling
loadMore(): void {
  if(this.subContractorList.length >= this.filterGrid.length - this.pagesize){
    setTimeout(()=>{
      this.isShow = false;
    }, 1500);
  }
  else{
    this.isShow = true;
  }
  if(this.subContractorList.length == this.filterGrid.length){
    this.loading = false;
  } else{
    this.loading = true;
    const next = this.subContractorList.length;
    this.subContractorList = [
      ...this.subContractorList,
      ...this.filterGrid.slice(next, next + this.pagesize)
    ];
  setTimeout(()=>{
    this.loading = false;
    this.state.sort = [{ dir: "desc", field: "Date" }];
    this.gridData = process(this.subContractorList, this.state);
  }, 1500);
  }
}

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }

  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locationsIds = this.locationDateFilterData.locations;
    }
    else {
      this.locationsIds = "";
    }
    this.range["start"] = new Date(this.locationDateFilterData.start);
    this.range["end"] = new Date(this.locationDateFilterData.end);
    this.loadData(this.locationsIds, this.range);
    this.setDataInLocalStorage();
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;

    const result: ExcelExportData = {

      data: process(this.DataList, state).data
    };
    return result;
  }


  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    this.pageHeight = event.target.innerHeight - 233;
  }
//FK: all filter
  public filterChange(filter: CompositeFilterDescriptor): void {
  if(filter.filters.length == 1){
    this.filterGrid = [];
    this.filterGrid = filterBy(this.DataList, filter);
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.subContractorList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
    const next = this.subContractorList.length;
      this.subContractorList = [
        ...this.subContractorList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
    this.state.sort = [{ dir: "desc", field: "Date" }];
    this.gridData = process(this.subContractorList, this.state);
  }
  else {
    this.DataList = [];
    this.filterGrid = [];
    this.DataList = this.dataGrid;
    this.filterGrid = this.dataGrid;
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.subContractorList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
    const next = this.subContractorList.length;
    this.subContractorList = [
      ...this.subContractorList,
      ...this.filterGrid.slice(next, next + this.pagesize)
    ];
    this.state.sort = [{ dir: "desc", field: "Date" }];
    this.gridData = process(this.subContractorList, this.state);
  }
}
//FK: dropdown filer
public resolutionChange(values: any[], filterService: FilterService): void {
  filterService.filter({
    filters: values.map(value => ({
      field: 'Resolution',
      operator: 'eq',
      value
    })),
    logic: 'or'
  });
}

public callStatusChange(values: any[], filterService: FilterService): void {
  filterService.filter({
    filters: values.map(value => ({
      field: 'CallStatus',
      operator: 'eq',
      value
    })),
    logic: 'or'
  });
}

public dropdownFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
  this.dropdownFilter.splice(
    0, this.dropdownFilter.length,
    ...flatten(filter).map(({ value }) => value)
  );
  return this.dropdownFilter;
}

}
