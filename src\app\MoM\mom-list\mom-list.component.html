<admin-header (valueChange)='locationChange($event)'></admin-header>



<section class="margin-top-table-list" >
 
  <kendo-grid
  (selectionChange)="isRowSelected($event)"
  (dblclick)="getDetail()" [sortable]="{allowUnsort: true, mode:'multiple'}"
  class="gridFontStyle" style="margin-bottom: 80px;" [data]="MomGridData"
  [pageable]="{
    buttonCount: buttonCount,
      previousNext: previousNext
  }"
  [pageSize]="state.take" [skip]="state.skip" [sort]="state.sort"
    [filter]="state.filter" [sortable]="{allowUnsort: true, mode:'multiple'}" filterable="menu" [pageable]="true"
    [selectable]="true" (dataStateChange)="dataStateChange($event)">
    <ng-template style="text-align: right;" kendoGridToolbarTemplate>

      <div class="container-fluid p-0">
        <div class="row">
          <div class="col-md-4 col-12">
            <h1 style="top: 9px;">Meetings</h1>
          </div>
          <div class="col-md-8 col-12 text-right">
            <div class="button-create-company">
           
                    <button type="button" (click)="createCompany()" class="k-button bottom-button ExportToExcelButtonCss" >Create Meeting</button>
                   <button type="button" [disabled]="gridData.length===0" class="ExportToExcelButtonCss" kendoGridExcelCommand icon="file-excel">
                    <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                    Export to Excel</button>

            </div>
          </div>
        </div>
      </div>
  </ng-template>
    <kendo-grid-column field="MeetingID" title="Meeting ID" width="170">
      <ng-template kendoGridCellTemplate let-dataItem  >
        {{dataItem?.MeetingID}}
        <button kendoButton  class="followupButton" [primary]="true"  
        (click)="followUpMeeting(dataItem)" primary="true">Follow up</button>        </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="MeetingDate" editor="date" title="Meeting Date" width="170" filter="date" operator="eq"
      format="{0:dd-MM-yyyy}">
      <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
        <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService" operator="eq">
        </kendo-grid-date-filter-menu>
      </ng-template>
      <!-- a -->
    </kendo-grid-column>
    <kendo-grid-column field="MeetingTitle" title="Meeting Title" width="170">

    </kendo-grid-column>
    <kendo-grid-column field="HeldAt" title="Held At" width="150">

    </kendo-grid-column>



    <kendo-grid-column field="NextMeeting" title="Next Meeting" width="180">

    </kendo-grid-column>


    <kendo-grid-column field="Attendees" title="Attendees" width="160">

    </kendo-grid-column>
    <kendo-grid-column field="State" title="State" width="140">
  
    </kendo-grid-column>
    <kendo-grid-column field="Apologies" title="Apologies" width="160">

    </kendo-grid-column>
    <kendo-grid-excel fileName="MoM.xlsx" [fetchData]="allData"></kendo-grid-excel>

    <!-- Apologies -->
  </kendo-grid>


</section>
<admin-footer class="footer-css"></admin-footer>