<div *ngIf="count">
  <p *ngIf="totalCount===0" class="ErrorMsg">No Data Found</p>
  <kendo-chart class="chart-size-css" (seriesClick)="onSeries($event)"
    [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
    [ngClass]="{'cardHeight' : totalCount===0}">

    <kendo-chart-legend position="bottom">
    </kendo-chart-legend>
    <kendo-chart-tooltip>
      <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category" let-series="series">
        {{ category }} : {{ value/totalCount | percent }}
      </ng-template>
    </kendo-chart-tooltip>
    <kendo-chart-series>
      <kendo-chart-series-item type="pie" [data]="pieData" field="value"
        *ngFor="let series of model; let outermost = last;" [startAngle]="190" [name]="series.name" [data]="value"
        field="value" categoryField="category" colorField='#9de219'>
        <kendo-chart-series-item-labels background="none" [visible]='true' [content]="category">
          <kendo-chart-series-item-labels *ngIf="outermost" position="outsideEnd" background="none"
            [content]="labelContent">
          </kendo-chart-series-item-labels>
        </kendo-chart-series-item-labels>
      </kendo-chart-series-item>
    </kendo-chart-series>
  </kendo-chart>
             <!-- new line changes -->
</div>