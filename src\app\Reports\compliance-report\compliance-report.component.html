<admin-header></admin-header>
<section class="dashboard-header section-padding margin-top-section">
  <div class="container-fluid width-auto" style="margin-top: -30px;">
    <div class="row">
      <div class="col-12 mt-4">
        <div class="mb-2 pl-2 text-white d-block titulo">Compliance</div>
        <div class="mscolumnLeft">
          <kendo-treeview class="treeViewUI treeViewOverflowUI" [nodes]="complianceFoldersData" textField="title"
            kendoTreeViewExpandable kendoTreeViewHierarchyBinding [isExpanded]="isExpanded"
            (collapse)="handleCollapse($event)" (expand)="handleExpand($event)" childrenField="children"
            (nodeClick)="onNodeClick($event)" (nodeDblClick)="downloadOnDoubleClick($event)">
            <!-- (selectionChange)="fileClick($event)" -->
            <!-- iconClass(dataItem) -->
            <ng-template kendoTreeViewNodeTemplate let-dataItem>
              <span [ngClass]="iconClass(dataItem.isFolder,dataItem.extension)"></span>
              {{dataItem.title}}
            </ng-template>
          </kendo-treeview>
          <kendo-contextmenu class="contextMenuUI" #treemenu [items]="itemSelected" (select)="onSelect($event)">
          </kendo-contextmenu>
        </div>
        <!-- <div class="mscolumnRight" style="background:#eaf3f3">
          <img *ngIf="showFile" src="imageUrl" style="height:300px;width:300px;" class="center"/>
        </div> -->
      </div>
    </div>
  </div>
</section>

<admin-footer class="dashboardFooterCss"></admin-footer>