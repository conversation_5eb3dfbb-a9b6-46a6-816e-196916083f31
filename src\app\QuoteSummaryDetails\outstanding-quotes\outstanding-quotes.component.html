<admin-header (valueChange)='locationChange($event)'></admin-header>

<section class="margin-top-table-list">
    <div [ngStyle]="{'height.px': pageHeight}">
        <kendo-grid
            [data]="gridData"
            [selectable]="true"
            class="gridFontStyle"
            (selectionChange)="isRowSelected($event)"
            [sortable]="{allowUnsort: true, mode:'multiple'}"
            (dblclick)="getDetail()"
            [skip]="state.skip"
            (filterChange)="filterChange($event)"
            [sort]="state.sort"
            [filter]="state.filter"
            filterable="menu"
            (dataStateChange)="dataStateChange($event)"
            [style.height]="'100%'"
            (scrollBottom)="loadMore()"
            [navigable]="true"
            [loading]="loading"
        >
        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
            <div class="container-fluid p-0">
                <div class="row">
                    <div class="col-md-5 col-5">
                        <h1>Quotes</h1>
                    </div>
                    <div class="col-md-7 col-7 text-right">
                        <button type="button" [disabled]="QuoteSummaryList.length===0" class="ExportToExcelButtonCss" kendoGridExcelCommand icon="file-excel">
                            <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                            Export to Excel
                        </button>
                    </div>
                </div>
            </div>
        </ng-template>
        <kendo-grid-column field="LocationName" title="Location Name" width="210"></kendo-grid-column>
        <kendo-grid-column field="QuoteReference" title="Quote Reference" width="190"></kendo-grid-column>
        <kendo-grid-column field="Subject" title="Subject" width="260"></kendo-grid-column>
        <kendo-grid-column field="PriceTotal" title="Price" width="140">
            <ng-template kendoGridCellTemplate let-dataItem>
                <div class="k-grid-ignore-click">
                    ${{ dataItem.PriceTotal }}
                </div>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="CreationDate" title="Date" width="140" filter="date" operator="eq" format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="Priority" title="Priority" width="150">
            <ng-template kendoGridFilterMenuTemplate
                let-column="column"
                let-filter="filter"
                let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:140px"
                    [data]="priorities"
                    textField="Priority"
                    valueField="Priority"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="priorityChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                <span [ngClass]="{'priority-high': (dataItem.Priority === 'HIGH'|| dataItem.Priority === 'Critical'),
                                  'priority-medium': (dataItem.Priority === 'MEDIUM'|| dataItem.Priority === 'Non-Conformance'),
                                  'priority-low': (dataItem.Priority === 'LOW'|| dataItem.Priority === 'Non-Critical'),
                                  'priority-compliance': dataItem.Priority === 'COMPLIANCE',
                                  'priority-energy': (dataItem.Priority === 'ENERGY'|| dataItem.Priority === 'Recommendations')
                                }">
                    {{dataItem.Priority|uppercase}}
                </span>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="QuoteStatus" title="Quote Status" width="170">
            <ng-template kendoGridFilterMenuTemplate
                let-column="column"
                let-filter="filter"
                let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:170px"
                    [data]="quoteStatuses"
                    textField="QuoteStatus"
                    valueField="QuoteStatus"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="quoteStatusChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="Division" title="Division" width="145">
            <ng-template kendoGridFilterMenuTemplate
               let-column="column"
               let-filter="filter"
               let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:160px"
                    [data]="division"
                    textField="Division"
                    valueField="Division"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="quoteTypeChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-excel fileName="Quotes.xlsx" width="100" [fetchData]="allData">
            <kendo-excelexport-column field="LocationName" title="Location Name" width="210">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="QuoteReference" title="Quote Reference" width="170">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Subject" title="Subject" width="260">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="PriceTotal" title="Price" width="115">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="CreationDate" title="Date" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Priority" title="Priority" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="QuoteStatus" title="Quote Status" width="150">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Division" title="Division" width="145">
            </kendo-excelexport-column>
           </kendo-grid-excel>
        </kendo-grid>
        <div *ngIf="isShow" style="text-align: center;">
            <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
                <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
            </button>
        </div>
    </div>
</section>

<admin-footer class="outstandingQuotesFooter"></admin-footer>