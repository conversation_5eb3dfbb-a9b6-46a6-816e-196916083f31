<div id="headerView"></div>
<admin-header (addLocationListOnRefreshBrowser)='locationChange($event)'></admin-header>
<section class="section-css">
  <div class="container-fluid">

    <form ngNativeValidate #customerStatus="ngForm" class="k-form" (ngSubmit)="savePlaceCall(customerStatus)">
      <div class="row">
        <div class="col-md-12">
          <div class="mb-2 pl-2 text-white d-block title">Place a Call</div>
        </div>
        <label class="k-form-field col-md-12" style="margin-top: -16px;">
          <span class="defaultSpanPosition">Location <span class="k-required">*</span></span>
          <kendo-dropdownlist class="k-multiselect" class="margin-auto" [data]="LocationList"
            [(ngModel)]="selectedLocations" [filterable]="true" [textField]="'Name'" [valueField]="'LocationID'"
            (filterChange)="filterLocation($event)" name="location" (valueChange)="locationSelectionChange($event)"
            #location="ngModel" required class="dropdownListWidth" [disabled]="isBacklogData">
            <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
              <div class="container drodown-header-location">
                <div class="row">
                  <div class="col-md-6 col-6">
                    <span class="mutiselectTextPosition" style="position: relative;
                    left: 10%;">Customer Name</span>
                  </div>
                  <div class="col-md-6 col-6">
                    <span class="template" class="mutiselectTextPosition" style="  position: relative;
                right: 1%;">Location</span>
                  </div>
                </div>
              </div>
            </ng-template>
            <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
              <div class="container ">
                <div class="row">
                  <div class="col-md-6 col-6">
                    <!-- <input type="checkbox" class="k-checkbox"
                      [checked]="isItemSelected(dataItem.text)"> -->
                    <span class="template md-size location-position">{{ dataItem.CustName }}</span>
                  </div>
                  <div class="col-md-6 col-6">
                    <span class="md-size">{{dataItem.Name }}</span>
                  </div>
                </div>
              </div>
            </ng-template>
          </kendo-dropdownlist>
          <div *ngIf="location.errors && location.touched">
            <div class="invalid-login" [hidden]="(!location.errors.required )">
              <span class="errorMessage">Location
                is required !</span>
            </div>
          </div>

        </label>
        <label class="k-form-field col-md-12" style="margin-top: 0 !important">
          <span class="defaultSpanPosition">Equipment</span>
          <kendo-combobox [data]="equipmentDropDownList" class="margin-auto" [textField]="'Code'" name="equipment"
            [filterable]="true" [(ngModel)]="selectedPlaceCallData.EquipmentID" [valuePrimitive]="true"
            [valueField]="'Code'" (filterChange)="FilterEquipment($event)" #equipment="ngModel"
            class="dropdownListWidth" [allowCustom]="true" [disabled]="isBacklogData">

            <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
              <div class="container drodown-header-location">
                <div class="row">
                  <div class="col-md-2 col-2">
                    <span class="mutiselectTextPosition" style="position: relative;
                      left: 9%;">
                      Code</span>
                  </div>
                  <div class="col-md-2  col-2">
                    <span class="template" class="mutiselectTextPosition" style="  position: relative;
                  right: -3%;">Make
                    </span>
                  </div>
                  <div class="col-md-4  col-4">
                    <span class="template" class="mutiselectTextPosition" style="  position: relative;
                  right: 2%;">EquipmentType
                    </span>
                  </div>
                  <div class="col-md-2 col-2">
                    <span class="template" class="mutiselectTextPosition" style="  position: relative;
                  right: 15%;">Model
                    </span>
                  </div>
                  <div class="col-md-2  col-2">
                    <span class="template" class="mutiselectTextPosition" style="  position: relative;
                  right: 30%;">BarCode
                    </span>
                  </div>

                </div>
              </div>
            </ng-template>
            <ng-template kendoDropDownListItemTemplate let-dataItem>
              <div class="container" style="padding: 0px">
                <div class="row ">
                  <div class="col-md-2 col-2">
                    <span class="template" class="mutiselectTextPosition">{{ dataItem.Code | titlecase }}</span>
                  </div>
                  <div class="col-md-2 col-2">
                    <span class="mutiselectTextPosition">{{ dataItem.Make | titlecase }}</span>
                  </div>
                  <div class="col-md-4 col-4">
                    <span class="mutiselectTextPosition">{{ dataItem.EquipmentType | titlecase }}</span>
                  </div>
                  <div class="col-md-2 col-2">
                    <span class="mutiselectTextPosition">{{ dataItem.Model | titlecase }}</span>
                  </div>
                  <div class="col-md-2 col-2">
                    <span class="mutiselectTextPosition">{{ dataItem.BarCode | titlecase }}</span>
                  </div>
                </div>
              </div>
            </ng-template>




          </kendo-combobox>
          <!-- <div *ngIf="equipment.errors && equipment.touched && location['valid']">
            <div class="invalid-login" [hidden]="(!equipment.errors.required )">
              <span class="errorMessage">Equipment
                is required !</span>
            </div>
          </div> -->
          <div *ngIf="location['valid']===false && equipment.touched===true">
            <div class="invalid-login">
              <span class="errorMessage">Please select location first
              </span>
            </div>
          </div>

        </label>
        <label class="k-form-field col-md-12" style="margin-bottom: -1px; margin-top: 0 !important;">
          <span class="defaultSpanPosition" style="    position: relative;
          bottom: -3px;">Purchase Order <span class="k-required">*</span>
          </span>
          <input class="k-textbox" name="purchaseOrder" [(ngModel)]='selectedPlaceCallData.PurchaseOrder'
            #order="ngModel" required />
          <div *ngIf="order.errors && order.touched">
            <div class="invalid-login" [hidden]="(!order.errors.required )">
              <span class="errorMessage">Purchase Order
                is required !</span>
            </div>
          </div>
        </label>

        <label *ngIf="isBacklogData" class="k-form-field col-md-12" style="margin-bottom: -1px; margin-top: 0 !important;">
          <span class="defaultSpanPosition" style="position: relative;
          bottom: -3px;">Work Order Limit
          </span>
          <kendo-numerictextbox style="width: 64% !important;" name="workOrderLimit"
           showErrorIcon="initial" [min]="0" [decimals]="2" [autoCorrect]="true"
           [(ngModel)]='workOrderLimitValue'
            #workOrder="ngModel"></kendo-numerictextbox>
        </label>


        <label class="k-form-field col-md-8" style="margin-top: 0 !important;">
          <span class="defaultSpanPosition" style="    position: relative;
                bottom: -3px;">Description </span>
          <textarea class="k-textarea" name="description" [(ngModel)]='selectedPlaceCallData.Description'
            #description="ngModel" style="background-color: transparent;" [disabled]="isBacklogData">
                       </textarea>

        </label>
        <label class="k-form-field col-md-4">

        </label>
        <label class="k-form-field col-md-12" style="margin-top: 31px;">
          <button type="submit" [disabled]='!customerStatus.valid'
            class="k-button k-primary button-css" style="background-color: #00ccff !important;">Save</button>&nbsp;
          <button type="button" class="k-button cancel button-css" routerLink="/customer-management">cancel</button>
        </label>
      </div>
    </form>
  </div>
</section>
<admin-footer class="footer-css"></admin-footer>