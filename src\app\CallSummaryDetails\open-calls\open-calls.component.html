<admin-header (valueChange)='locationChange($event)'></admin-header>

<section class="margin-top-table-list">
    <div [ngStyle]="{'height.px': pageHeight}" #container>
        <kendo-grid [data]="gridData"
            [selectable]="true"
            class="gridFontStyle"
            [sortable]="{allowUnsort: true, mode:'multiple'}"
            [skip]="state.skip"
            [sort]="state.sort"
            [filter]="state.filter"
            filterable="menu"
            (filterChange)="filterChange($event)"
            [loading]="loading"
            (dataStateChange)="dataStateChange($event)"
            [style.height]="'100%'"
            (scrollBottom)="loadMore()"
            [navigable]="true"
        >
        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
            <div class="container-fluid p-0">
                <div class="row">
                    <div class="col-md-5 col-5">
                        <h1>Service Calls</h1>
                    </div>
                    <div class="col-md-7 col-7 text-right">
                        <button type="button" [disabled]="gridView.length===0" class="ExportToExcelButtonCss" kendoGridExcelCommand icon="file-excel">
                            <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                            Export to Excel</button>
                    </div>
                </div>
            </div>
        </ng-template>
        <!-- <kendo-grid-column field="Location" title="Location" width="145">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem?.Location}}
            </ng-template>
        </kendo-grid-column>-->
        <kendo-grid-column field="LocationName" title="Location Name" width="210">
            <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem?.LocationName}}
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="DATE" title="Date" width="140" filter="date" operator="eq" format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="ServiceCallID" title="Service Call ID" width="170" height="0">

        </kendo-grid-column>
        <kendo-grid-column field="ServiceDesciption" title="Service Description" width="290">

        </kendo-grid-column>

        <kendo-grid-column field="Resolution" title="Resolution" width="160">
            <ng-template kendoGridFilterMenuTemplate
                let-column="column"
                let-filter="filter"
                let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:150px"
                    [data]="resolutions"
                    textField="Resolution"
                    valueField="Resolution"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="resolutionChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="PO" title="Purchase Order" width="180">

        </kendo-grid-column>

        <kendo-grid-column field="CallStatus" title="Call Status" width="165">
            <ng-template kendoGridFilterMenuTemplate
                let-column="column"
                let-filter="filter"
                let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:145px"
                    [data]="callStatus"
                    textField="CallStatus"
                    valueField="CallStatus"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="callStatusChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="CallType" title="Call Type" width="150">
            <ng-template kendoGridFilterMenuTemplate
                let-column="column"
                let-filter="filter"
                let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:170px"
                    [data]="callTypes"
                    textField="CallType"
                    valueField="CallType"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="callTypeChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
            <!-- <ng-template kendoGridCellTemplate let-dataItem>
                {{dataItem?.CallType}}
            </ng-template> -->
        </kendo-grid-column>
        <kendo-grid-column field="Divisions" title="Division" width="165">
            <ng-template kendoGridFilterMenuTemplate
               let-column="column"
               let-filter="filter"
               let-filterService="filterService"
            >
                <kendo-multiselect
                    style="width:160px"
                    [data]="division"
                    textField="Divisions"
                    valueField="Divisions"
                    [valuePrimitive]="true"
                    [value]="dropdownFilters(filter)"
                    (valueChange)="divisionsChange($event, filterService)"
                >
                </kendo-multiselect>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="" title="" width="80">
            <ng-template kendoGridCellTemplate let-dataItem>
                <button [ngClass]="{'hideButton':dataItem.CallStatus==='OPEN'}"  kendoButton class="followupButton downloadButton" title="DownLoad Call Summary Report" [primary]="true"
                    (click)="DownloadSummaryReport(dataItem)" primary="true"><span
                        class="k-icon k-i-download"></span></button>
            </ng-template>
        </kendo-grid-column>

        <kendo-grid-excel fileName="Open Calls.xlsx" [fetchData]="allData">
            <kendo-excelexport-column field="Location" title="Location" width="210">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="DATE" title="Date" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="ServiceCallID" title="Service Call ID" width="170">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="ServiceDesciption" title="Service Description" width="300">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Resolution" title="Resolution " width="140">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="PO" title="Purchase Order" width="170">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="CallStatus" title="Call Status" width="140">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Divisions" title="Division" width="140">
            </kendo-excelexport-column>


        </kendo-grid-excel>
        </kendo-grid>
        <div *ngIf="isShow" style="text-align: center;">
            <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
                <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
            </button>
        </div>
    </div>
</section>

<admin-footer class="open-calls-footer"></admin-footer>