@charset "utf-8";
/* Variables
   ============================================== */
/* placeholder */
/* buttons */
/* Mixins
   ============================================== */
/* Reset
   ============================================== */
*,
*:after,
*:before {
  box-sizing: border-box;
  margin: 12px;
  padding: 0;
}
/* Common
   ============================================== */
html {
  font-family: calibri light , "Roboto";
  min-height: 100%;
  min-width: 320px;
}
body {
  background-color: #663faa;
  background: -ms-linear-gradient(top left, #663faa 0%, #585fd5 100%);
  background: -webkit-linear-gradient(top left, #663faa, #585fd5);
  background: -o-linear-gradient(top left, #663faa, #585fd5);
  background: -moz-linear-gradient(top left, #663faa, #585fd5);
  background: linear-gradient(top left, #663faa, #585fd5);
  background-repeat: no-repeat;
  font-family: "Roboto", calibri light;
  font-size: 14px;
  min-width: 100%;
  min-height: 100%;
  overflow: scroll;
  padding: 30px 0;
}
.container {
  display: block;
  background: #fff;
  width: 558px;
  min-height: 458px;
  border-radius: 3px;
  margin: 98px auto;
  padding: 30px;
}
.container h1 {
  position: relative;
  display: inline-block;
  color: #000;
  font-size: 2em;
  font-weight: 400;
  text-transform: uppercase;
  text-align: center;
  margin: 0 0 20px;
  padding: 0;
}
.container h1:after {
  display: block;
  background: #000;
  content: "";
  height: 3px;
  width: 50%;
  margin: 20px auto 0;
  padding: 0;
}
.form {
  display: flex;
  flex-direction: column;
}
.form__group {
  margin: 10px 0 0;
}
.form__group--error.form__pincode > input {
  background-color: #eb3b3b;
}
.form__group--error.form__pincode > input[disabled] {
  background-color: #eb3b3b;
  color: #fff;
  opacity: 1;
}
.form__group--success.form__pincode > input {
  background-color: #32c832;
}
.form__group--success.form__pincode > input[disabled] {
  background-color: #32c832;
  color: #fff;
  opacity: 1;
}
.form__pincode {
  display: block;
  width: 100%;
  margin: 10px auto 20px;
  padding: 0;
}
.form__pincode:before,
.form__pincode:after {
  display: table;
  content: "";
}
.form__pincode:after {
  clear: both;
}
.form__pincode > label {
  display: block;
  text-align: center;
  margin: 10px 0;
}
.form__pincode > input[type="number"] {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}
.form__pincode > input {
  display: inline-block;
  float: left;
  width: 15%;
  height: 50px;
  line-height: 48px;
  text-align: center;
  font-size: 2em;
  color: #181819;
  border: 0;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 2px 2px 0 0;
  transition: background-color 0.3s, color 0.3s, opacity 0.3s;
  cursor: default;
  user-select: none;
  margin: 0;
  margin-top: 10px;
  margin-right: 2%;
  padding: 0;
}
.cross-label {
  background-color: red;
  display: flex;
  flex-direction: row-reverse;
  margin-top: -22px;
  margin-left: -26px;
  margin-right: -26px;
  height: 37px;
}
.form__pincode > input:focus {
  outline: 0;
  box-shadow: none;
  border-color: #1486f0;
  animation: border-pulsate 1.5s infinite;
  -webkit-tap-highlight-color: transparent;
}
.form__pincode > input:last-child {
  margin-right: 0;
}
.form__pincode > input[disabled] {
  background: #eee;
  opacity: 1;
}
.form__buttons {
  text-align: center;
  margin: 0 auto;
  padding: 10px 0 0;
}
.pin-blocks {
  padding-bottom: 21px;
  margin-left: 14px;
  display: flex;
  justify-content: space-between;
}
/* Button
   ============================================== */
.button {
  position: relative;
  display: inline-block;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  outline: 0;
  cursor: pointer;
  height: 50px;
  line-height: 50px;
  margin: 0;
  padding: 0 20px;
  /* primary */
}
.button--primary {
  background-color: #4776e6;
  color: #fff;
  line-height: 48px;
  border: 1px solid transparent;
  border-radius: 50px;
  text-transform: uppercase;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
}
.button--primary:focus,
.button--primary:hover {
  background-color: #2d62e2;
  color: #fff;
}
.button--primary:active {
  background-color: #1d53d4;
  color: #fff;
  box-shadow: inset 0 0 4px 2px rgba(0, 0, 0, 0.1);
}
.button--primary[disabled] {
  background-color: #4776e6;
  user-select: none;
  pointer-events: none;
  cursor: not-allowed;
  -moz-opacity: 0.3;
  -khtml-opacity: 0.3;
  opacity: 0.3;
}
.input_blocks {
  width: 37px;
  margin: 7px;
  height: 50px;
  text-align: center;
}
#resCode {
  cursor: pointer;
  color: #2d62e2;
}
#resCode:hover {
  color: darkblue;
}

/* Placeholder
   ============================================== */
/* Chrome/Opera/Safari */
::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.7;
}
/* Firefox 19+ */
::-moz-placeholder {
  color: inherit;
  opacity: 0.7;
}
/* IE 10+ */
:-ms-input-placeholder {
  color: inherit;
  opacity: 0.7;
}
/* Firefox 18- */
:-moz-placeholder {
  color: inherit;
  opacity: 0.7;
}
/* Animations
   ============================================== */
@-moz-keyframes border-pulsate {
  0% {
    border-color: #1486f0;
  }
  50% {
    border-color: rgba(0, 0, 0, 0.3);
  }
  100% {
    border-color: #1486f0;
  }
}
@-webkit-keyframes border-pulsate {
  0% {
    border-color: #1486f0;
  }
  50% {
    border-color: rgba(0, 0, 0, 0.3);
  }
  100% {
    border-color: #1486f0;
  }
}
@-o-keyframes border-pulsate {
  0% {
    border-color: #1486f0;
  }
  50% {
    border-color: rgba(0, 0, 0, 0.3);
  }
  100% {
    border-color: #1486f0;
  }
}
@keyframes border-pulsate {
  0% {
    border-color: #1486f0;
  }
  50% {
    border-color: rgba(0, 0, 0, 0.3);
  }
  100% {
    border-color: #1486f0;
  }
}
.padding-left{
  padding-left: 2%;
  border: 1px solid gainsboro;
}
.img-center
{
  padding-left: 17%;
  padding-right: 17%;
}
.form-group-material
{
  margin-bottom: 1px !important;
}
.logoHeight
{
  position: relative;
  width: 88%;
  margin-bottom: -25px;
}
.help-block
{
  color: #ff0000d1;
  font-size: smaller;
  position: relative;
  bottom: 5px;
}
.btn-unisaf
{
  background: #196db6;
  border-color: #196db6;
  color: white;
  font-family: calibri light , "Roboto";
}
.d-flex
{
  display: block!important;
}
.form-inner
{
  width: 83%;   
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
}
.btn-unisaf
{
  position: relative;
  top: 10px;
}
.form__buttons {
  text-align: center;
  margin: 0 auto;
  padding: 10px 0 0;
}