﻿import { Component, ContentChild, EventEmitter, Input, Output, On<PERSON><PERSON>roy, Renderer2, TemplateRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { GridComponent } from '@progress/kendo-angular-grid';

@Component({
    selector: 'grid-context-menu',
    template: `
        <kendo-popup *ngIf="show" [offset]="offset">
            <ul class="menu">
              <li *ngFor="let item of menuItems" (click)="menuItemSelected(item)">
                <ng-template
                      *ngIf="menuItemTemplate"
                      [ngTemplateOutlet]="menuItemTemplate"
                      [ngTemplateOutletContext]="{ item: item, dataItem: dataItem }">
                </ng-template>
                <ng-container *ngIf="!menuItemTemplate">
                    <span [ngClass]="contextIconClass(item)">
                      {{ item }}
                    </span>
                </ng-container>
              </li>
            </ul>
        </kendo-popup>
    `,
    styles: [`
     .menu {
        list-style:none;
        margin: 0;
        padding: 0;
        cursor: pointer;
      }
      .menu-icon{
        color:white !important;
        cursor: pointer;
      }
      .menu li {
        border-bottom: 1px solid rgba(0,0,0,.08);
        padding: 0px 10px;
        transition: background .2s, color .2s;
        min-width:100px;
        width:auto;
      }

      .menu li:last-child {
        border-bottom: 0;
      }

      .menu li:hover {
        background: #0093d7;
        color:white;
      }

      .menu li:active {
        background: #17b0e9;
        color: #fff;
      }
    `]
})
export class GridContextMenuComponent implements OnDestroy {

    @ContentChild(TemplateRef)
    public menuItemTemplate: TemplateRef<any>;

    @Input()
    public menuItems: any[] = [];

    @Output()
    public select: EventEmitter<any> = new EventEmitter<any>();

    @Input() public set for(grid: GridComponent) {
        this.unsubscribe();
        this.cellClickSubscription = grid.cellClick.subscribe(this.onCellClick);
    }

    public show: boolean;
    public dataItem: any;
    public offset: any;

    private cellClickSubscription: Subscription;
    private documentClickSubscription: any;

    constructor(private renderer: Renderer2) {
        this.onCellClick = this.onCellClick.bind(this);
        this.documentClickSubscription = this.renderer.listen('document', 'click', () => {
            this.show = false;
        });
    }

    public ngOnDestroy(): void {
        this.unsubscribe();
        this.documentClickSubscription();
    }

    public menuItemSelected(item: any): void {
        this.select.emit({ item: item, dataItem: this.dataItem });
    }

    private onCellClick({ dataItem, type, originalEvent }): void {
      if (type === 'contextmenu') {
        originalEvent.preventDefault();
        this.dataItem = dataItem;
        this.show = true;
        this.offset = { left: originalEvent.pageX, top: originalEvent.pageY };
      }
    }
  public contextIconClass(items) {
    debugger;
      if (items != undefined && items != null) {
          let itemName = items.toUpperCase();
          return {
              //MS: Context Menu Items Icon
              "k-i-preview": itemName == "PREVIEW",
              "k-i-folder-open": itemName == "OPEN",
              "k-i-download": itemName == "DOWNLOAD",

          //MS: Icon Config
          "k-icon": true
          };
      }
    }
    private unsubscribe(): void {
        if (this.cellClickSubscription) {
            this.cellClickSubscription.unsubscribe();
            this.cellClickSubscription = null;
        }
    }
}