.titulo {
  background-color: #343a40;
/* font-family: 'Open Sans Condensed'; */
font-family: calibri light , "Roboto";
font-weight: 700;
padding: 12px 0px;
}


.k-menu.k-context-menu .k-item.k-state-hover, .k-menu.k-context-menu .k-item:hover {
  color: #0291d5 !important;
  background-color: white !important;
}
// .k-menu.k-context-menu .k-item.k-state-hover, .k-menu.k-context-menu .k-item:hover {
//   color: #0291d5 !important;
//   background-color: white !important;
// }

.k-icon{
  color:#0291d5 !important;
}
.k-i-arrow {
  color:#0291d5 !important;
}
.event-log li:last-child { margin-bottom: -1px;}
.treeViewOverflowUI{
  color: black !important;
  height: 400px;
  overflow: scroll;
}
.mscolumnLeft {
  float: left;
  width: 100%;
  padding: 10px;
  height: 400px; /* Should be removed. Only for demonstration */
}
.mscolumnRight {
  float: right;
  width: 70%;
  padding: 10px;
  height: 400px; /* Should be removed. Only for demonstration */
}
.center {
  
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 50%;
}
.dashboardFooterCss{
  position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
}
@media only screen and (max-width: 1139px) {
  .margin-top-section
  {
    margin-top: 3% !important;
  }
  .section-padding
  {
    padding: 0px;
  }
  }
  .margin-top-section
  {
    margin-top: 10%;
  }
.k-i-file-pdf
{
  color: red !important;
}
.k-i-photo
{
  color: #150000ed !important;
}