<div *ngIf="count===true">

  <kendo-chart style="    height: 332px;"
    [seriesColors]="['#FF8A6F','#74E6B9','#9F9F9F','#D55974','#FDE2E2','#EE6C4D','#F79494','#777777']"
    (seriesClick)="onSeries($event)">

    <kendo-chart-tooltip>
      <ng-template kendoChartSeriesTooltipTemplate let-value="value" let-category="category" let-series="series">
        {{ category }} : ${{value }}{{currency}}
      </ng-template>
    </kendo-chart-tooltip>
  <kendo-chart-category-axis>
      <kendo-chart-category-axis-item [categories]="categories">
        <kendo-chart-category-axis-item-labels [font]="10">
        </kendo-chart-category-axis-item-labels>
      </kendo-chart-category-axis-item>
    </kendo-chart-category-axis>
    <kendo-chart-series>

      <kendo-chart-series-item type="column" [data]="openDebtData">
        <kendo-chart-series-item-labels format="C0" [margin]="-6">

        </kendo-chart-series-item-labels>
      </kendo-chart-series-item>
    </kendo-chart-series>
    <kendo-chart-value-axis>
      <kendo-chart-value-axis-item [labels]="{ format: 'C0' }">
      </kendo-chart-value-axis-item>
    </kendo-chart-value-axis>
  </kendo-chart>
</div>