export class DocumentIDEntity {
    DocumentID: number;
}
export class CustomersEntities {
    CustName: string;
    CustomerCode: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class BulkDocumentsEntity {
    DocumentTypeId: number;
    Name: string;
    Description: string;
    Active: boolean;
}
export class DocDetailByPropertiesModel {
    DocumentId: number;
    DocumentTypeId: number;
    DocumentType: string;

}
export class DocumentsPropertiesEntity {
    DataType: number
    PropertyId: number
    PropertyName: string;
    UserPrompt: string;
}

export class CustomerLocationByName {
    CustName: string;
    Name: string;
    LocationID: number;
    CustomerID: number;
    CustomerCode: string;
    LocationCode: string;
}
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}
export class DocumentDetailsEntities {
    DocumentTypeID: string;
    PropertyID: number;
    DataTypeVal: number;
    Operator: string;
    SearchVal: string;
}
