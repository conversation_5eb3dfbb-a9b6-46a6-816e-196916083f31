export class pieChartModel {
    Total: number;
    Type: string;
}

export class chartSeriesModel {
    category: string;
    value: number;
}
export class OpenDebtGraphEntity {
    Aged: string;
    TotalAmount: number;
}
export class TMMonthGraphEntity {
   Months:string;
   TMTotalCalls:number;
   AHTotalCalls:number;
   TotalCalls:number;
}

//FK:
export class TMMonthGraphByStatusCallEntity {
    Months :string;
    OPENCalls  :number;
    COMPLETECalls :number;
    INVOICEDCalls :number;
    TotalCalls  :number;
}
export class MostExpensiveEquipmentChartData{
    EquipmentID: string;
    Spend_ExGST: number;
}
export class MostExpensiveEquipment{
    JobNumber: string;
    EquipmentID: string;
    LocationCode: string;
    GPCustomerID: string;
    Spend_ExGST: number;
}

export enum OpenDebtChartVALUES {
    "0 - 30 DAYS" = "0To30",
    "31 - 60 DAYS" = "31To60",
    "61 - 90 DAYS" = "61To90",
    "91 AND OVER" = "Over90"
}
export var piechanrtInnerDataModel = [{
    data: []
}];
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}
export class CustomerLocationByName {
    CustName: string;
    Name: string;
    LocationID: number;
    CustomerID: number;
}
export class CustomerSummary {
    TotalControlsJCServices: number;
    TotalInstallJCServices: number;
    TotalMCCServices: number;
    TotalMaintenanceJCCallsServices: number;
    TotalQuotes: number;
    TotalServicesJCCallServices: number;
    TotalTMCompletionServices: number;
    TotalTMOpenServices: number;
    TotalTMServiceCalls: number;
    TotalJCServiceCalls: number;
    TotalOpenDebt: number;
    TotalSubContractor: number;
}
export class LocationDropdownListModel {
    CustName: string;
    CustomerID: number;
    LocationCode: string;
    LocationID: number;
    Name: string;
    StateCode: string;
}
export class TMMonthGraphByStatusCallChartValues {
    static "Open Calls" = "Open";
    static "Complete Calls" = "Complete";
    static "Invoiced Calls" = "Invoiced";
}