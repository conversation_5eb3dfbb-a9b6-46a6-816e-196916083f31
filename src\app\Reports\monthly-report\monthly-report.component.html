<div id="headerView"></div>
<admin-header (addLocationListOnRefreshBrowser)='locationChange($event)'  ></admin-header>


<section class="margin-top-table-list section-css">
  <div class="container-fluid">
    <div class="row">
      <div class="col-12 mt-4">
        <div class="mb-2 pl-2 text-white d-block title" style="margin-top: 2vh">Monthly Report</div>
        <form #reportref="ngForm">
          <div class="row">

            <div class="col-md-6 col-12">
              <div class="k-form-field" style="display: block; margin-bottom: -2px;">
                <p style="font-size: .9rem;">Customers<span class="k-required">*</span></p>
                <div class="displayInline" *ngIf="ReportData.Customer.length>0">
                  <button class="k-button" kendoButton
                    (click)="openCustomer(dataItem)">{{ReportData.Customer.length>2 ? defaultCustomerAsString+' + '+(ReportData.Customer.length-totalCustomerCount)+" more":totalSelectedCustomers}}
                  </button>
                </div>
                <div class="displayInline" *ngIf="ReportData.Customer.length===0">
                  <button class="k-button" kendoButton
                    (click)="openCustomer()">{{defaultCustomerDropdown}}
                  </button>
                </div>

              </div>
            </div>

            <div class="col-md-6 col-12">
              <div class="k-form-field" style="display: block; margin-bottom: -2px;">
                <p style="font-size: .9rem;">Locations<span class="k-required">*</span></p>
                <div class="displayInline" *ngIf="ReportData.Location.length>0">
                  <button class="k-button" kendoButton
                    (click)="open(dataItem)">{{ReportData.Location.length>2 ? defaultLocationAsString+' + '+(ReportData.Location.length-totalCount)+" more":totalSelectedLocations}}
                  </button>
                </div>
                <!-- [disabled]='!customer.valid' -->
                <div class="displayInline" *ngIf="ReportData.Location.length===0">
                  <button class="k-button" kendoButton
                    (click)="open()" [disabled]='ReportData.Customer.length == 0'>{{defaultDropdown}}
                  </button>
                </div>
              </div>
            </div>

          </div>

          <div class="row">
            <div class="col-md-5">
              <div class="k-form-field" style="display: block;    margin-bottom: -2px;">
                <p style="font-size: .9rem;">Contact<span class="k-required">*</span></p>
                <div class="displayInline" *ngIf="ReportData.Contact.length>0">
                  <button class="k-button" kendoButton
                    (click)="openContact(dataItem)">{{ReportData.Contact.length>2 ? defaultContactAsString+' + '+(ReportData.Contact.length-totalCount)+" more":totalSelectedContact}}
                  </button>
                </div>
                <div class="displayInline" *ngIf="ReportData.Contact.length===0">
                  <button class="k-button" kendoButton
                    (click)="openContact()" [disabled]='ReportData.Location.length == 0'>{{defaultContactDropdown}}
                  </button>
                </div>
               
              
                 <!-- <kendo-multiselect class="dropdownListWidth" [data]="contactData" [textField]="'Name'"
                  [filterable]="true" (filterChange)="filterContact($event)" [valueField]="'ContactID'"
                  #Contact="ngModel" [(ngModel)]="Contact " [valuePrimitive]="true" name="Contact"
                  required
                > 
                  <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
                    <div class="container drodown-header-location" style="max-width: 100%;">
                      <div class="row">
                        <div class="col-md-6 col-6">
                          <span class="mutiselectTextPosition md-size location-position" >Contact Name</span>
                        </div>
                        <div class="col-md-6  col-6">
                          <span class="template md-size" class="mutiselectTextPosition" style="  position: relative;
                          right: -13%;"> </span>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                  <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
                    <div class="container" style="max-width: 100%;">
                      <div class="row">
                        <div class="col-md-6 ">
                          <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
                        </div>
                        <div class="col-md-6 text-right ">
                           <span class=" ">{{dataItem.CustName | titlecase}}</span>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </kendo-multiselect> -->
                <!-- <div *ngIf="Contact.errors && Contact.touched">
                  <div class="invalid-login" [hidden]="(!Contact.errors.required )">
                    <span class="errorMessage">Contact is required !</span>
                  </div>
                </div> -->
              </div>
            </div>
            <div class="col-md-3 setField">
              <label class="k-form-field" class="dateRangeMon" class="date-range-position">
                <kendo-daterange  style="position: relative;
                bottom: 6px;" #daterange>
                  <label class="startDate">
                    <span style="font-size: .9rem;    position: relative;
                    top: 3px;" class="label">Start Date</span>
                    <kendo-dateinput [format]="'dd/MM/yyyy'" name="start" kendoDateRangeStartInput
                      [(ngModel)]="ReportData.StartDate" autoCorrectOn="blur" style="font-family: calibri light, 'Roboto';"></kendo-dateinput>
                  </label>
                  <label>
                    <span style="font-size: .9rem;    position: relative;
                    top: 3px;" class="label">End Date</span>
                    <kendo-dateinput  [format]="'dd/MM/yyyy'" style="margin-right: 9px; font-family: calibri light, 'Roboto';" name="end"
                      kendoDateRangeEndInput [(ngModel)]="ReportData.EndDate" autoCorrectOn="blur"
                    >
                    </kendo-dateinput>
                  </label>
                  <kendo-daterange-popup  [popupAlign]="{ horizontal: 'left', vertical: 'top' }" [animate]="false"
                    class="z-index: 999;">
                    <ng-template kendoDateRangePopupTemplate>
                      <kendo-multiviewcalendar kendoDateRangeSelection>
                      </kendo-multiviewcalendar>
                    </ng-template>
                  </kendo-daterange-popup>
                </kendo-daterange>
              </label>
            </div>
            <div class="col-md-2 setField">
              <label class="k-form-field date-range-position" style="display: block;    margin-bottom: -2px;">
                <p *ngIf="!isBMS" style="font-size: .9rem;">Fire Contract</p>
                <p *ngIf="isBMS" style="font-size: .9rem;">BMS</p>
              
                <kendo-switch
                    [(ngModel)]="ReportData.FireContract"
                    name="FireContract" #FireContract="ngModel">
                  </kendo-switch>
              </label>
            </div>
            <div class="col-md-2 text-right setField">
              <label class="k-form-field" class="date-range-position ">
                <p>
                  <button type="submit" [disabled]="!reportref.valid || ReportData.Location.length===0 || ReportData.Contact.length===0"
                    (click)="generateReport()" class="k-button bottom-button  k-primary " style="background-color: #00ccff !important; color: white !important;">Generate Monthly
                    Report</button>
                </p>
              </label>
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</section>

<kendo-dialog class="multiselextDropdownHeader" title="Choose Locations" *ngIf="opened" (close)="close('cancel')"
  [minWidth]="300">
  <!-- <div class="container-fluid ul-container">
    <div class="row ul-row">
      <ul style="display: contents;">
        <li *ngFor="let item of ReportData.Location;index as i;">
          <span class="span-content">
            <span>{{item.Name}}</span>
            <span aria-label="delete" class="k-select" aria-hidden="true">
              <span class="k-icon k-i-close" (click)="removeTag(i)">
                <i class="fa fa-cross-circle-right fa-lg" (click)="removeTag(i)"></i>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div> -->

  <kendo-multiselect [autoClose]="false" class="k-multiselect" [data]="locationDropdown"
    [(ngModel)]="ReportData.Location" [filterable]="true" [textField]="'Name'" (filterChange)="filterLocation($event)"
    [valueField]="'LocationID'" name="location" #location="ngModel" required (valueChange)="OnLocationChange()">
    <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
      <div class="container drodown-filter-location">
        <div class="row">
          <div class="col-md-6 col-6">
            <span class="mutiselectTextPosition">Location Name</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="template" class="mutiselectTextPosition" style="  position: relative;
        right: 1%;">Code</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="mutiselectTextPosition">
              <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                class="btn selectAllButtonCss">Select All</button>
              <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                class="btn deselectAllButtonCss">Deselect All</button>
            </span>
            <span style="padding-left: 5px;" class="mutiselectTextPosition">
              <button type="submit" (click)="close('yes')"
                class="btn selectAllButtonCss">Done</button>
            </span>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-6">

            <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
          </div>
          <div class="col-md-4 col-4">
            <span class="md-size">{{dataItem.LocationCode }}</span>
          </div>
          <div class="col-md-2 col-2">
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-multiselect>
  <kendo-dialog-actions>
    <div class="container-fluid" style="padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton style="width: 29% !important; width: 29% !important;
          background: #00ccff !important;
          color: white;" class="ButtonFooterDialog" [primary]="true" (click)="clearAll()" primary="true">Clear
            All</button>
        </div>
        <div class="col-md-6 col-6 text-right">
          <button kendoButton class="ButtonFooterDialog" [primary]="true" style="float: right;width: 29% !important;
          background: #00ccff !important;
          color: white;" (click)="close('yes')" primary="true">Add</button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<!-- FK: select customer popup -->
<kendo-dialog class="multiselextDropdownHeader" title="Choose Customers" *ngIf="showCustomer" (close)="closeCustomer('cancel')"
  [minWidth]="300">
  <!-- <div class="container-fluid ul-container">
    <div class="row ul-row">
      <ul style="display: contents;">
        <li *ngFor="let item of ReportData.Customer;index as i;">
          <span class="span-content">
            <span>{{item.CustName}}</span>
            <span aria-label="delete" class="k-select" aria-hidden="true">
              <span class="k-icon k-i-close" (click)="removeCustomerTag(i)">
                <i class="fa fa-cross-circle-right fa-lg" (click)="removeCustomerTag(i)"></i>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div> -->

  <kendo-multiselect [autoClose]="false" class="k-multiselect" [data]="AllLocationList" [(ngModel)]="ReportData.Customer"
  [filterable]="true" [textField]="'CustName'" [valueField]="'CustomerID'" (filterChange)="filterCustomer($event)"
  name="customer" #customer="ngModel" (valueChange)="valueChange()" required>
    <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
      <div class="container drodown-filter-location">
        <div class="row">
          <div class="col-md-6 col-6">
            <span class="mutiselectTextPosition">Customer Name</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="template" class="mutiselectTextPosition" style="position: relative; right: 1%;">Code</span>
          </div>
          <div class="col-md-3 col-3" style="display: flex;align-items: center;justify-content: center;">
            <span class="mutiselectTextPosition">
              <button type="submit" (click)="OnSelectAllCustomerChange()" *ngIf="hasAllCustomerValueSelected===false"
                class="btn selectAllButtonCss">Select All</button>
              <button type="submit" (click)="OnDeSelectAllCustomerChange()" *ngIf="hasAllCustomerValueSelected===true"
                class="btn deselectAllButtonCss">Deselect All</button>
            </span >
            <span style="padding-left: 5px;" class="mutiselectTextPosition">
              <button type="submit" (click)="closeCustomer('yes')"
                class="btn selectAllButtonCss">Done</button>
            </span>
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-6">

            <span class="template md-size location-position">{{dataItem.CustName | titlecase}}</span>
          </div>
          <div class="col-md-4 col-4">
            <span class="md-size">{{dataItem.CustomerCode }}</span>
          </div>
          <div class="col-md-2 col-2">
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-multiselect>

  <div *ngIf="showCustomerError == true">
    <div class="invalid-login">
      <span class="errorMessage">Customer is required !</span>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="container-fluid" style="padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton style="width: 29% !important; width: 29% !important;
          background: #00ccff !important;
          color: white;" class="ButtonFooterDialog" [primary]="true" (click)="clearAllCustomer()" primary="true">Clear
            All</button>
        </div>
        <div class="col-md-6 col-6 text-right">
          <button kendoButton class="ButtonFooterDialog" [primary]="true" style="float: right;width: 29% !important;
            background: #00ccff !important; color: white;" (click)="closeCustomer('yes')" primary="true">
            Add
          </button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<kendo-dialog class="multiselextDropdownHeader" title="Choose Contact" *ngIf="showContact" (close)="closeContact('cancel')"
  [minWidth]="300">
  <!-- <div class="container-fluid ul-container">
    <div class="row ul-row">
      <ul style="display: contents;">
        <li *ngFor="let item of ReportData.Customer;index as i;">
          <span class="span-content">
            <span>{{item.CustName}}</span>
            <span aria-label="delete" class="k-select" aria-hidden="true">
              <span class="k-icon k-i-close" (click)="removeCustomerTag(i)">
                <i class="fa fa-cross-circle-right fa-lg" (click)="removeCustomerTag(i)"></i>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div> -->

  <kendo-multiselect [autoClose]="false" class="k-multiselect" [data]="contactData" [(ngModel)]="ReportData.Contact"
  [filterable]="true" [textField]="'Name'" [valueField]="'ContactID'" (filterChange)="filterContact($event)"
  name="contact" #contact="ngModel" (valueChange)="valueChange()" required>
    <ng-template kendoDropDownListHeaderTemplate let-dataItem style="width: auto;">
      <div class="container drodown-filter-location">
        <div class="row">
          <div class="col-md-6 col-6">
            <span class="mutiselectTextPosition">Contact</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="template" class="mutiselectTextPosition" style="position: relative; right: 1%;">Code</span>
          </div>
          <div class="col-md-3 col-3">
            <span class="mutiselectTextPosition">
              <button type="submit" (click)="OnSelectAllContactChange()" *ngIf="hasAllContactValueSelected===false"
                class="btn selectAllButtonCss">Select All</button>
              <button type="submit" (click)="OnDeSelectAllContactChange()" *ngIf="hasAllContactValueSelected===true"
                class="btn deselectAllButtonCss">Deselect All</button>
            </span>
            <span style="padding-left: 5px;" class="mutiselectTextPosition">
              <button type="submit" (click)="closeContact('yes')"
                class="btn selectAllButtonCss">Done</button>
            </span>
            
          </div>
        </div>
      </div>
    </ng-template>
    <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
      <div class="container">
        <div class="row">
          <div class="col-md-6 col-6">

            <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
          </div>
          <div class="col-md-4 col-4">
            <span class="md-size">{{dataItem.Name }}</span>
          </div>
          <div class="col-md-2 col-2">
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-multiselect>

  <div *ngIf="showContactError == true">
    <div class="invalid-login">
      <span class="errorMessage">Contact is required !</span>
    </div>
  </div>
  

  <kendo-dialog-actions>
    <div class="container-fluid" style="padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton style="width: 29% !important; width: 29% !important;
          background: #00ccff !important;
          color: white;" class="ButtonFooterDialog" [primary]="true" (click)="clearAllContact()" primary="true">Clear
            All</button>
        </div>
        <div class="col-md-6 col-6 text-right">
          <button kendoButton class="ButtonFooterDialog" [primary]="true" style="float: right;width: 29% !important;
            background:  #00ccff !important; color: white;" (click)="closeContact('yes')" primary="true">
            Add
          </button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>


<admin-footer class="monthlyReportFooterCss"></admin-footer>