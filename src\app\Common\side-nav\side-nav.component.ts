import { Component, Output, EventEmitter } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { SharedDataService } from 'src/app/Services/shared-data.service';

@Component({
  selector: 'app-side-nav',
  templateUrl: './side-nav.component.html',
  styleUrls: ['./side-nav.component.scss']
})
export class SideNavComponent {
  @Output() toggle = new EventEmitter<void>();
  currentUrl: string = '';
  toggles: { [key: string]: boolean } = {};
  toggleKeys = [
    {title: 'Asset Performance', key:'assetPerformance' },
    { title: 'Workflow', key: 'workflow' },
    { title: 'Backlog', key: 'backlog' },
    { title: 'Tuning', key: 'tuning' },
    { title: 'Awaiting Approval', key: 'awaitingApproval' }
  ];
  
  togglesForIAQ: { [key: string]: boolean } = {};
  toggleKeysForIAQ = [
    {key:'Temperature' },
    { key: 'Humidity'},
    { key: 'CO2'}
  ];

  dataAvailable = {
    Temperature: false,
    Humidity: false,
    CO2: false
  }

  isTuningModuleActive: boolean = false;
  
  constructor(public router: Router, private shareData: SharedDataService) {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.currentUrl = event.url;
      });
  }

  ngOnInit() {
    this.shareData.toggles$.subscribe(values => {
      this.toggles = values;
    });
    this.shareData.togglesForIAQ$.subscribe(values => {
      this.togglesForIAQ = values;
    });
    this.isTuningModuleActive = JSON.parse(localStorage.getItem("TuningModule"));
    this.shareData.temperatureAvailable$.subscribe(val => this.dataAvailable.Temperature = val );
    this.shareData.humidityAvailable$.subscribe(val => this.dataAvailable.Humidity = val);
    this.shareData.co2Available$.subscribe(val => this.dataAvailable.CO2= val);
  }

  onToggleChange(key: string, event: any) {
    this.shareData.updateToggle(key, !event.target.checked);
  }
  onToggleChangeForIAQ(key: string, event: any) {
    this.shareData.updateToggleForIAQ(key, !event.target.checked);
  }
}
