<admin-header></admin-header>
<section class="dashboard-header section-padding section-margin">

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title">User Profile</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class=" col-md-12 col-12">
                                <table class="table table-user-infsormation">
                                    <tbody>
                                        <tr>
                                            <td> <i class="material-icons">
                                                    person
                                                </i><span class="customCss">{{LoggedInUserName}}</span></td>
                                            <td> 
                                                <a href="javascript:void(0)" (click)="changePassword()">
                                                    <i  aria-hidden="true"></i>
                                                    <span>Change Password</span>
                                                  </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <i class="material-icons">
                                                    people
                                                </i><span class="customCss">Customer Name</span>
                                            </td>
                                            <td class="autoWidth">
                                                <i class="material-icons">
                                                    location_on
                                                </i><span class="customCss">Location</span>
                                            </td>

                                        </tr>
                                        <tr class="tr-md" *ngFor="let data of customerLocation">
                                            <td>
                                                <span class="customCss default"><b>{{data.customerName}}
                                                    </b></span></td>
                                            <td class="autoWidth " class="UserInfoGrid">
                                                <div class="externalDiv">
                                                    <kendo-grid  [data]="data.locations"  
                                                       >
                                                        <kendo-grid-column field="Name" title="Location Name"
                                                            [width]="170">
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="StateCode" title="State" [width]="70">
                                                        </kendo-grid-column>
                                                        <kendo-grid-column field="totalEquipments"
                                                            title="Total Equipment" width="135">
                                                        </kendo-grid-column>
                                                    </kendo-grid>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<admin-footer [ngClass]="{'topFooterCss':customerLocation.length<4}" class="momFooterCss" 
 [ngStyle]="{
    position: 'absolute', 
    bottom: customerLocation.length > 4 ? 'auto' : '0', 
    left: '0', 
    right: '0',
    top: 'auto'
  }"></admin-footer>