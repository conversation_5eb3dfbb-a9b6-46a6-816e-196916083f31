<admin-header (addLocationListOnRefreshBrowser)='locationChange($event)'></admin-header>
<section class="dashboard-header section-padding margin-top-section">

  <div class="container-fluid width-auto ">

    <div class="row" *ngFor="let item of items;">
      <kendo-panelbar [keepItemContent]="true" class="panel-bar-width" (stateChange)="onPanelChange($event)">
        <kendo-panelbar-item [title]="item.title" [expanded]="item.expanded" style="font-family: calibri light ,'Roboto' !important;">
          <ng-template kendoPanelBarContent class="panelContentPadding-auto" *ngIf="item.title==='Search & Download'">
            <div [class]="'custom-template'">
              <div class="container-fluid" style="margin-top: 0px;
                    padding: 0px;">
                <div class="row">
                  <div class="col-md-3 col-xs-offset-1">
                    <div class="panel panel-default panel-table">
                      <div class="panel-heading">
                        <div class="tr">
                          <div class="td">Category</div>
                        </div>
                      </div>
                      <div class="panel-body">
                        <div class="tr">
                          <div class="td" style="padding: 0px 0px !important;">
                            <select multiple="multiple" name="SelectedDocuments" (change)="onItemChange(multiple)"
                              [(ngModel)]="SelectedDocuments" class="custom-select mb-3">
                              <option [value]="data.DocumentTypeId" *ngFor="let data of DocumentList">{{data.Name}}
                              </option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-9 bulkReportDownload">
                    <kendo-grid [data]="gridData" scrollable="true">
                      <ng-template style="text-align: right;" kendoGridToolbarTemplate>
                        <div class="container-fluid p-0">
                          <div class="row">
                            <div class="col-md-6">
                              <h1 class="md-h1" style="font-size: 13px;">Criteria Type</h1>
                            </div>
                            <div class="col-md-6 text-right">
                            </div>
                          </div>
                        </div>
                      </ng-template>
                      <kendo-grid-column field="PropertyName" width="200" title="Criteria">

                      </kendo-grid-column>

                      <kendo-grid-column field="UserPrompt" width="200" title="">
                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                          <div *ngIf="dataItem.DataType===0 && dataItem.PropertyName!='Location ID'">
                            <kendo-dropdownlist [value]="StringDataTypeDropdown[0]" class="bulkDocDropDown"
                              [textField]="'key'" (valueChange)="stringDateChange(dataItem,$event)"
                              [data]="StringDataTypeDropdown">
                            </kendo-dropdownlist>
                          </div>
                          <div *ngIf="(dataItem.DataType===1 && dataItem.PropertyName!='Location ID') || (dataItem.DataType===2 && dataItem.PropertyName!='Location ID') || (dataItem.DataType===3 && dataItem.PropertyName!='Location ID') || (dataItem.DataType===4 && dataItem.PropertyName!='Location ID')">
                            <kendo-dropdownlist [value]="DateNumberDropdown[4]"
                              (valueChange)="stringChange(dataItem,$event)" class="bulkDocDropDown"
                              [data]="DateNumberDropdown">
                            </kendo-dropdownlist>
                          </div>
                          <div *ngIf="dataItem.PropertyName==='Location ID'">
                            <kendo-dropdownlist class="bulkDocDropDown" [value]="selectedValue"
                              [data]="locationInDropdown">
                            </kendo-dropdownlist>
                          </div>
                        </ng-template>

                      </kendo-grid-column>
                      <kendo-grid-column width="200" field="" title="Search For">
                        <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">

                          <div *ngIf="dataItem.PropertyName!='Location ID' && dataItem.DataType===2">

                            <kendo-datepicker *ngIf="allOperator" class="datePickerBulkDoc" [format]="'dd-MM-yyyy'" name='test'
                              [value]="dataItem.date" #ref (blur)="onKeyPress(ref,dataItem,'datePicker')"
                              (open)="onOpenDatePicker(dataItem,dataItem.isEdited)" style="font-weight: 100 !important; font-size: 12px !important; width: 89% !important;">
                            </kendo-datepicker>

                            <kendo-daterange #daterange *ngIf="betweenOperator">
                              <label style="margin-right: 10px;">
                                <kendo-dateinput [format]="'dd/MM/yyyy'" class="dateInputCss" kendoDateRangeStartInput [(value)]="bwrange.start"

                                name="start" style="font-weight: 100 !important; font-size: 12px !important;"></kendo-dateinput>
                              </label>
                                <span class="label"></span>
                              <label>
                                <kendo-dateinput [format]="'dd/MM/yyyy'" class="dateInputCss" kendoDateRangeEndInput [(value)]="bwrange.end"

                                name="end" style="font-weight: 100 !important; font-size: 12px !important;"></kendo-dateinput>
                              </label>
                            </kendo-daterange>

                          </div>
                          <div *ngIf="dataItem.PropertyName!='Location ID' && dataItem.DataType===0">
                            <input #ref (blur)="onKeyPress($event,dataItem,'searchBox')"
                              style="font-size: 12px; width: 89% !important;" class="k-textbox"
                              placeholder="Enter Text" />
                          </div>
                          <div *ngIf="dataItem.DataType===3 && dataItem.PropertyName!='Location ID'">
                            <kendo-numerictextbox [spinners]="false" [decimals]="'7'" [format]="'n7'" #ref
                              (blur)="onKeyPress(ref,dataItem,'numericbox')">
                            </kendo-numerictextbox>
                          </div>
                          <div *ngIf="dataItem.DataType===4 && dataItem.PropertyName!='Location ID'">
                            <kendo-numerictextbox [spinners]="false" [format]="format" [value]="NumericTextBoxValue"
                              #ref (blur)="onKeyPress(ref,dataItem,'numericbox')">
                            </kendo-numerictextbox>
                          </div>
                          <div *ngIf="dataItem.DataType===1 && dataItem.PropertyName!='Location ID'">
                            <kendo-numerictextbox [spinners]="false" [decimals]="'0'" [format]="'n'" #ref
                              (blur)="onKeyPress(ref,dataItem,'numericbox')">
                            </kendo-numerictextbox>
                          </div>
                          <div *ngIf="dataItem.PropertyName==='Location ID'">
                            <div class="displayInline"
                              *ngIf="selectedLocationList.length>0 && selectedLocationList.length!=listItems.length">
                              <button class="k-button" style="background-color: #00ccff !important;" kendoButton
                                (click)="open(dataItem)">{{selectedLocationList.length>1 ? defaultLocationAsString+' + '+(selectedLocationList.length-totalCount)+" more":totalSelectedLocations}}</button>
                            </div>
                            <div class="displayInline" *ngIf="selectedLocationList.length===0">
                              <button class="k-button" style="background-color: #00ccff !important;" kendoButton (click)="open(dataItem)">{{defaultDropdown}}</button>
                            </div>
                            <div class="displayInline" *ngIf="selectedLocationList.length===listItems.length">
                              <button class="k-button" style="background-color: #00ccff !important;" kendoButton (click)="open(dataItem)">All Locations</button>
                            </div>
                          </div>
                        </ng-template>
                      </kendo-grid-column>
                    </kendo-grid>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-12 text-right">
                    <button type="button" (click)="showRecords()" class="k-button bottom-button" style="background-color: #00ccff !important;">Search</button>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>

          <ng-template kendoPanelBarContent style="padding: 19px;" *ngIf="item.title==='Search Results'">
            <div [class]="'custom-template'">
              <section style="display: block; margin-bottom: 0;">
                <kendo-grid
                    class="OpenCallsGrid"
                    [data]="gridDataDetails"

                    [pageSize]="state.take"
                    [skip]="state.skip"
                    [sort]="state.sort"
                    [filter]="state.filter"
                    [sortable]="{allowUnsort: true, mode:'multiple'}"
                    filterable="menu"
                    [pageable]="true"
                    (dataStateChange)="dataStateChange($event)"
                    [selectable]="selectableSettings"
                    [kendoGridSelectBy]="'DocumentId'"
                    [selectedKeys]="mySelection"
                    (selectedKeysChange)="onSelectedKeysChange($event)">

                  <kendo-grid-checkbox-column width="50">
                    <ng-template kendoGridHeaderTemplate>
                      <input id="selectAllCheckboxId" kendoGridSelectAllCheckbox
                        [state]="selectAllState" (selectAllChange)="onSelectAllChange($event)">
                      <label class="k-checkbox-label" for="selectAllCheckboxId"></label>
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-idx="rowIndex">
                      <input [kendoGridSelectionCheckbox]="idx" name="checkbox" />
                  </ng-template>
                  </kendo-grid-checkbox-column>
                  <kendo-grid-column field="DocumentType" title="Document Name" width="170">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem?.DocumentType}}
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="ServiceCall" title="Service Call" width="140">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem?.ServiceCall}}
                    </ng-template>
                  </kendo-grid-column>
                  <kendo-grid-column field="LocationName" title="Location" width="150">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem?.LocationName}}
                    </ng-template>
                  </kendo-grid-column>
                  <kendo-grid-column field="DebtorCode" title="Debtor" width="140">
                    <ng-template kendoGridCellTemplate let-dataItem>
                      {{dataItem?.DebtorCode}}
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="" title="" width="180">
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                      <div *ngIf="selectedRowIndex===undefined && selectedCheckbox.length>0">
                        <div *ngFor="let data of selectedCheckbox">
                          <div *ngIf="dataItem.DocumentId===data.DocumentId && isBulkDownloadClicked===true"
                            [ngClass]="{'hideButton':(fileDownloaded===true && progressPercentage===100)}">
                            <progress-bar class="ProgressBarCss" [progress]="progressPercentage">
                            </progress-bar>
                          </div>
                        </div>
                      </div>
                      <div *ngIf="rowIndex===selectedRowIndex && isBulkDownloadClicked===true"
                        [ngClass]="{'hideButton':(fileDownloaded===true && progressPercentage===100)}">
                        <progress-bar class="ProgressBarCss" [progress]="progressPercentage">
                        </progress-bar>
                      </div>
                    </ng-template>
                  </kendo-grid-column>

                  <kendo-grid-column field="" title="" width="110">
                    <ng-template kendoGridHeaderTemplate>
                      <button kendoButton class="downloadButton" style="background-color: #00ccff !important;"  (click)="DownloadDoc()"
                        primary="true">DownLoad All</button>
                    </ng-template>
                    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                      <button kendoButton class="followupButton downloadButton" [primary]="true"
                        (click)="DownloadDoc(dataItem,rowIndex)" style="background-color: #00ccff !important;"><span
                          class="k-icon k-i-download"></span></button>
                    </ng-template>
                  </kendo-grid-column>
                </kendo-grid>
              </section>

            </div>
          </ng-template>
        </kendo-panelbar-item>
      </kendo-panelbar>
    </div>
  </div>
</section>


<kendo-dialog class="multiselextDropdownHeader" title="Choose Locations" *ngIf="opened" (close)="close('cancel')"
  [minWidth]="300">
  <!-- <div class="container-fluid ul-container">
    <div class="row ul-row">
      <ul style="display: contents;">
        <li *ngFor="let item of selectedLocationList;index as i;">
          <span class="span-content">
            <span>{{item.Name}}</span>
            <span aria-label="delete" class="k-select" aria-hidden="true">
              <span class="k-icon k-i-close" (click)="removeTag(i)">
                <i class="fa fa-cross-circle-right fa-lg" (click)="removeTag(i)"></i>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div> -->
  <div *ngIf="selectedLocationList.length===1">
    <kendo-multiselect [data]="LocationData" [textField]="'Name'" [textField]="'CustName'" class="DisabledTemplate"
      [valueField]="'LocationID'" [filterable]="true" (filterChange)="filterLocation($event)" [autoClose]="false"
      (close)="close()" (valueChange)='setValues()' [(ngModel)]="tempSelectedLocationList" [itemDisabled]="itemDisabled">
      <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
        <div class="container drodown-header-location">
          <div class="row">
            <div class="col-md-6 col-6 mobileAutoPadding">
              <span class="mutiselectTextPosition" style="  position: relative;
            left: 9%;">Location</span>
            </div>
            <div class="col-md-3 col-3">
              <span class="template" class="mutiselectTextPosition" style="  position: relative;
        right: 4%;">Customer Name</span>
            </div>
            <div class="col-md-3 col-3">
              <span class="mutiselectTextPosition">
                <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                  class="btn selectAllButtonCss"><b>Select All</b></button>
                <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                  class="btn deselectAllButtonCss"><b>Deselect All</b></button>
              </span>
            </div>
          </div>
        </div>
      </ng-template>
      <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
        <div class="container">
          <div class="row">
            <div class="col-md-6 col-6">
              <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
            </div>
            <div class="col-md-6 col-6">
              <span class="md-size">{{dataItem.CustName | titlecase}}</span>
            </div>
          </div>
        </div>
      </ng-template>
    </kendo-multiselect>
  </div>
  <div *ngIf="selectedLocationList.length>1">
    <kendo-multiselect [autoClose]="false" [data]="LocationData" #list [textField]="'Name'" [textField]="'CustName'"
      [valueField]="'LocationID'" [filterable]="true" (filterChange)="filterLocation($event)" [(ngModel)]="tempSelectedLocationList"
      (valueChange)='setValues()' (close)="close()">
      <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
        <div class="container drodown-header-location">
          <div class="row">
            <div class="col-md-6 col-6 mobileAutoPadding">
              <span class="mutiselectTextPosition" style="position: relative; left: 9%;">Location</span>
            </div>
            <div class="col-md-3 col-3">
              <span class="template" class="mutiselectTextPosition" style="  position: relative;
        right: 4%;">Customer Name</span>
            </div>
            <div class="col-md-3 col-3">
              <span class="mutiselectTextPosition">
                <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                  class="btn selectAllButtonCss"><b>Select All</b></button>
                <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                  class="btn deselectAllButtonCss"><b>Deselect All</b></button>
              </span>
            </div>
          </div>
        </div>
      </ng-template>
      <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;">
        <div class="container">
          <div class="row">
            <div class="col-md-6 col-6">
              <span class="template md-size location-position">{{dataItem.Name | titlecase}}</span>
            </div>
            <div class="col-md-6 col-6">
              <span class="md-size">{{dataItem.CustName | titlecase}}</span>
            </div>
          </div>
        </div>
      </ng-template>
    </kendo-multiselect>
  </div>
  <kendo-dialog-actions>
    <div class="container-fluid" style="padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton style="width: 29% !important;" class="ButtonFooterDialog" [primary]="true"
            (click)="clearAll()" style="background-color: #00ccff !important;">Clear All</button>
        </div>
        <div class="col-md-6 col-6 text-right">
          <button kendoButton class="ButtonFooterDialog" [primary]="true" style="    float: right;"
            (click)="SetLocations()" style="background-color: #00ccff !important;">Add</button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
<admin-footer [ngClass]="{'footerCssOnToggleClose':isExpanded==false}" class="dashboardFooterCss"></admin-footer>