
// {
//   "compileOnSave": false,
//   "compilerOptions": {
//     "paths": { "*": ["types/*"] },
//     "baseUrl": "./",
//     "outDir": "./dist/out-tsc",
//     "skipLibCheck": true,
//     "sourceMap": true,
//     "declaration": false,
//     "module": "es2015",
//     "moduleResolution": "node",
//     "emitDecoratorMetadata": true,
//     "experimentalDecorators": true,
//     "target": "es5",
//     "typeRoots": [
//       "node_modules/@types"
//     ],

//     "lib": [
//       "es2017",
//       "dom"
//     ]
//   }
// }

{
  "compileOnSave": false,
  "include": [
    "./src"
  ],
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "module": "esnext",
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "target": "es5",
    "typeRoots": [
      "node_modules/@types"
    ],
    "lib": [
      "es2018",
      "dom"
    ],
    "types": [
      "node"
    ],
    "skipLibCheck": true
  },
  "angularCompilerOptions": {
    "enableIvy": false
  }
}
