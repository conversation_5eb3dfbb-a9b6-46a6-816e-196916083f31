<admin-header></admin-header>
<section class="dashboard-header section-padding margin-top-section">
  <div class="onedrive container-fluid width-auto">
    <div class="row">
      <div class="col-12 mt-4">
        <div class="header-bar" style="margin-top: 30px !important">
          <div class="HeaderTools">
            <!-- [openOnClick]="true" -->
            <kendo-menu *ngIf="!hideNewMenu" class="newUI" [kendoMenuFlatBinding]="NewMenuItems"
              (select)="onSelectNewMenu($event)" idField="id" parentIdField="parentId" textField="name"
              iconField="iconName">
            </kendo-menu>

            <kendo-menu *ngIf="!hideUploadMenu" class="uploadUI" (select)="onSelectUploadMenu($event)">
              <kendo-menu-item icon="upload" text="Upload">
                <kendo-menu-item icon="file" text="File">
                </kendo-menu-item>
                <!-- <kendo-menu-item icon="folder" text="Folder" (select)="onSelectMenu($event)">
                </kendo-menu-item> -->
              </kendo-menu-item>
            </kendo-menu>
            <input id="uploadFile" #uploadFile type="file" style="display:none" (change)="uploadSelectedFile($event)">
            <!-- <kendo-menu class="uploadUI"
                            [kendoMenuFlatBinding]="UploadMenuItems"
                            (select)="onSelectMenu($event)"
                            idField="id"
                            parentIdField="parentId"
                            textField="name"
                            iconField="iconName">
                      </kendo-menu> -->

            <kendo-menu class="downloadUI" [openOnClick]="true">
              <kendo-menu-item icon="download" text="Download">
              </kendo-menu-item>
            </kendo-menu>

            <!-- <kendo-upload [saveUrl]="uploadSaveUrl" (select)="openFile($event)" [showFileList]="false"
              (upload)="uploadEventHandler($event)">
            </kendo-upload> -->
          </div>
          <!-- <div class="group2">
            <kendo-menu class="drive-menu" [kendoMenuFlatBinding]="data3" (select)="onClickData2($event)" idField="id"
              parentIdField="parentId" textField="name" iconField="iconName">
            </kendo-menu>
            <kendo-menu class="drive-menu" [kendoMenuFlatBinding]="data4" (select)="onClickData2($event)" idField="id"
              parentIdField="parentId" textField="name" iconField="iconName">
            </kendo-menu>
          </div> -->
        </div>

        <kendo-breadcrumb class="breadCrumbUI" [items]="defaultBreadCrumbItems" (itemClick)="onBreadCrumbClick($event)">
          <ng-template kendoBreadCrumbItemTemplate let-item let-index="index">
            {{item.text.split('+')[0]}}
          </ng-template>
        </kendo-breadcrumb>
        <div [ngStyle]="{'height.px': pageHeight}" style="margin-bottom: 20px;" #container>
          <kendo-grid
                  [data]="DriveData"
                  [height]="pageHeight"
                  [hideHeader]="true"
                  (cellClick)="onCellClick($event)"
                  #driveGrid>
            <kendo-grid-column [headerStyle]="{'background-color': '#666','color': '#fff',
                    'line-height': '1em'}">
              <ng-template kendoGridCellTemplate let-dataItem>
                <div *ngIf="dataItem.Type.toUpperCase()=='FOLDER'">
                  <div *ngIf="isRoot == true">
                    <a [routerLink]="" (click)="cellClickHandler(dataItem)">
                      <span [ngClass]="iconClass(dataItem)"></span>&nbsp;
                      {{ dataItem.CustName }}
                    </a>
                  </div>
                  <div *ngIf="isRoot == false">
                    <a [routerLink]="" (click)="cellClickHandler(dataItem)">
                      <span [ngClass]="iconClass(dataItem)"></span>&nbsp;
                      {{ dataItem.LOCATNNM ? dataItem.LOCATNNM : dataItem.Name }}
                    </a>
                  </div>
                </div>
                <div *ngIf="dataItem.Type.toUpperCase()!='FOLDER'">
                  <span [ngClass]="iconClass(dataItem)"></span>&nbsp;
                  {{ dataItem.Name }}
                </div>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
        </div>
        <!-- Right Clik Context Menu -->
        <grid-context-menu [for]="driveGrid" [menuItems]="contextMenuItem" (select)="onContextItemSelect($event)"
          #driveContextMenu>
          <ng-template kendoContextMenuTemplate let-item="item">
            <span [ngClass]="contextIconClass(item)"></span>&nbsp;
            {{ item }}
          </ng-template>
        </grid-context-menu>
      </div>
    </div>
  </div>
</section>
<!-- Create Folder under Location by Providing Name -->
<kendo-dialog *ngIf="openCreateFolderPopup" class="CreateFolderPopup" #CreateFolderPopup id="CreateFolderPopup"
(close)="openCreateFolderPopup=false">
<kendo-dialog-titlebar>
  <div style="font-size: 18px; line-height: 1.3em;">
    Create a folder<br>
  </div>
</kendo-dialog-titlebar>

  <p style="margin-bottom: 0px !important;">
    <input class="k-textbox" placeholder="Enter your folder name" (keypress)="validateFolderNameLength(folderName)"
      [(ngModel)]="folderName" maxlength="50" style="margin-top: 5px !important; width: 100% !important;" />
  </p>
  <kendo-dialog-actions>
    <button type="button" kendoButton (click)="createFolder('')" primary="true" style="background: #3f51b5 !important;">Create</button>
  </kendo-dialog-actions>
</kendo-dialog>

<!-- Create Folder(s) under Customer by Available Locations Dialog -->
<kendo-dialog *ngIf="openCreateFolderByLocationPopup" class="DasBoardDialog"
  (close)="openCreateFolderByLocationPopup=false" [minWidth]="400" #CreateFolderByLocationPopup>
  <kendo-dialog-titlebar>
    <div style="font-size: 18px; line-height: 1.3em;">
      Choose Location(s) to create folder<br>
    </div>
  </kendo-dialog-titlebar>
  <kendo-multiselect  [data]="createLocationFolderData"
                      [itemDisabled]="itemDisabled"
                      [autoClose]="false"
                      [textField]="'LocationName'"
                      [textField]="'CustomerName'"
                      (close)="close()"
                      [valueField]="'LocationCode'"
                      [filterable]="true"
                      (filterChange)="filterChange($event)"
                      (valueChange)="onAllSelected()"
                      [(ngModel)]="locationListForFolder">
    <ng-template kendoMultiSelectHeaderTemplate let-dataItem style="width: auto;">
      <div class="container drodown-header-location">
        <div class="row">
          <div class="col-md-1 col-1"></div>
          <div class="col-md-5 col-5">
            <span class="mutiselectTextPosition" style="position: relative; left: 3%;">Location</span>
          </div>
          <div class="col-md-6 col-6">
            <span class="template" class="mutiselectTextPosition" style="position: relative; right: 8%;">Customer</span>
          </div>
          <!-- <div class="col-md-2 col-2 selectCss" style="padding-right: 0; padding-left: 0;">
            <span class="mutiselectTextPosition">
              <button type="submit" (click)="OnSelectAllChange()" *ngIf="hasAllValueSelected===false"
                class="btn selectAllButtonCss"><b>Select All</b></button>
              <button type="submit" (click)="OnDeSelectAllChange()" *ngIf="hasAllValueSelected===true"
                class="btn deselectAllButtonCss"><b>Deselect All</b></button>
            </span>
          </div> -->
        </div>
      </div>
    </ng-template>
    <ng-template kendoMultiSelectItemTemplate let-dataItem style="width: 112%;" #FolderLocationsTemplate>
      <div class="container">
        <div class="row">
          <div class="col-md-1 col-1">
            <span *ngIf="dataItem.Status==0" class="fa-stack" style='margin-right: 10px;width: 1.1em;font-size:24px;color:#007bff'>
              <i class="fas fa-folder fa-stack-1x" ></i>
              <i class="fas fa-check fa-stack-1x" style='color:white;font-size:12px;margin-top:1px'></i>
            </span>
            <span *ngIf="dataItem.Status==1" style="margin-right: 10px;">
              <i class='fas fa-folder-plus' style='font-size:25px;color:lightgreen'></i>
            </span>

              <!-- <span *ngIf="dataItem.Status==0" class="k-icon k-i-checkbox-checked" style="color: black;"></span> -->
              <!-- <span *ngIf="dataItem.Status==1" class="k-icon k-i-folder-add" style="color: black;"></span> -->
          </div>
          <div class="col-md-5 col-5">
            <span class="template customerName-MDcSS" style="position: relative; right: 6%;">{{dataItem.LocationName |
              titlecase}}</span>
          </div>
          <div class="col-md-6 col-6">
            <span class="template" style="position: relative; right: 6%;">{{dataItem.CustomerName | titlecase}}</span>
          </div>
        </div>
      </div>
    </ng-template>
  </kendo-multiselect>
  <kendo-dialog-actions>
    <div class="container-fluid" style="padding: 0px 12px;">
      <div class="row">
        <div class="col-md-6 col-6">
          <button kendoButton class="buttonFooter" (click)="clearAll()">Clear
            All</button>
        </div>
        <div class="col-md-6 col-6">
          <button class="buttonFooter" kendoButton [primary]="true" style="float: right; background-color: #3f51b5 !important;" (click)="createFolder('FROMLOCATION')"
            primary="true">Create</button>
        </div>
      </div>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>

<admin-footer class="dashboardFooterCss"></admin-footer>

<!-- field="Name"
title="Name" -->

<!-- <kendo-grid-checkbox-column width="5%">
  <ng-template kendoGridHeaderTemplate let-dataItem>
    <input style="width: 20px;height: 20px;"
           type="checkbox"
           name="selectAll"
           (change)="selectAllRows($event)"
           [checked]="allRowsSelected"/>
  </ng-template>
</kendo-grid-checkbox-column> -->