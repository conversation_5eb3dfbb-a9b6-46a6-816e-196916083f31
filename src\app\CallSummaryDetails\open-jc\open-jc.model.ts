export class OpenJCListModel {
    JobNumber: string;
    JobName: string;
    ExpectedContract: string;
    StartDate: Date;
    EndDate: Date;
    Divisions: string;
} 
export class GridListModel {
    JobNumber: string;
    JobName: string;
    ExpectedContract: string;
    StartDate: Date;
    EndDate: Date;
    Divisions: string;
} 
export class locationDateFilterEntity {
    start: string;
    end: string;
    locations: string;
}