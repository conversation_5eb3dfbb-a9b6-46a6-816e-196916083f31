<admin-header (valueChange)='locationChange($event)'></admin-header>

<section class="margin-top-table-list">
    <div [ngStyle]="{'height.px': pageHeight}">
    <kendo-grid class="gridFontStyle" [data]="gridData" [skip]="state.skip" [sort]="state.sort" [style.height]="'100%'"
        [sortable]="{allowUnsort: true, mode:'multiple'}" [filter]="state.filter" filterable="menu" (filterChange)="filterChange($event)"
        (dataStateChange)="dataStateChange($event)" (scrollBottom)="loadMore()" [navigable]="true"[loading]="loading"
    >
        <ng-template style="text-align: right;" kendoGridToolbarTemplate>
            <div class="container-fluid p-0">
                <div class="row">
                    <div class="col-md-6 col-6">
                        <h1>PPM Completion %</h1>
                    </div>
                    <div class="col-md-6 col-6 text-right">
                        <button type="button" [disabled]="MCCList.length===0" class="ExportToExcelButtonCss" kendoGridExcelCommand
                            icon="file-excel">
                            <!-- <span class="k-icon k-i-file-excel" role="presentation"></span> -->
                            Export to Excel</button>
                    </div>
                </div>
            </div>
        </ng-template>

        <kendo-grid-column field="ServiceCallID" title="Service Call ID" width="120">
        </kendo-grid-column>
        <kendo-grid-column field="LocationName" title="Location Name" width="210">
            <ng-template kendoGridCellTemplate let-dataItem>
               {{dataItem?.LocationName}}
            </ng-template>    
        </kendo-grid-column>

        <kendo-grid-column field="StatusCall" title="Call Status" width="120">
            <ng-template kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService"
            >
            <kendo-multiselect
                style="width:150px"
                [data]="statusCalls"
                textField="StatusCall"
                valueField="StatusCall"
                [valuePrimitive]="true"
                [value]="dropdownFilters(filter)"
                (valueChange)="statusCallChange($event, filterService)"
            >
            </kendo-multiselect> 
        </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="Date" title="Date" width="120" filter="date" operator="eq" format="{0:dd-MM-yyyy}">
            <ng-template kendoGridFilterMenuTemplate let-filter let-column="column" let-filterService="filterService">
                <kendo-grid-date-filter-menu [column]="column" [filter]="filter" [filterService]="filterService"
                    operator="eq">
                </kendo-grid-date-filter-menu>
            </ng-template>
            <!-- a -->
        </kendo-grid-column>
        <kendo-grid-column field="ServiceDescription" title="Service Description" width="300">
            <ng-template kendoGridFilterCellTemplate let-filter let-column="column">
                <kendo-grid-string-filter-cell [showOperators]="false" [column]="column" [filter]="filter">
                </kendo-grid-string-filter-cell>
            </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="" title="" width="80">
            <ng-template kendoGridCellTemplate let-dataItem>
                <button [ngClass]="{'hideButton':dataItem.StatusCall==='OPEN'}" kendoButton class="followupButton downloadButton"
                    title="DownLoad Call Summary Report" [primary]="true" (click)="DownloadSummaryReport(dataItem)"
                    primary="true"><span class="k-icon k-i-download"></span></button>
            </ng-template>

        </kendo-grid-column>
        <kendo-grid-excel fileName="PPM %.xlsx" [fetchData]="allData" >
            <kendo-excelexport-column field="ServiceCallID" title="Service Call ID" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="LocationName" title="Location Name" width="210">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="StatusCall" title="Status Call" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="Date" title="Date" width="120">
            </kendo-excelexport-column>
            <kendo-excelexport-column field="ServiceDescription" title="Service Description" width="300">
            </kendo-excelexport-column>
        </kendo-grid-excel>
    </kendo-grid>
    <div *ngIf="isShow" style="text-align: center;">
        <button class="scrollDownBtn" tooltip="Click or Scroll down for more" placement="top" (click)="loadMore()">
            <img src="../../../assets/images/icons8-scroll-down-50.png" alt="" />
        </button>
    </div>
</div>
</section>

<admin-footer class="ppmPercentageCss"></admin-footer>