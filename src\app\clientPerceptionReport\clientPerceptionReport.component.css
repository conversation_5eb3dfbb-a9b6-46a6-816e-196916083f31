/*Font-awesome integration*/
.SubMitButtonCss
{
    width:7%;
}

form {
  padding: 10px 0px;
}
#contact {
  background-color: #f1f1f1;
  font-family: calibri light, "Roboto";
}
.k-form-field {
  display: block;
  text-align: left;
  margin-bottom: -17px;
}
.footer-css {
  position: relative;
  bottom: 0;
  top: 30px;
  left: 0;
  right: 0;
}
.errorMessage {
  color: #ff0000d1;
  font-size: smaller;
}
.mt-top {
  margin-top: -12px;
}
section {
  margin-top: 169px;
}
#contact .well {
  margin-top: 30px;
  border-radius: 0;
}

textarea {
  resize: none;
  min-height: 91px !important;
  width: 98%;
  padding: 6px;
}

/* // span
// {
//     font-family: sans-serif;
//     font-size: 105%;
//     font-weight: 700;
//     position: relative;
    
    
// } */
.defaultSpanPosition {
  font-size: 105%;
  position: relative;
  top: 11px;
  margin-top: 1px;
  margin-bottom: 1px;
  font-family: calibri light, "Roboto";
  font-weight: 600;
  color: #000000f7;
}
/* label{
    display: inline-grid;

} */
.mutiselectTextPosition {
  position: relative;

  text-transform: capitalize;
  font-family: calibri light, "Roboto";
  font-size: 13px;

  letter-spacing: 0px;
  font-weight: 600px;
}
.ul-container {
  padding: 0px 12px;
}
/* .row label{
    margin-bottom: -13px;

 } */
.dropdownListWidth {
  width: 64%;
  position: relative;
  bottom: 0px;
}


.k-textbox {
  top:0px;
  width: 64% !important;
  position: relative;

}



@media (max-width: 411px) {

  .k-textbox {
    top:0px;
    width: 75% !important;
    position: relative;  
  }

.dropdownListWidth {
  width: 75%;
  position: relative;
  bottom: 0px;
}
}
.container .background:hover {
  background: white !important;
}
#contact .form-control {
  border-radius: 0;
  border: 2px solid #1e1e1e;
}
#contact button {
  border-radius: 0;
  border: 2px solid #1e1e1e;
}

#contact .row {
  margin-bottom: 30px;
}

@media (max-width: 1130px) {
  .section-css {
    margin-top: 10px;
  }
}

@media (min-width: 511px) {
  .textarea-css {
    width: 46%;
  }

  .heading-css {
    width: 47%;
  }
}

 

@media (max-width: 850px) {
  .button-css {
    width: 8%;
  }
}

@media (min-width: 851px) {
  .button-css {
    width: 7%;
  }
}

@media (max-width: 510px) {
  .textarea-css {
    width: 100%;
  }

  .heading-css {
    width: 100%;
  }
}
.k-required {
  color: red;
}

.mutiselectTextPosition {
  text-transform: capitalize;
  font-family: calibri light , "Roboto";
  font-size: 13px !important;
  letter-spacing: 0px;
  font-weight: 600px;
}
.k-select:hover {
  transition: 0.5s !important;
  background: black !important;
}

@media only screen and (min-width: 991px) {
  .bar-chart-position {
    margin-top: 0px;
    margin-bottom: 30px;
  }
}

.k-i-close {
  font-size: 12px;
  padding: 2px;
  box-sizing: content-box;
}
.k-select {
  position: relative;
  font-size: 75%;
  bottom: 1px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.4);
  color: #fff;
  margin-left: 0.5em;
  margin-right: -0.2em;
}
.span-content {
  background: #80808042;
  margin-right: 8px;
  padding: 4px;
  font-family: calibri light , "Roboto";
  margin-bottom: -7px;
  font-weight: 500;
  border-radius: 16px;
  margin-top: 4px;
  display: unset !important;
 
}
.title {
  background-color: #343a40;
  font-family: calibri light , "Roboto";
  font-weight: 700;
  padding: 8px 0px;
  font-size: 1rem;
}

.margin-top-12 {
  margin-top: 14px;
}
/* kendo-grid
  {
    width: 68% !important;
  } */

.labelOnTop {
  float: left !important;
  padding: 0 1em !important;
  text-align: center !important;
}
.radioAlign {
  position: fixed;
  margin: 25px 0px 0px 13px;
}


@media (max-width: 1130px) {
    section {
      margin-top: 0px !important;
    }
    textarea,h6,.SubMitButtonCss
    {
        width: 100% !important;

    }
    }

    ::placeholder {
       font-size: 13px;
    }
    

    @media only screen and (max-width: 1090px) {
      .ClientPerception 
      {
        overflow: scroll !important;
        overflow-y: hidden !important;
        padding: 0px 0px 0px 0px !important;
      }
    }