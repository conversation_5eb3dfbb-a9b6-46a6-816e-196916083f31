import { Injectable } from '@angular/core';
import {
  <PERSON>ttpInterceptor, HttpRequest, HttpHandler, HttpEvent,
  HttpProgressEvent, HttpEventType, HttpResponse
} from '@angular/common/http';
import { Observable, of, concat } from 'rxjs';
import { delay } from 'rxjs/operators';
@Injectable()
export class AuthHttpInterceptor implements HttpInterceptor {
  token: any;
  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    //this.token = localStorage.getItem('CPToken');
    if(window.location.hostname == 'localhost') {
      this.token = localStorage.getItem('CPToken');
    }
    else if (window.location.hostname.split(".")[1].toString().toUpperCase() == "AIRMASTER") {
      this.token = localStorage.getItem('CPTokenAIR');

    } else if (window.location.hostname.split(".")[1].toString().toUpperCase() == "AIRMASTERFIRE") {
      this.token = localStorage.getItem('CPTokenAFS');

    }
    else if (window.location.hostname.split(".")[1].toString().toUpperCase() == "OPTIMUMAIR") {
      this.token = localStorage.getItem('CPTokenOPT');
    }
    else if (window.location.hostname.split(".")[1].toString().toUpperCase() == "CONTROLCO") {
      this.token = localStorage.getItem('CPTokenControlco');
    }


    const newRequest = req.clone({
      headers: req.headers.set('Authorization', 'Bearer ' + this.token),
    });

    if (this.token) {
      return next.handle(newRequest);
    }
    if (!this.token) {
      return next.handle(req);
    }

    if (req.url === 'saveUrl') {
      const events: Observable<HttpEvent<any>>[] = [0, 30, 60, 100].map((x) => of(<HttpProgressEvent>{
        type: HttpEventType.UploadProgress,
        loaded: x,
        total: 100
      }).pipe(delay(1000)));

      const success = of(new HttpResponse({ status: 200 })).pipe(delay(1000));
      events.push(success);

      return concat(...events);
    }

    if (req.url === 'removeUrl') {
      return of(new HttpResponse({ status: 200 }));
    }

  }
}
