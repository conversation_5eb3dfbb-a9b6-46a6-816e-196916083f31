import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { OpenDebtGraphEntity, locationDateFilterEntity, OpenDebtChartVALUES } from "./open-debt-chart.model";
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { first } from 'rxjs/operators';

@Component({
  selector: 'app-open-debt-chart',
  templateUrl: './open-debt-chart.component.html',
  styleUrls: ['./open-debt-chart.component.scss']
})
export class OpenDebtChartComponent implements OnInit, OnDestroy {
  seriesColors = ['#9F9F9F', '#74E6B9', '#9F9F9F', '#D55974', '#FDE2E2', '#EE6C4D', '#F79494', '#777777'];
  locations: string;
  chartCategory: string;
  isSubscribed: Subscription;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  start: Date;
  end: Date;
  categories: Array<string> = [];
  openDebtGraphList: OpenDebtGraphEntity[] = [];
  public pieData: any = [];
  Ammount: string = "Amount in";
  currency: String;
  @Input() defaultCurrency: String;
  @Input() startDate: any;
  @Input() endDate: any;
  maxAmount: number = 0;
  TotalControlsJCServices: number;
  TotalInstallJCServices: number;
  TotalServicesJCCallServices: number;
  TotalMaintenanceJCCallsServices: number;
  count: boolean = false;
  Locations: Array<number> = [];
  public openDebtData = [];
  constructor(private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute, private router: Router) {
    // this.shareData.Token$.subscribe()
    this.shareData.removeBacklogData();
    this.chartCategory = this.route.snapshot.paramMap.get('chartCategory');
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));

    if (this.locationDateFilterData != null) {
      if (!!this.locationDateFilterData.locations) {
        this.Locations = this.locationDateFilterData.locations.split(",").map(element => parseInt(element));
      }
    }

    this.isSubscribed = this.shareData.customeridsasObservables.subscribe(data => {
      this.Locations = data;
      //    if (this.auhtentication.isAuthenticated()) {
      // }
    });

  }

  ngOnInit() {

    //  this.loadCount();

  }
  onSeries(event) {

    if (event != null && event != undefined) {
      this.router.navigate(["/OpenDebt", OpenDebtChartVALUES[event.category]]);
    }
  }
  CurrencyToString(value: any) {
    if (value == 0) {

      return 0;
    }
    else {
      // hundreds
      if (value <= 999) {
        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat(this.openDebtGraphList[key].TotalAmount.toString()).toFixed(2).toString();
          this.openDebtData.push(data);
          this.categories.push(this.openDebtGraphList[key].Aged);
        }

      }
      else if (value >= 1000 && value <= 999999) {
        this.currency = "k";
        this.Ammount = "Amount in " + this.currency;
        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat((this.openDebtGraphList[key].TotalAmount / 1000).toString()).toFixed(2).toString();
          this.openDebtData.push(data);
          this.categories.push(this.openDebtGraphList[key].Aged);
        }

      }
      // millions
      else if (value >= 1000000 && value <= 999999999) {
        this.currency = "M";
        this.Ammount = "Amount in " + this.currency;

        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat((this.openDebtGraphList[key].TotalAmount / 1000000).toString()).toFixed(2).toString();
          this.openDebtData.push(data);
          // this.openDebtData=[1,2,4];
          this.categories.push(this.openDebtGraphList[key].Aged);
        }
      }
      // billions
      else if (value >= 1000000000 && value <= 999999999999) {
        this.currency = "B"
        this.Ammount = "Amount in " + this.currency;

        for (let key in this.openDebtGraphList) {
          let data = Number.parseFloat((this.openDebtGraphList[key].TotalAmount / 1000000000).toString()).toFixed(2).toString();


          this.openDebtData.push(data);
          this.categories.push(this.openDebtGraphList[key].Aged);
        }
        // return Number.parseFloat((value / 1000000000).toString()).toFixed(2).toString() + 'B';
      }
      else
        return value;
    }
  }

  loadCount() {

    this.coredata.getOpenDebtGraph(this.Locations).pipe(first()).subscribe(response => {
      if (response.StatusCode === 200) {
        this.count = true;
        this.openDebtData = [];
        this.categories = [];
        this.openDebtGraphList = response.response;
        let maxAmount = 0;
        for (let key in this.openDebtGraphList) {
          let totalAmount = this.openDebtGraphList[key].TotalAmount;
          if (totalAmount > maxAmount) {
            maxAmount = totalAmount;
          }
        }
        this.CurrencyToString(maxAmount);
      }
    },
      error => {
        if (error) {
          this.shareData.ErrorHandler(error);
        }
      });

  }
  ngOnDestroy() {
    this.isSubscribed.unsubscribe();
  }

  locationChange(event) {
  }


}
