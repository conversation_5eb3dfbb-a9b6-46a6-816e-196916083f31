<div id="ftSidebar">
  <div class="menu-card">
    <div class="header">
      <h3>Intelligent</h3>
      <h3>Maintenance</h3>
    </div>
    <div class="menu-item">
      <i class="fa-solid fa-home"></i>
      <a routerLink="/IM-Dashboard" routerLinkActive="active-link" [routerLinkActiveOptions]="{ exact: true }">Home</a>
    </div>
    <div class="menu-item">
      <i class="fa-solid fa-user-plus"></i>
      <a routerLink="/IM-Dashboard/IAQ" routerLinkActive="active-link" [routerLinkActiveOptions]="{ exact: true }">IAQ</a>
    </div>
    <div class="menu-item disabled">
      <i class="fa-solid fa-bolt"></i>
      <span>Utilities</span>
      <small>Coming Soon!</small>
    </div>
    <div class="menu-item disabled">
      <i class="fa-solid fa-wrench"></i>
      <span>Tasks</span>
      <small>Coming Soon!</small>
    </div>
    <div class="menu-item disabled" style="margin-bottom: 0 !important;">
      <i class="fa-solid fa-book"></i>
      <span>Reports</span>
      <small>Coming Soon!</small>
    </div>
  </div>
  <div *ngIf="currentUrl === '/IM-Dashboard'" class="toggle-card">  
    <div *ngFor="let item of toggleKeys" class="toggle-item">
      <label for="{{item.key}}">{{ item.title }}</label>
      <label class="switch" [style]="item.key === 'tuning' && !isTuningModuleActive ? 'opacity: 0.5;' : ''">
        <input
          id="{{item.key}}"
          type="checkbox"
          [checked]="!toggles[item.key]"
          (change)="onToggleChange(item.key, $event)"
          [disabled]="item.key === 'tuning' && !isTuningModuleActive"
        />
        <span class="slider"></span>
      </label>
    </div>
  </div>
  <div *ngIf="currentUrl === '/IM-Dashboard/IAQ'" class="toggle-card">
    <div *ngFor="let item of toggleKeysForIAQ" class="toggle-item">
      <label for="{{item.key}}">{{ item.key }}</label>
      <label class="switch" [style]="dataAvailable[item.key] ? '' : 'opacity: 0.5;'">
        <input
          id="{{item.key}}"
          type="checkbox"
          [checked]="!togglesForIAQ[item.key]"
          (change)="onToggleChangeForIAQ(item.key, $event)"
          [disabled]="!dataAvailable[item.key]"
        />
        <span class="slider"></span>
      </label>
    </div>
  </div>
  
</div>
