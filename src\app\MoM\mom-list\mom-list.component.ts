import { Component, OnInit, HostListener } from '@angular/core';
import { process, State } from '@progress/kendo-data-query';
import { Router, ActivatedRoute } from '@angular/router';
import { CoreDataService } from "../../Services/core-data.service";
import * as moment from 'moment';
import { Title } from '@angular/platform-browser';
import { SharedDataService } from '../../Services/shared-data.service';
import { MoMListEntities } from "./mom-list.model";
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { locationDateFilterEntity } from "./../../Services/shared-data.service";
import { first } from 'rxjs/operators';
@Component({
  selector: 'app-mom-list',
  templateUrl: './mom-list.component.html',
  styleUrls: ['./mom-list.component.scss']
})
export class MomListComponent implements OnInit {
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  gridData: Array<MoMListEntities> = [];
  DataList: any;
  startDate: string;
  endDate: string;
  clickRowSelectedData: any;
  MomGridData: any;
  subsMOM: any;
  public state: State = {
    skip: 0,
    take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public range: any;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  originalDate = new Date();
  start: Date;
  end: Date;
  locations: string;
  constructor(private route: ActivatedRoute,
    private shareData: SharedDataService
    , private titleService: Title, private router: Router, private coredata: CoreDataService)
  {
    this.allData = this.allData.bind(this);
    this.shareData.removeBacklogData();
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    if (this.locationDateFilterData === null) {
      this.range = {
        start: new Date(this.originalDate.getFullYear() - 1, this.originalDate.getMonth(), 1),
        end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), 0)
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
  }

  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;

    }

    this.subsMOM = this.shareData.getMOMData$.subscribe((obj) => {
      if (obj != undefined && obj != null) {
        if (obj == true) {
          //localStorage.setItem('location'
          let dataToString = JSON.parse(localStorage.getItem('location'));
          let startDate = new Date(dataToString.start);
          let endDate =new Date(dataToString.end);
          this.range = {
               start: new Date(startDate.getFullYear() - 1, startDate.getMonth(), 1,),
               //end: new Date(endDate.getFullYear(), endDate.getMonth(), 0)
                end:new Date(endDate)
          };
          //this.loadMoMList(this.locations, this.range);
        }
      }
    });

    this.loadMoMList(this.locations, this.range);


  }
  ngOnDestroy() {
    this.subsMOM.unsubscribe();
   }

  createCompany() {
    this.router.navigate(["/MoMDetail"]);
  }
  getDetail() {
    let id = this.clickRowSelectedData.selectedRows[0].dataItem.MeetingID;
    if (id != null && id != undefined) {
      this.router.navigate(["/MoMDetail", id]);
    }
  }
  public dataStateChange(state) {
    this.state = state;
    this.MomGridData = process(this.gridData, this.state);

  }
  followUpMeeting(data) {
    let id = data.MeetingID;
    if (id != null && id != undefined) {
      this.router.navigate(["/followUpMeeting", id, "followUpMeeting"]);
    }
  }
  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }
  isRowSelected(e: any): void {
    this.clickRowSelectedData = e;
  }
  loadMoMList(ids,range) {
    let id: string;
    if (ids === undefined) {
      id = "";
    }
    else {
      id = ids;
    }
    this.coredata.getMoMList(id, range.start, range.end).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {

        if (res.StatusCode === 200) {
          this.DataList = res.response;
          this.gridData = this.DataList
          this.gridData.map(element => {
            element.MeetingDate = this.GetFormattedDate(element.MeetingDate);
          });
          this.MomGridData = this.gridData;
        }
      }

    },
      catchError => {
        if (catchError) {
          this.shareData.ErrorHandler(catchError);
        }
      });
  }
  locationChange(event) {
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;
    const result: ExcelExportData = {
      data: process(this.DataList, state).data
    };
    return result;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
  }
}
