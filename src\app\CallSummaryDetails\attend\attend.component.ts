import { Component, OnInit, HostListener } from '@angular/core';
import { CoreDataService } from "../../Services/core-data.service";
import { SharedDataService } from "../../Services/shared-data.service";
import { process, State, FilterDescriptor, CompositeFilterDescriptor, filterBy, orderBy } from '@progress/kendo-data-query';
import { Title } from "@angular/platform-browser";
import * as moment from 'moment';
import { MCCServiceListModel, locationDateFilterEntity } from "./attend.model";
import { ActivatedRoute } from "@angular/router";
import { saveAs } from 'file-saver';
import { ExcelExportData } from '@progress/kendo-angular-excel-export';
import { GridDataResult, DataStateChangeEvent, FilterService } from '@progress/kendo-angular-grid';
import { Subscription } from 'rxjs';
import { SpinnerVisibilityService } from 'ng-http-loader';
import { first } from 'rxjs/operators';

const flatten = filter => {
  const filters = (filter || {}).filters;
  if (filters) {
    return filters.reduce((acc, curr) => acc.concat(curr.filters ? flatten(curr) : [curr]), []);
  }
  return [];
};

@Component({
  selector: 'app-attend',
  templateUrl: './attend.component.html',
  styleUrls: ['./attend.component.scss']
})
export class AttendComponent implements OnInit {
  public buttonCount: number;
  public info = true;
  public previousNext = true;
  public loading: boolean;
  public pageHeight = window.innerHeight - 233;
  locationDateFilterData: locationDateFilterEntity = new locationDateFilterEntity();
  locations: string;
  originalDate = new Date();
  startDate: string;
  endDate: string;
  start: Date;
  end: Date;
  MCCList: MCCServiceListModel[] = [];
  gridView = [];
  public range = {};
  valueChanges: Subscription;
  public state: State = {
    skip: 0,
    //take: 10,
    filter: {
      logic: 'and',
      filters: []
    }
  };
  public gridData: GridDataResult;
  //FK:
  isShow: boolean = false;
  public dataGrid: any[];
  public DataList: any = [];
  public filterGrid: any[];
  pagesize: number = 20;
  private dropdownFilter: any[] = [];
  public filter: CompositeFilterDescriptor;
  public statusCalls: any[];
  fromDateFilter: any;
  constructor(private spinner: SpinnerVisibilityService, private titleService: Title, private coredata: CoreDataService, private shareData: SharedDataService, private route: ActivatedRoute) {
    this.allData = this.allData.bind(this);
    this.shareData.removeBacklogData();
    this.locationDateFilterData = JSON.parse(localStorage.getItem('location'));
    this.fromDateFilter = this.shareData.getStartDateByUser();
    if (this.locationDateFilterData === null) {
      if(this.fromDateFilter == "null" || this.fromDateFilter == undefined || this.fromDateFilter == ""){
        this.range = {
          start: new Date(this.originalDate.getFullYear() - 3, this.originalDate.getMonth(), this.originalDate.getDate() + 1),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
      else{
        let StartingDate = new Date(this.fromDateFilter);
        this.range = {
          start: new Date(StartingDate.getFullYear(), StartingDate.getMonth(), StartingDate.getDate()),
          end: new Date(this.originalDate.getFullYear(), this.originalDate.getMonth(), this.originalDate.getDate())
        }
      }
    }
    else {
      if (!!this.locationDateFilterData.locations) {
        this.locations = this.locationDateFilterData.locations;
      }
      this.startDate = this.locationDateFilterData.start;
      this.endDate = this.locationDateFilterData.end;
      this.range = {
        start: new Date(this.startDate),
        end: new Date(this.endDate)
      }
    }
    let pageTite = this.route.snapshot.data['title'];
    this.titleService.setTitle(pageTite);
  }

  ngOnInit() {
    if (window.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (window.innerWidth < 698) {
      this.buttonCount = 1;

    }
    this.loadData(this.locations, this.range);
  }

  setDataInLocalStorage() {
    let dataToString = {
      start: this.range["start"],
      end: this.range["end"],
      locations: this.locations
    };
    localStorage.setItem('location', JSON.stringify(dataToString));
  }

  public dataStateChange(state: DataStateChangeEvent): void {
    this.state = state;
    this.gridData = process(this.MCCList, this.state);
  }

  loadData(ids, range) {
    let id: string;
    if (ids === undefined) {
      id = "";
    }
    else {
      id = ids;
    }
    this.coredata.GETMCCServiceCalls(id, range.start, range.end).pipe(first()).subscribe(res => {
      if (res != null && res != undefined) {
        if (res.StatusCode === 200) {
          this.dataGrid = res.response;
          this.dataGrid.map(element => {
            element.Date = this.GetFormattedDate(element.Date);
          });
          this.statusCalls = this.dataGrid.filter((x, idx, xs) => xs.findIndex(y => y.StatusCall === x.StatusCall) === idx);
          this.statusCalls = this.statusCalls.filter(function (el) {
            return el.StatusCall != "";
          });
          this.DataList = this.dataGrid;
          this.filterGrid = this.dataGrid;
          if(this.filterGrid.length <= this.pagesize){
            this.isShow = false;
          }
          else{
            this.isShow = true;
          }
          this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
          const next = this.MCCList.length;
          this.MCCList = [
              ...this.MCCList,
              ...this.filterGrid.slice(next, next + this.pagesize)
          ];
          this.state.filter.filters = [];
          this.state.sort = [{ dir: "desc", field: "Date" }];
          this.gridData = process(this.MCCList, this.state);
        }
      }
    },
    catchError => {
      if (catchError) {
        this.shareData.ErrorHandler(catchError);
      }
    });
  }
// FK: auto load scrolling
  loadMore(): void {
    if(this.MCCList.length >= this.filterGrid.length - this.pagesize){
      setTimeout(()=>{
        this.isShow = false;
      }, 1500);
    }
    else{
      this.isShow = true;
    }
    if(this.MCCList.length == this.filterGrid.length){
      this.loading = false;
    } else{
      this.loading = true;
      const next = this.MCCList.length;
      this.MCCList = [
          ...this.MCCList,
          ...this.filterGrid.slice(next, next + this.pagesize)
      ];
      setTimeout(()=>{
        this.loading = false;
        this.state.sort = [{ dir: "desc", field: "Date" }];
        this.gridData = process(this.MCCList, this.state);
      }, 1500);
    }
  }

  GetFormattedDate(todayTime) {
    var dt = new Date(todayTime);
    var month = dt.getMonth();
    var day = dt.getDate();
    var year = dt.getFullYear();
    return moment(new Date(year, month, day)).toDate();
  }

  DownloadSummaryReport(data) {
    if (data.ServiceCallID != null && data.ServiceCallID != undefined) {
      let payload = {
        DocumentType: "Call Summary Report",
        PropertyType: "Service Job Number",
        PropertyValue: data.ServiceCallID
      };

      this.spinner.show();
      this.coredata.getDocLinkByID(payload).subscribe(
        res => {
          if (res != null && res != undefined) {
            if (res.status === 200) {
              let result = res['_body'];
              let fileType = result.type.split('/')[1];
              let ReportName = res.headers.get('x-filename') + "." + fileType;
              this.shareData.showSuccess("Call Summary Document downloaded successfully");
              var blob = new Blob([result], { type: res.headers.get("content-type") + ';' + 'charset=utf - 8' });
              saveAs(blob, ReportName);
            }
            else if (res.status === 204) {
              this.shareData.showWarning("No Call Summary Document found for Service Call : " + data.ServiceCallID);
            }
            this.spinner.hide();
          }
        }, catchError => {
          if (catchError) {
            this.shareData.ErrorHandler(catchError);
            this.spinner.hide();
          }
        });
    }
  }

  locationChange(event) {
    if (event != undefined)
      this.locationDateFilterData = JSON.parse(event);
    if (!!this.locationDateFilterData.locations) {
      this.locations = this.locationDateFilterData.locations;
    }
    else {
      this.locations = "";
    }
    this.range["start"] = new Date(this.locationDateFilterData.start);
    this.range["end"] = new Date(this.locationDateFilterData.end);
    this.loadData(this.locations, this.range);
    this.MCCList = [];
    this.setDataInLocalStorage();
  }

  public allData(): ExcelExportData {
    let state = JSON.parse(JSON.stringify(this.state));
    state["take"] = this.DataList.total;
    state["filter"]["filters"] = this.state.filter.filters;
    state["skip"] = 0;
    const result: ExcelExportData = {
      data: process(this.DataList, state).data
    };
    return result;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth >= 698) {
      this.buttonCount = 10;
    }
    else if (event.target.innerWidth < 698) {
      this.buttonCount = 1;
    }
    //FK: set page height
    this.pageHeight = event.target.innerHeight - 233;
  }
  //FK: all filter
  public filterChange(filter: CompositeFilterDescriptor): void {
    this.MCCList = [];
    const next = this.MCCList.length;
  if(filter.filters.length >= 1){
    this.filterGrid = [];
    this.filterGrid = filterBy(this.DataList, filter);
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.MCCList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
    const next = this.MCCList.length;
      this.MCCList = [
        ...this.MCCList,
        ...this.filterGrid.slice(next, next + this.pagesize)
      ];
    this.state.sort = [{ dir: "desc", field: "Date" }];
    this.gridData = process(this.MCCList, this.state);
  }
  else {
    this.DataList = [];
    this.filterGrid = [];
    this.DataList = this.dataGrid;
    this.filterGrid = this.dataGrid;
    if(this.filterGrid.length <= this.pagesize){
      this.isShow = false;
    }
    else{
      this.isShow = true;
    }
    this.MCCList = [];
    this.filterGrid = orderBy(this.filterGrid, [{ dir: "desc", field: "Date" }]);
    const next = this.MCCList.length;
    this.MCCList = [
        ...this.MCCList,
        ...this.filterGrid.slice(next, next + this.pagesize)
    ];
    this.state.sort = [{ dir: "desc", field: "Date" }];
    this.gridData = process(this.MCCList, this.state);
  }
}
      //FK: dropdown filter
      public statusCallChange(values: any[], filterService: FilterService): void {
        filterService.filter({
          filters: values.map(value => ({
            field: 'StatusCall',
            operator: 'eq',
            value
          })),
          logic: 'or'
        });
      }
    //FK: filter
      public dropdownFilters(filter: CompositeFilterDescriptor): FilterDescriptor[] {
          this.dropdownFilter.splice(
            0, this.dropdownFilter.length,
            ...flatten(filter).map(({ value }) => value)
          );
          return this.dropdownFilter;
      }

}
